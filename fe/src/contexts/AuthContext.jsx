import React, { createContext, useContext, useState, useEffect } from "react";
import authService from "../services/authService";
import statusChecker from "../utils/statusChecker";

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Initialize auth state from storage
    const initializeAuth = () => {
      try {
        const currentUser = authService.getCurrentUser();
        const isAuth = authService.isUserAuthenticated();

        // Check if user is suspended
        if (currentUser && currentUser.status === 0) {
          authService.logout();
          setUser(null);
          setIsAuthenticated(false);
        } else {
          setUser(currentUser);
          setIsAuthenticated(isAuth);

          // Start monitoring if user is authenticated
          if (isAuth && currentUser) {
            statusChecker.startMonitoring();
          }
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (email, password) => {
    try {
      setLoading(true);
      const result = await authService.login(email, password);

      if (result.success) {
        // Check if user is suspended before setting as authenticated
        if (result.user && result.user.status === 0) {
          authService.logout();
          setUser(null);
          setIsAuthenticated(false);
          return {
            success: false,
            error: "Tài khoản của bạn đã bị đình chỉ hoạt động",
            suspended: true,
            message:
              "Tài khoản của bạn hiện đang bị đình chỉ hoạt động. Bạn không thể đăng nhập vào hệ thống. Vui lòng liên hệ quản trị viên để được hỗ trợ.",
          };
        }

        setUser(result.user);
        setIsAuthenticated(true);

        // Start monitoring user status
        statusChecker.startMonitoring();

        return result;
      } else {
        setUser(null);
        setIsAuthenticated(false);
        return result;
      }
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        error: "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau",
      };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Stop monitoring user status
      statusChecker.stopMonitoring();

      const result = await authService.logout();
      setUser(null);
      setIsAuthenticated(false);
      return result;
    } catch (error) {
      console.error("Logout error:", error);
      // Still clear local state even if API call fails
      statusChecker.stopMonitoring();
      setUser(null);
      setIsAuthenticated(false);
      return {
        success: false,
        error: "Đã xảy ra lỗi khi đăng xuất. Vui lòng thử lại",
      };
    }
  };

  const updateProfile = (profileData) => {
    try {
      const success = authService.updateProfile(profileData);
      if (success) {
        const updatedUser = authService.getCurrentUser();
        setUser(updatedUser);
      }
      return success;
    } catch (error) {
      console.error("Update profile error:", error);
      return false;
    }
  };

  const updateStatus = (newStatus) => {
    try {
      const success = authService.updateStatus(newStatus);
      if (success) {
        const updatedUser = authService.getCurrentUser();
        setUser(updatedUser);
      }
      return success;
    } catch (error) {
      console.error("Update status error:", error);
      return false;
    }
  };

  const hasRole = (role) => {
    return authService.hasRole(role);
  };

  const hasAnyRole = (roles) => {
    return authService.hasAnyRole(roles);
  };

  const getRedirectPath = () => {
    return authService.getRedirectPath();
  };

  const value = {
    // State
    user,
    loading,
    isAuthenticated,

    // Methods
    login,
    logout,
    updateProfile,
    updateStatus,
    hasRole,
    hasAnyRole,
    getRedirectPath,

    // Computed values
    userRole: user?.role || null,
    userStatus: user?.status || null,
    isFirstLogin: user?.isFirstLogin || false,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
