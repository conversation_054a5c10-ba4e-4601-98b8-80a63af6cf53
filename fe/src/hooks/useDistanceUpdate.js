import { useState } from 'react';
import userInfoService from '../services/userInfoService';

/**
 * Custom hook for updating user distance information
 * @returns {Object} Hook functions and state
 */
export const useDistanceUpdate = () => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateError, setUpdateError] = useState(null);
  const [updateSuccess, setUpdateSuccess] = useState(false);

  /**
   * Update distance for current user
   * @param {number} distance - Distance in kilometers
   * @returns {Promise<Object>} Update result
   */
  const updateDistance = async (distance) => {
    if (!distance || distance < 0) {
      setUpdateError('Khoảng cách không hợp lệ');
      return { success: false, error: 'Khoảng cách không hợp lệ' };
    }

    setIsUpdating(true);
    setUpdateError(null);
    setUpdateSuccess(false);

    try {
      const result = await userInfoService.saveDistanceForCurrentUser(distance);

      if (result.success) {
        setUpdateSuccess(true);
        console.log('Distance updated successfully:', distance);
      } else {
        setUpdateError(result.error);
      }

      return result;
    } catch (error) {
      const errorMessage = error.message || 'Có lỗi xảy ra khi cập nhật khoảng cách';
      setUpdateError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsUpdating(false);
    }
  };

  /**
   * Update distance for specific user
   * @param {string|number} userId - User ID
   * @param {number} distance - Distance in kilometers
   * @returns {Promise<Object>} Update result
   */
  const updateDistanceForUser = async (userId, distance) => {
    if (!userId) {
      setUpdateError('ID người dùng không hợp lệ');
      return { success: false, error: 'ID người dùng không hợp lệ' };
    }

    if (!distance || distance < 0) {
      setUpdateError('Khoảng cách không hợp lệ');
      return { success: false, error: 'Khoảng cách không hợp lệ' };
    }

    setIsUpdating(true);
    setUpdateError(null);
    setUpdateSuccess(false);

    try {
      const result = await userInfoService.updateUserDistance(userId, distance);
      setUpdateSuccess(true);
      console.log(`Distance updated successfully for user ${userId}:`, distance);

      return {
        success: true,
        data: result,
        message: 'Đã cập nhật khoảng cách thành công'
      };
    } catch (error) {
      const errorMessage = error.message || 'Có lỗi xảy ra khi cập nhật khoảng cách';
      setUpdateError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsUpdating(false);
    }
  };

  /**
   * Clear error and success states
   */
  const clearStates = () => {
    setUpdateError(null);
    setUpdateSuccess(false);
  };

  return {
    // Functions
    updateDistance,
    updateDistanceForUser,
    clearStates,

    // States
    isUpdating,
    updateError,
    updateSuccess
  };
};

export default useDistanceUpdate;
