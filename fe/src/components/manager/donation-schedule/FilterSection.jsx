import { Card, Row, Col, Select } from "antd";

const { Option } = Select;

/**
 * Reusable filter section component
 */
const FilterSection = ({
  filters,
  onFilterChange,
  sortValue,
  onSortChange,
  filterOptions,
  showStatusFilter = false,
}) => {
  return (
    <Card className="filters-card">
      <Row gutter={[16, 16]}>
        {/* Status Filter (only for process tab) */}
        {showStatusFilter && filterOptions.status && (
          <Col xs={24} sm={12} md={8}>
            <div className="filter-group">
              <label>{filterOptions.status.label}</label>
              <Select
                value={filters.status}
                onChange={(value) =>
                  onFilterChange((prev) => ({ ...prev, status: value }))
                }
                style={{ width: "100%" }}
              >
                {filterOptions.status.options.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
        )}

        {/* Blood Type Filter */}
        {filterOptions.bloodType && (
          <Col xs={24} sm={12} md={8}>
            <div className="filter-group">
              <label>{filterOptions.bloodType.label}</label>
              <Select
                value={filters.bloodType}
                onChange={(value) =>
                  onFilterChange((prev) => ({
                    ...prev,
                    bloodType: value,
                  }))
                }
                style={{ width: "100%" }}
              >
                {filterOptions.bloodType.options.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
        )}

        {/* Sort Filter */}
        {filterOptions.sort && (
          <Col xs={24} sm={12} md={8}>
            <div className="filter-group">
              <label>{filterOptions.sort.label}</label>
              <Select
                value={sortValue}
                onChange={onSortChange}
                style={{ width: "100%" }}
              >
                {filterOptions.sort.options.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
        )}
      </Row>
    </Card>
  );
};

export default FilterSection;
