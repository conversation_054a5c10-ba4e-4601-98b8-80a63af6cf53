import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Container,
  Row,
  Col,
  Card,
  Form,
  <PERSON>ton,
  ProgressBar,
  <PERSON><PERSON>,
  Badge,
  Spinner,
} from "react-bootstrap";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Fa<PERSON>ser,
  FaStethoscope,
  FaFileAlt,
  FaCheckCircle,
  FaArrowLeft,
  FaArrowRight,
  FaSpinner,
  FaHome,
  FaExclamationTriangle,
  FaRedo,
} from "react-icons/fa";
import MemberNavbar from "../../components/member/MemberNavbar";

import authService from "../../services/authService";
import userInfoService from "../../services/userInfoService";
import NotificationService from "../../services/notificationService";
import bloodRequestService from "../../services/bloodRequestService";
import {
  formatBloodRequestData,
  formatBloodRequestResponse,
} from "../../utils/bloodRequestHelpers";
import { REQUEST_STATUS, BLOOD_TYPES } from "../../constants/systemConstants";
import { BLOOD_COMPONENT_MAP } from "../../constants/bloodInventoryConstants";
import UnifiedModal from "../../components/member/UnifiedModal";
import ProfileIncompleteModal from "../../components/member/ProfileIncompleteModal";
import "../../styles/pages/BloodRequestFormPageNew.scss";

const BloodRequestFormPage = () => {
  const navigate = useNavigate();
  const currentUser = authService.getCurrentUser();

  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [formData, setFormData] = useState({
    bloodType: "",
    componentId: "",
    quantity: "",
    unit: "ml",
    patientName: "",
    patientAge: "",
    patientGender: "",
    patientRelation: "",
    patientRelationOther: "",
    medicalCondition: "",
    hospitalName: "",
    doctorName: "",
    doctorPhone: "",
    medicalReports: "",
    additionalNotes: "",
  });
  const [validated, setValidated] = useState(false);
  const [attemptedSubmit, setAttemptedSubmit] = useState(false);
  const [touchedFields, setTouchedFields] = useState({});

  // Pending request check states
  const [isCheckingPending, setIsCheckingPending] = useState(true);
  const [pendingRequest, setPendingRequest] = useState(null);
  const [canCreateRequest, setCanCreateRequest] = useState(false);

  // Profile completeness check states
  const [isCheckingProfile, setIsCheckingProfile] = useState(true);
  const [profileCheckResult, setProfileCheckResult] = useState(null);
  const [profileComplete, setProfileComplete] = useState(false);

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showProfileIncompleteModal, setShowProfileIncompleteModal] =
    useState(false);
  const [modalType, setModalType] = useState("profile-incomplete");
  const [modalContext, setModalContext] = useState("request");

  // Reset validation when step changes
  useEffect(() => {
    setValidated(false);
    setAttemptedSubmit(false);
  }, [currentStep]);

  // Check profile completeness when component mounts
  useEffect(() => {
    const checkProfileCompleteness = async () => {
      if (!currentUser?.id) {
        console.error("No current user found");
        setIsCheckingProfile(false);
        return;
      }

      try {
        setIsCheckingProfile(true);
        console.log("🔍 Checking profile completeness...");

        const userInfo = await userInfoService.getUserInfo(currentUser.id);
        const profileCheck = userInfoService.checkProfileCompleteness(userInfo);

        console.log("📋 Profile check result:", profileCheck);

        if (profileCheck.isComplete) {
          setProfileComplete(true);
        } else {
          // Profile incomplete - show modal and block form
          setProfileCheckResult(profileCheck);
          setProfileComplete(false);
          setShowProfileIncompleteModal(true);
        }
      } catch (error) {
        console.error("Error checking profile completeness:", error);
        // If there's an error, allow form access but log error
        setProfileComplete(true);
      } finally {
        setIsCheckingProfile(false);
      }
    };

    checkProfileCompleteness();
  }, [currentUser]);

  // Check for pending requests when component mounts (only if profile is complete)
  useEffect(() => {
    if (!profileComplete) return;

    const checkPendingRequests = async () => {
      if (!currentUser?.id) {
        console.error("No current user found");
        setIsCheckingPending(false);
        return;
      }

      try {
        setIsCheckingPending(true);
        console.log("🔍 Checking for pending blood requests...");

        const result = await bloodRequestService.checkUserPendingRequest(
          currentUser.id
        );
        console.log("📋 Pending check result:", result);

        if (result.success) {
          if (result.hasPendingRequest) {
            // User has pending request - show modal and block form
            setPendingRequest(result.pendingRequest);
            setCanCreateRequest(false);
            setModalType("blood-request-status");
            setShowModal(true);
          } else {
            // No pending request - allow form creation
            setCanCreateRequest(true);
          }
        } else {
          // Error checking - allow form creation but log error
          console.error("Error checking pending requests:", result.error);
          setCanCreateRequest(true);
        }
      } catch (error) {
        console.error("Exception checking pending requests:", error);
        setCanCreateRequest(true);
      } finally {
        setIsCheckingPending(false);
      }
    };

    checkPendingRequests();
  }, [currentUser?.id, profileComplete]);

  const steps = [
    {
      title: "Thông tin máu",
      icon: FaHeart,
      description: "Nhóm máu và số lượng cần thiết",
    },
    {
      title: "Thông tin bệnh nhân",
      icon: FaUser,
      description: "Chi tiết về bệnh nhân",
    },
    {
      title: "Thông tin y tế",
      icon: FaStethoscope,
      description: "Bệnh viện và bác sĩ điều trị",
    },
  ];

  const handleInputChange = (field, value) => {
    // Mark field as touched
    setTouchedFields((prev) => ({
      ...prev,
      [field]: true,
    }));

    // Special handling for doctor phone - only allow numbers
    if (field === "doctorPhone") {
      const numbersOnly = value.replace(/\D/g, "");
      setFormData((prev) => ({
        ...prev,
        [field]: numbersOnly,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // Helper function to determine if field should show validation error
  const shouldShowFieldError = (fieldName, condition) => {
    return (validated || touchedFields[fieldName]) && condition;
  };

  const validateCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return formData.bloodType && formData.componentId && formData.quantity;
      case 1:
        const hasPatientRelation = formData.patientRelation;
        const hasPatientRelationOther =
          formData.patientRelation === "other"
            ? formData.patientRelationOther.trim()
            : true;
        const hasValidAge = formData.patientAge && formData.patientAge > 0;
        return (
          formData.patientName &&
          hasValidAge &&
          formData.patientGender &&
          formData.medicalCondition &&
          hasPatientRelation &&
          hasPatientRelationOther
        );
      case 2:
        const hasValidPhone =
          formData.doctorPhone && formData.doctorPhone.length === 10;
        return (
          formData.hospitalName &&
          formData.doctorName &&
          hasValidPhone &&
          formData.medicalReports
        );
      default:
        return false;
    }
  };

  const handleNext = () => {
    setAttemptedSubmit(true); // Mark that user attempted to proceed
    if (validateCurrentStep()) {
      setValidated(false);
      setAttemptedSubmit(false);
      setCurrentStep((prev) => prev + 1);
    } else {
      setValidated(true);
    }
  };

  const handlePrev = () => {
    setValidated(() => false); // Force reset validation first
    setTouchedFields({}); // Reset touched fields
    setCurrentStep((prev) => prev - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    setAttemptedSubmit(true); // Mark that user attempted to submit
    if (!validateCurrentStep()) {
      setValidated(true);
      return;
    }

    setLoading(true);

    try {
      // Prepare blood request data for API using helper function
      const bloodRequestData = formatBloodRequestData(formData, currentUser);

      // Call API to create blood request
      const apiResponse = await bloodRequestService.createBloodRequest(
        bloodRequestData
      );

      if (!apiResponse.success) {
        // More detailed error message based on status
        let errorMessage =
          apiResponse.error || "Có lỗi xảy ra khi tạo yêu cầu máu";

        if (apiResponse.status === 400) {
          errorMessage =
            "Dữ liệu gửi lên không hợp lệ. Vui lòng kiểm tra lại thông tin.";
        } else if (apiResponse.status === 401) {
          errorMessage = "Bạn cần đăng nhập để thực hiện chức năng này.";
        } else if (apiResponse.status === 500) {
          errorMessage =
            "Lỗi server. Vui lòng thử lại sau hoặc liên hệ hỗ trợ.";
        } else if (apiResponse.status === 0 || !apiResponse.status) {
          errorMessage =
            "Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.";
        }

        throw new Error(errorMessage);
      }

      const bloodRequest = apiResponse.data;

      // Send notification to blood department doctors
      await NotificationService.createNotification({
        userId: "blood_department_doctors",
        type: "new_blood_request",
        title: "📋 Yêu cầu máu mới",
        message: `Yêu cầu máu ${formData.bloodType} từ ${currentUser.name}`,
        data: {
          requestId: bloodRequest.id,
          bloodType: formData.bloodType,
          quantity: formData.quantity,
          requesterName: currentUser.name,
        },
      });

      setSubmissionResult({
        status: "success",
        message: "GỬI YÊU CẦU THÀNH CÔNG",
        description:
          "Yêu cầu máu của bạn đã được gửi đến bác sĩ khoa Huyết học. Bạn sẽ nhận được phản hồi sớm.",
        requestId: bloodRequest.id || bloodRequest.requestId,
        data: {
          bloodType: formData.bloodType,
          quantity: formData.quantity,
          unit: formData.unit || "ml",
          patientName: formData.patientName,
        },
      });
    } catch (error) {
      console.error("Error submitting blood request:", error);
      setSubmissionResult({
        status: "error",
        message: "GỬI YÊU CẦU THẤT BẠI",
        description:
          error.message ||
          "Có lỗi xảy ra khi gửi yêu cầu. Vui lòng thử lại sau hoặc liên hệ hỗ trợ.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Success/Error Result Component
  if (submissionResult) {
    return (
      <div className="blood-request-form-page">
        <MemberNavbar />
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={8}>
              <Card
                className={`shadow-lg border-0 ${
                  submissionResult.status === "success"
                    ? "success-card"
                    : "error-card"
                }`}
              >
                <Card.Body className="text-center p-5">
                  {submissionResult.status === "success" ? (
                    <FaCheckCircle className="result-icon text-success" />
                  ) : (
                    <FaExclamationTriangle className="result-icon text-danger" />
                  )}

                  <h2 className="result-title">{submissionResult.message}</h2>

                  <p className="result-description">
                    {submissionResult.description}
                  </p>

                  {submissionResult.status === "success" &&
                    submissionResult.data && (
                      <div className="request-summary">
                        <div className="summary-title">
                          <FaFileAlt className="me-2" />
                          Thông tin yêu cầu
                        </div>
                        <div className="summary-item">
                          <span className="label">Mã yêu cầu:</span>
                          <span className="value request-id">
                            #{submissionResult.requestId}
                          </span>
                        </div>
                        <div className="summary-item">
                          <span className="label">Nhóm máu:</span>
                          <span className="value blood-type">
                            {submissionResult.data.bloodType}
                          </span>
                        </div>
                        <div className="summary-item">
                          <span className="label">Số lượng:</span>
                          <span className="value">
                            {submissionResult.data.quantity}{" "}
                            {submissionResult.data.unit}
                          </span>
                        </div>
                        <div className="summary-item">
                          <span className="label">Bệnh nhân:</span>
                          <span className="value">
                            {submissionResult.data.patientName}
                          </span>
                        </div>
                      </div>
                    )}

                  <div className="d-flex gap-4 justify-content-center mt-4">
                    <Button
                      variant="success"
                      size="lg"
                      className="home-button px-6 py-4"
                      onClick={() => navigate("/member")}
                    >
                      <FaHome className="me-2" />
                      Về trang chủ
                    </Button>
                    {submissionResult.status === "error" && (
                      <Button
                        variant="outline-primary"
                        size="lg"
                        className="retry-button px-6 py-4"
                        onClick={() => setSubmissionResult(null)}
                      >
                        <FaRedo className="me-2" />
                        Thử lại
                      </Button>
                    )}
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  // Step Progress Component
  const StepProgress = () => (
    <div className="progress-container">
      <div className="step-indicator">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;

          return (
            <div
              key={index}
              className={`step ${isActive ? "active" : ""} ${
                isCompleted ? "completed" : ""
              }`}
            >
              <div
                className={`step-icon ${isActive ? "active" : ""} ${
                  isCompleted ? "completed" : ""
                }`}
              >
                <Icon />
              </div>
              <div
                className={`step-title ${isActive ? "active" : ""} ${
                  isCompleted ? "completed" : ""
                }`}
              >
                {step.title}
              </div>
            </div>
          );
        })}
      </div>
      <ProgressBar
        now={(currentStep / (steps.length - 1)) * 100}
        variant={currentStep === steps.length - 1 ? "success" : "primary"}
        style={{ height: "8px", borderRadius: "4px" }}
      />
    </div>
  );

  // Loading screen while checking pending requests
  if (isCheckingPending) {
    return (
      <div className="blood-request-form-page">
        <MemberNavbar />
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={6} className="text-center">
              <Card className="p-5">
                <Spinner
                  animation="border"
                  variant="primary"
                  className="mb-3"
                />
                <h4>Đang kiểm tra trạng thái đơn đăng ký...</h4>
                <p className="text-muted">Vui lòng chờ trong giây lát</p>
              </Card>
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  // Block access if profile is incomplete
  if (!profileComplete && !isCheckingProfile) {
    return (
      <div className="blood-request-form-page">
        <MemberNavbar />
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={8}>
              <Alert variant="info" className="text-center p-4">
                <FaExclamationTriangle size={48} className="mb-3 text-info" />
                <h4>Cần cập nhật hồ sơ cá nhân</h4>
                <p>
                  Để đảm bảo quy trình đăng ký nhận máu diễn ra thuận lợi, bạn
                  cần cập nhật đầy đủ thông tin cá nhân trước khi tiếp tục.
                </p>
                <div className="d-flex gap-3 justify-content-center mt-4">
                  <Button variant="primary" onClick={() => setShowModal(true)}>
                    Xem thông tin cần bổ sung
                  </Button>
                  <Button
                    variant="outline-primary"
                    onClick={() => navigate("/member/profile")}
                  >
                    Cập nhật hồ sơ
                  </Button>
                  <Button
                    variant="outline-secondary"
                    onClick={() => navigate("/member")}
                  >
                    <FaHome className="me-2" />
                    Về trang chủ
                  </Button>
                </div>
              </Alert>
            </Col>
          </Row>
        </Container>

        {/* Unified Modal */}
        <UnifiedModal
          visible={showModal}
          onClose={() => setShowModal(false)}
          type={modalType}
          context={modalContext}
          profileCheckResult={profileCheckResult}
          pendingRequest={pendingRequest}
          onGoToProfile={() => {
            setShowModal(false);
            navigate("/member/profile");
          }}
          onViewHistory={() => {
            setShowModal(false);
            navigate("/member/activity-history");
          }}
        />
      </div>
    );
  }

  // Block access if user has pending request
  if (!canCreateRequest && pendingRequest) {
    return (
      <div className="blood-request-form-page">
        <MemberNavbar />
        <Container className="py-5">
          <Row className="justify-content-center">
            <Col lg={8}>
              <Alert variant="warning" className="text-center p-4">
                <FaExclamationTriangle
                  size={48}
                  className="mb-3 text-warning"
                />
                <h4>Không thể tạo đơn đăng ký mới</h4>
                <p>
                  Bạn đã có đơn đăng ký nhận máu đang chờ xử lý. Để tránh spam,
                  hệ thống chỉ cho phép một đơn đăng ký tại một thời điểm.
                </p>
                <div className="d-flex gap-3 justify-content-center mt-4">
                  <Button variant="primary" onClick={() => setShowModal(true)}>
                    Xem chi tiết đơn hiện tại
                  </Button>
                  <Button
                    variant="outline-secondary"
                    onClick={() => navigate("/member/activity-history")}
                  >
                    Xem lịch sử hoạt động
                  </Button>
                  <Button
                    variant="outline-primary"
                    onClick={() => navigate("/member")}
                  >
                    <FaHome className="me-2" />
                    Về trang chủ
                  </Button>
                </div>
              </Alert>
            </Col>
          </Row>
        </Container>
      </div>
    );
  }

  return (
    <div className="blood-request-form-page">
      <MemberNavbar />

      <Container className="py-4">
        <Row className="justify-content-center">
          <Col lg={8} xl={7}>
            <div className="page-header">
              <div className="header-content">
                <div className="header-icon">
                  <FaHeart />
                </div>
                <div className="header-text">
                  <h2 className="header-title">Yêu cầu máu</h2>
                  <p className="header-subtitle">
                    Điền thông tin để yêu cầu máu từ ngân hàng máu
                  </p>
                </div>
              </div>
            </div>

            <StepProgress />

            <Card className="form-card">
              <Card.Header>
                <h5>
                  {React.createElement(steps[currentStep].icon, {
                    className: "me-2",
                  })}
                  {steps[currentStep].title}
                </h5>
                <small className="text-light">
                  {steps[currentStep].description}
                </small>
              </Card.Header>
              <Card.Body>
                <Form noValidate onSubmit={handleSubmit}>
                  {/* Step 0: Blood Information */}
                  {currentStep === 0 && (
                    <div className="step-content">
                      <div className="form-row">
                        <div className="form-col form-col-6">
                          <Form.Group>
                            <Form.Label>
                              Nhóm máu <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Select
                              value={formData.bloodType}
                              onChange={(e) =>
                                handleInputChange("bloodType", e.target.value)
                              }
                              className={
                                validated && !formData.bloodType
                                  ? "is-invalid"
                                  : ""
                              }
                            >
                              <option value="">Chọn nhóm máu</option>
                              <option value="O+">O+</option>
                              <option value="O-">O-</option>
                              <option value="A+">A+</option>
                              <option value="A-">A-</option>
                              <option value="B+">B+</option>
                              <option value="B-">B-</option>
                              <option value="AB+">AB+</option>
                              <option value="AB-">AB-</option>
                            </Form.Select>

                            {validated && !formData.bloodType && (
                              <div className="invalid-feedback d-block">
                                Vui lòng chọn nhóm máu
                              </div>
                            )}
                          </Form.Group>
                        </div>
                        <div className="form-col form-col-4">
                          <Form.Group>
                            <Form.Label>
                              Thành phần máu{" "}
                              <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Select
                              value={formData.componentId}
                              onChange={(e) =>
                                handleInputChange("componentId", e.target.value)
                              }
                              className={
                                validated && !formData.componentId
                                  ? "is-invalid"
                                  : ""
                              }
                            >
                              <option value="">Chọn thành phần máu</option>
                              {Object.entries(BLOOD_COMPONENT_MAP).map(
                                ([id, name]) => (
                                  <option key={id} value={id}>
                                    {name}
                                  </option>
                                )
                              )}
                            </Form.Select>
                            {validated && !formData.componentId && (
                              <div className="invalid-feedback d-block">
                                Vui lòng chọn thành phần máu
                              </div>
                            )}
                          </Form.Group>
                        </div>
                        <div className="form-col form-col-3">
                          <Form.Group>
                            <Form.Label>
                              Số lượng <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Control
                              type="number"
                              value={formData.quantity}
                              onChange={(e) =>
                                handleInputChange("quantity", e.target.value)
                              }
                              placeholder="Nhập số lượng"
                              min="1"
                              className={
                                validated && !formData.quantity
                                  ? "is-invalid"
                                  : ""
                              }
                            />
                            {validated && !formData.quantity && (
                              <div className="invalid-feedback d-block">
                                Vui lòng nhập số lượng
                              </div>
                            )}
                          </Form.Group>
                        </div>
                        <div className="form-col form-col-2">
                          <Form.Group>
                            <Form.Label>Đơn vị</Form.Label>
                            <Form.Control
                              type="text"
                              value="ml"
                              disabled
                              className="unit-display"
                            />
                          </Form.Group>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Step 1: Patient Information */}
                  {currentStep === 1 && (
                    <div className="step-content">
                      <div className="form-row">
                        <div className="form-col form-col-6">
                          <Form.Group>
                            <Form.Label>
                              Tên bệnh nhân{" "}
                              <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              value={formData.patientName}
                              onChange={(e) =>
                                handleInputChange("patientName", e.target.value)
                              }
                              placeholder="Nhập tên bệnh nhân"
                              className={
                                shouldShowFieldError(
                                  "patientName",
                                  !formData.patientName
                                )
                                  ? "is-invalid"
                                  : ""
                              }
                            />
                            {shouldShowFieldError(
                              "patientName",
                              !formData.patientName
                            ) && (
                              <div className="invalid-feedback d-block">
                                Vui lòng nhập tên bệnh nhân
                              </div>
                            )}
                          </Form.Group>
                        </div>
                        <div className="form-col form-col-3">
                          <Form.Group>
                            <Form.Label>
                              Tuổi <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Control
                              type="number"
                              value={formData.patientAge}
                              onChange={(e) =>
                                handleInputChange("patientAge", e.target.value)
                              }
                              placeholder="Tuổi bệnh nhân"
                              min="1"
                              max="150"
                              className={
                                shouldShowFieldError(
                                  "patientAge",
                                  !formData.patientAge ||
                                    formData.patientAge <= 0
                                )
                                  ? "is-invalid"
                                  : ""
                              }
                            />
                            {shouldShowFieldError(
                              "patientAge",
                              !formData.patientAge || formData.patientAge <= 0
                            ) && (
                              <div className="invalid-feedback d-block">
                                {!formData.patientAge
                                  ? "Vui lòng nhập tuổi"
                                  : "Tuổi phải lớn hơn 0"}
                              </div>
                            )}
                          </Form.Group>
                        </div>
                        <div className="form-col form-col-3">
                          <Form.Group>
                            <Form.Label>
                              Giới tính <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Select
                              value={formData.patientGender}
                              onChange={(e) =>
                                handleInputChange(
                                  "patientGender",
                                  e.target.value
                                )
                              }
                              className={
                                validated && !formData.patientGender
                                  ? "is-invalid"
                                  : ""
                              }
                            >
                              <option value="">Chọn giới tính</option>
                              <option value="male">Nam</option>
                              <option value="female">Nữ</option>
                              <option value="other">Khác</option>
                            </Form.Select>
                            {validated && !formData.patientGender && (
                              <div className="invalid-feedback d-block">
                                Vui lòng chọn giới tính
                              </div>
                            )}
                          </Form.Group>
                        </div>
                      </div>

                      <Form.Group className="mb-4">
                        <Form.Label>Mối quan hệ với bệnh nhân</Form.Label>
                        <Form.Select
                          value={formData.patientRelation}
                          onChange={(e) =>
                            handleInputChange("patientRelation", e.target.value)
                          }
                          className={
                            validated && !formData.patientRelation
                              ? "is-invalid"
                              : ""
                          }
                        >
                          <option value="">Chọn mối quan hệ</option>
                          <option value="self">Chính bản thân tôi</option>
                          <option value="family">Gia đình</option>
                          <option value="friend">Bạn bè</option>
                          <option value="doctor">Bác sĩ phụ trách</option>
                          <option value="other">Khác</option>
                        </Form.Select>
                        {validated && !formData.patientRelation && (
                          <div className="invalid-feedback d-block">
                            Vui lòng chọn mối quan hệ với bệnh nhân.
                          </div>
                        )}
                      </Form.Group>

                      {formData.patientRelation === "other" && (
                        <Form.Group className="mb-4">
                          <Form.Label>
                            Mối quan hệ khác{" "}
                            <span className="text-danger">*</span>
                          </Form.Label>
                          <Form.Control
                            type="text"
                            placeholder="Nhập mối quan hệ cụ thể"
                            value={formData.patientRelationOther}
                            onChange={(e) =>
                              handleInputChange(
                                "patientRelationOther",
                                e.target.value
                              )
                            }
                            className={
                              validated &&
                              formData.patientRelation === "other" &&
                              !formData.patientRelationOther.trim()
                                ? "is-invalid"
                                : ""
                            }
                          />
                          {validated &&
                            formData.patientRelation === "other" &&
                            !formData.patientRelationOther.trim() && (
                              <div className="invalid-feedback d-block">
                                Vui lòng nhập mối quan hệ cụ thể.
                              </div>
                            )}
                        </Form.Group>
                      )}

                      <Form.Group className="mb-3 medical-condition-group">
                        <Form.Label>
                          Tình trạng bệnh lý{" "}
                          <span className="text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                          as="textarea"
                          rows={4}
                          value={formData.medicalCondition}
                          onChange={(e) =>
                            handleInputChange(
                              "medicalCondition",
                              e.target.value
                            )
                          }
                          placeholder="Mô tả chi tiết tình trạng bệnh lý cần truyền máu, bao gồm: chẩn đoán, triệu chứng, mức độ nghiêm trọng, thời gian cần truyền máu..."
                          className={`medical-condition-textarea ${
                            shouldShowFieldError(
                              "medicalCondition",
                              !formData.medicalCondition
                            )
                              ? "is-invalid"
                              : ""
                          }`}
                        />
                        {shouldShowFieldError(
                          "medicalCondition",
                          !formData.medicalCondition
                        ) && (
                          <div className="invalid-feedback d-block">
                            Vui lòng mô tả tình trạng bệnh lý
                          </div>
                        )}
                      </Form.Group>
                    </div>
                  )}

                  {/* Step 2: Medical Information */}
                  {currentStep === 2 && (
                    <div className="step-content">
                      <div className="form-row">
                        <div className="form-col form-col-6">
                          <Form.Group>
                            <Form.Label>
                              Cơ sở y tế đang khám chữa bệnh{" "}
                              <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              value={formData.hospitalName}
                              onChange={(e) =>
                                handleInputChange(
                                  "hospitalName",
                                  e.target.value
                                )
                              }
                              placeholder="Tên cơ sở y tế đang khám chữa bệnh"
                              className={
                                validated &&
                                attemptedSubmit &&
                                !formData.hospitalName
                                  ? "is-invalid"
                                  : ""
                              }
                            />
                            {validated &&
                              attemptedSubmit &&
                              !formData.hospitalName && (
                                <div className="invalid-feedback d-block">
                                  Vui lòng nhập cơ sở y tế đang khám chữa bệnh
                                </div>
                              )}
                          </Form.Group>
                        </div>
                        <div className="form-col form-col-6">
                          <Form.Group>
                            <Form.Label>
                              Tên bác sĩ điều trị{" "}
                              <span className="text-danger">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              value={formData.doctorName}
                              onChange={(e) =>
                                handleInputChange("doctorName", e.target.value)
                              }
                              placeholder="Tên bác sĩ"
                              className={
                                validated &&
                                attemptedSubmit &&
                                !formData.doctorName
                                  ? "is-invalid"
                                  : ""
                              }
                            />
                            {validated &&
                              attemptedSubmit &&
                              !formData.doctorName && (
                                <div
                                  className="invalid-feedback d-block"
                                  style={{
                                    display:
                                      validated && !formData.doctorName
                                        ? "block"
                                        : "none",
                                  }}
                                >
                                  Vui lòng nhập tên bác sĩ điều trị
                                </div>
                              )}
                          </Form.Group>
                        </div>
                      </div>

                      <Form.Group className="mb-4">
                        <Form.Label>
                          Số điện thoại bác sĩ{" "}
                          <span className="text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                          type="tel"
                          value={formData.doctorPhone}
                          onChange={(e) =>
                            handleInputChange("doctorPhone", e.target.value)
                          }
                          placeholder="Số điện thoại bác sĩ (10 số)"
                          maxLength="10"
                          className={
                            validated &&
                            attemptedSubmit &&
                            (!formData.doctorPhone ||
                              formData.doctorPhone.length !== 10)
                              ? "is-invalid"
                              : ""
                          }
                        />
                        {validated &&
                          attemptedSubmit &&
                          (!formData.doctorPhone ||
                            formData.doctorPhone.length !== 10) && (
                            <div className="invalid-feedback d-block">
                              {!formData.doctorPhone
                                ? "Vui lòng nhập số điện thoại bác sĩ"
                                : "Số điện thoại phải đủ 10 số"}
                            </div>
                          )}
                      </Form.Group>

                      <Form.Group className="mb-4">
                        <Form.Label>
                          Báo cáo y tế/Chẩn đoán{" "}
                          <span className="text-danger">*</span>
                        </Form.Label>
                        <Form.Control
                          as="textarea"
                          rows={4}
                          value={formData.medicalReports}
                          onChange={(e) =>
                            handleInputChange("medicalReports", e.target.value)
                          }
                          placeholder="Thông tin về chẩn đoán, kết quả xét nghiệm..."
                          className={
                            validated &&
                            attemptedSubmit &&
                            !formData.medicalReports
                              ? "is-invalid"
                              : ""
                          }
                        />
                        {validated &&
                          attemptedSubmit &&
                          !formData.medicalReports && (
                            <div className="invalid-feedback d-block">
                              Vui lòng nhập báo cáo y tế/chẩn đoán
                            </div>
                          )}
                      </Form.Group>
                    </div>
                  )}

                  {/* Navigation Buttons */}
                  <div className="navigation-buttons">
                    <div>
                      {currentStep > 0 && (
                        <Button
                          variant="outline-secondary"
                          onClick={handlePrev}
                          disabled={loading}
                        >
                          <FaArrowLeft className="me-2" />
                          Quay lại
                        </Button>
                      )}
                    </div>

                    <div>
                      {currentStep < steps.length - 1 ? (
                        <Button
                          variant="primary"
                          onClick={handleNext}
                          disabled={loading}
                        >
                          Tiếp theo
                          <FaArrowRight className="ms-2" />
                        </Button>
                      ) : (
                        <Button
                          variant="success"
                          type="submit"
                          disabled={loading}
                        >
                          {loading ? (
                            <>
                              <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                className="me-2"
                              />
                              Đang gửi...
                            </>
                          ) : (
                            <>
                              <FaCheckCircle className="me-2" />
                              Gửi yêu cầu máu
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Profile Incomplete Modal */}
      <ProfileIncompleteModal
        visible={showProfileIncompleteModal}
        onClose={() => setShowProfileIncompleteModal(false)}
        profileCheckResult={profileCheckResult}
        onGoToProfile={() => {
          setShowProfileIncompleteModal(false);
          navigate("/member/profile");
        }}
      />
    </div>
  );
};

export default BloodRequestFormPage;
