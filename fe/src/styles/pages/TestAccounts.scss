.test-accounts {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;

  .accounts-container {
    max-width: 1400px;
    margin: 0 auto;
  }

  .accounts-header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;

    h1 {
      font-size: 2.5rem;
      margin: 0 0 1rem 0;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    p {
      font-size: 1.2rem;
      margin: 0 0 1rem 0;
      opacity: 0.9;
    }

    .error-message {
      background: #dc3545;
      color: white;
      padding: 1rem;
      border-radius: 8px;
      margin: 1rem auto;
      max-width: 500px;
      font-weight: 600;
    }
  }

  .accounts-sections {
    display: flex;
    flex-direction: column;
    gap: 3rem;
  }

  .account-section {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    h2 {
      margin: 0 0 0.5rem 0;
      font-size: 1.8rem;
      color: #333;
      font-weight: 700;
    }

    .section-description {
      margin: 0 0 2rem 0;
      color: #666;
      font-size: 1rem;
      line-height: 1.5;
    }

    .accounts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 1.5rem;
    }

    .account-card {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      padding: 1.5rem;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #007bff;
      }

      .account-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        h3 {
          margin: 0;
          font-size: 1.2rem;
          color: #333;
          font-weight: 600;
        }

        .role-badge {
          padding: 0.25rem 0.75rem;
          border-radius: 15px;
          font-size: 0.8rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.member {
            background: #e3f2fd;
            color: #1976d2;
          }

          &.doctor {
            background: #e8f5e8;
            color: #2e7d32;
          }

          &.manager {
            background: #fff3e0;
            color: #f57c00;
          }

          &.admin {
            background: #ffebee;
            color: #d32f2f;
          }
        }
      }

      .account-details {
        margin-bottom: 1.5rem;

        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0;
          border-bottom: 1px solid #eee;

          &:last-child {
            border-bottom: none;
          }

          .label {
            font-weight: 600;
            color: #555;
            font-size: 0.9rem;
          }

          span:not(.label) {
            color: #333;
            font-size: 0.9rem;
            text-align: right;
            max-width: 60%;
            word-break: break-word;
          }

          .blood-type {
            background: #dc3545;
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.8rem;
          }

          .activity-summary {
            font-style: italic;
            color: #666;
          }
        }
      }

      .login-btn {
        width: 100%;
        padding: 0.75rem;
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #0056b3, #004085);
          transform: translateY(-2px);
        }

        &:disabled {
          background: #6c757d;
          cursor: not-allowed;
          transform: none;
        }
      }
    }
  }

  .accounts-footer {
    margin-top: 3rem;
    text-align: center;

    .navigation-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-bottom: 2rem;

      .btn {
        padding: 1rem 2rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;

        &.btn-outline {
          background: transparent;
          border: 2px solid white;
          color: white;

          &:hover {
            background: white;
            color: #667eea;
          }
        }

        &.btn-primary {
          background: white;
          color: #667eea;
          border: 2px solid white;

          &:hover {
            background: transparent;
            color: white;
          }
        }
      }
    }

    .info-note {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 1.5rem;
      color: white;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      p {
        margin: 0 0 1rem 0;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: #ffd700;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .test-accounts {
    padding: 1rem;

    .accounts-header {
      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .account-section {
      padding: 1.5rem;

      .accounts-grid {
        grid-template-columns: 1fr;
      }

      .account-card {
        .account-details {
          .detail-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;

            span:not(.label) {
              text-align: left;
              max-width: 100%;
            }
          }
        }
      }
    }

    .accounts-footer {
      .navigation-buttons {
        flex-direction: column;
        align-items: center;

        .btn {
          width: 100%;
          max-width: 300px;
        }
      }
    }
  }
}
