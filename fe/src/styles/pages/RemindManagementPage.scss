.remind-management-content {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 200px);

  .remind-management {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        color: #262626;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }

    .ant-modal {
      .ant-modal-header {
        border-bottom: 1px solid #f0f0f0;
        
        .ant-modal-title {
          color: #1890ff;
          font-weight: 600;
        }
      }

      .ant-form {
        .ant-form-item-label > label {
          font-weight: 500;
          color: #262626;
        }

        .ant-input,
        .ant-input-number,
        .ant-select-selector,
        .ant-picker {
          border-radius: 6px;
        }

        .ant-btn {
          border-radius: 6px;
          font-weight: 500;
        }
      }
    }

    .ant-tag {
      border-radius: 12px;
      font-weight: 500;
      padding: 2px 8px;
    }

    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;

      &.ant-btn-primary {
        background: #1890ff;
        border-color: #1890ff;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }

      &.ant-btn-primary.ant-btn-dangerous {
        background: #ff4d4f;
        border-color: #ff4d4f;

        &:hover {
          background: #ff7875;
          border-color: #ff7875;
        }
      }
    }

    .ant-tooltip {
      .ant-tooltip-inner {
        border-radius: 6px;
      }
    }

    .ant-popconfirm {
      .ant-popover-inner {
        border-radius: 8px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .remind-management-content {
    padding: 16px;

    .remind-management {
      padding: 16px;

      .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        h2 {
          font-size: 20px;
        }
      }

      .ant-table {
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px;
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .remind-management-content {
    padding: 12px;

    .remind-management {
      padding: 12px;

      .page-header {
        h2 {
          font-size: 18px;
        }
      }

      .ant-modal {
        margin: 0;
        max-width: 100vw;
        top: 0;
        padding-bottom: 0;

        .ant-modal-content {
          border-radius: 0;
        }
      }
    }
  }
}
