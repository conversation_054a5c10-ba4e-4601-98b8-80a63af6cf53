@use "../base/variables" as vars;

.nearby-donors-modal {
  background: white;
  border-radius: 25px;
  max-width: 1200px;
  width: 95%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(23, 162, 184, 0.1);
  animation: slideUp 0.3s ease;

  .modal-header {
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border-radius: 25px 25px 0 0;

    .header-info {
      flex: 1;

      h2 {
        margin: 0 0 0.5rem 0;
        background: linear-gradient(45deg, #17a2b8, #20c997);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 1.8rem;
        font-weight: 700;
      }

      p {
        color: #6c757d;
        margin: 0 0 1rem 0;
        font-size: 1.1rem;
      }

      .request-info {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;

        .blood-type-badge {
          background: linear-gradient(45deg, #17a2b8, #20c997);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 12px;
          font-weight: 700;
          box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        }

        .quantity-info {
          background: linear-gradient(45deg, #28a745, #20c997);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 12px;
          font-weight: 700;
          box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .urgency-badge {
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 12px;
          font-weight: 700;
          font-size: 0.9rem;

          &.urgency-emergency {
            background: linear-gradient(45deg, #dc3545, #c82333);
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
          }

          &.urgency-urgent {
            background: linear-gradient(45deg, #fd7e14, #e55a4e);
            box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
          }

          &.urgency-normal {
            background: linear-gradient(45deg, #6c757d, #495057);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
          }
        }
      }
    }

    .close-btn {
      background: linear-gradient(45deg, #dc3545, #c82333);
      border: none;
      color: white;
      font-size: 1.5rem;
      cursor: pointer;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        transform: rotate(90deg) scale(1.1);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
      }
    }
  }

  .modal-body {
    padding: 2rem;

    .search-controls {
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 15px;
      margin-bottom: 2rem;
      display: flex;
      gap: 2rem;
      align-items: flex-end;
      flex-wrap: wrap;
      border: 1px solid #e9ecef;

      .control-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        label {
          font-weight: 700;
          color: #495057;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        select {
          padding: 0.75rem 1rem;
          border: 2px solid #e9ecef;
          border-radius: 10px;
          font-size: 1rem;
          background: white;
          transition: all 0.3s ease;
          font-weight: 500;

          &:focus {
            outline: none;
            border-color: #17a2b8;
            box-shadow: 0 0 0 4px rgba(23, 162, 184, 0.1);
          }
        }
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 10px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.btn-primary {
          background: linear-gradient(45deg, #17a2b8, #20c997);
          color: white;
          box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }

    .donor-stats {
      display: flex;
      gap: 2rem;
      margin-bottom: 2rem;
      justify-content: center;

      .stat-item {
        text-align: center;
        padding: 1.5rem;
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
        min-width: 120px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
          display: block;
          font-size: 2rem;
          font-weight: 800;
          background: linear-gradient(45deg, #17a2b8, #20c997);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 0.5rem;
        }

        .stat-label {
          color: #6c757d;
          font-size: 0.9rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        &.eligible .stat-number {
          background: linear-gradient(45deg, #28a745, #20c997);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        &.selected .stat-number {
          background: linear-gradient(45deg, #6f42c1, #e83e8c);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }

    .donors-section {
      .loading-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #f8f9fa;
          border-top: 4px solid #17a2b8;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 1rem;
        }
      }

      .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;

        .empty-icon {
          font-size: 4rem;
          display: block;
          margin-bottom: 1rem;
        }

        h3 {
          margin: 0 0 1rem 0;
          color: #495057;
        }
      }

      .donors-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 1.5rem;

        .donor-card {
          background: white;
          border-radius: 15px;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          border: 2px solid #e9ecef;
          transition: all 0.3s ease;
          overflow: hidden;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
          }

          &.selected {
            border-color: #17a2b8;
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
          }

          &.ineligible {
            opacity: 0.7;
            border-color: #dc3545;
            background: linear-gradient(
              135deg,
              rgba(248, 215, 218, 0.3),
              rgba(245, 198, 203, 0.3)
            );
          }

          .donor-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            display: flex;
            justify-content: space-between;
            align-items: center;

            .donor-basic-info {
              .donor-name {
                font-size: 1.2rem;
                font-weight: 700;
                color: #495057;
                margin-bottom: 0.5rem;
              }

              .blood-type-badge {
                background: linear-gradient(45deg, #17a2b8, #20c997);
                color: white;
                padding: 0.5rem 0.75rem;
                border-radius: 10px;
                font-weight: 700;
                font-size: 0.9rem;
                box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
              }
            }

            .donor-selection {
              input[type="checkbox"] {
                width: 20px;
                height: 20px;
                cursor: pointer;
                accent-color: #17a2b8;
              }
            }
          }

          .donor-details {
            padding: 1.5rem;

            .detail-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 1rem;
              padding-bottom: 0.75rem;
              border-bottom: 1px solid #f8f9fa;

              &:last-child {
                margin-bottom: 0;
                border-bottom: none;
              }

              .detail-label {
                font-weight: 600;
                color: #6c757d;
                font-size: 0.9rem;
              }

              .distance-info {
                font-weight: 700;
                text-align: right;

                &.distance-very_high {
                  color: #dc3545;
                }

                &.distance-high {
                  color: #fd7e14;
                }

                &.distance-medium {
                  color: #ffc107;
                }

                &.distance-low {
                  color: #28a745;
                }

                small {
                  display: block;
                  font-size: 0.8rem;
                  color: #6c757d;
                  font-weight: 500;
                }
              }

              .donations-count {
                background: linear-gradient(45deg, #28a745, #20c997);
                color: white;
                padding: 0.25rem 0.5rem;
                border-radius: 8px;
                font-weight: 700;
                font-size: 0.8rem;
              }

              .eligibility-status {
                padding: 0.25rem 0.5rem;
                border-radius: 8px;
                font-weight: 700;
                font-size: 0.8rem;

                &.eligible {
                  background: linear-gradient(45deg, #d4edda, #c3e6cb);
                  color: #155724;
                }

                &.not-eligible {
                  background: linear-gradient(45deg, #f8d7da, #f5c6cb);
                  color: #721c24;
                }
              }

              .contact-info,
              .address {
                font-size: 0.9rem;
                color: #495057;
                text-align: right;
                max-width: 200px;
                word-wrap: break-word;
              }
            }
          }

          .donor-actions {
            padding: 1rem 1.5rem;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .btn {
              padding: 0.5rem 1rem;
              border: 2px solid #17a2b8;
              background: transparent;
              color: #17a2b8;
              text-decoration: none;
              border-radius: 8px;
              font-weight: 600;
              font-size: 0.9rem;
              transition: all 0.3s ease;

              &:hover {
                background: #17a2b8;
                color: white;
                transform: translateY(-2px);
                text-decoration: none;
              }
            }

            .priority-badge {
              padding: 0.5rem 0.75rem;
              border-radius: 10px;
              font-weight: 700;
              font-size: 0.8rem;
              text-transform: uppercase;

              &.priority-very_high {
                background: linear-gradient(45deg, #dc3545, #c82333);
                color: white;
              }

              &.priority-high {
                background: linear-gradient(45deg, #fd7e14, #e55a4e);
                color: white;
              }

              &.priority-medium {
                background: linear-gradient(45deg, #ffc107, #fd7e14);
                color: #212529;
              }

              &.priority-low {
                background: linear-gradient(45deg, #28a745, #20c997);
                color: white;
              }

              &.priority-very_low {
                background: linear-gradient(45deg, #6c757d, #495057);
                color: white;
              }
            }
          }
        }
      }
    }
  }

  .modal-footer {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 0 0 25px 25px;

    .footer-info {
      color: #6c757d;
      font-weight: 600;
    }

    .footer-actions {
      display: flex;
      gap: 1rem;

      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 12px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.btn-secondary {
          background: linear-gradient(45deg, #6c757d, #495057);
          color: white;
          box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
          }
        }

        &.btn-primary {
          background: linear-gradient(45deg, #17a2b8, #20c997);
          color: white;
          box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
          }
        }
      }
    }
  }
}

// Animations
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .nearby-donors-modal {
    width: 98%;
    max-height: 95vh;

    .modal-header {
      padding: 1.5rem;
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;

      .header-info h2 {
        font-size: 1.5rem;
      }

      .request-info {
        justify-content: center;
      }
    }

    .modal-body {
      padding: 1.5rem;

      .search-controls {
        flex-direction: column;
        gap: 1rem;

        .control-group {
          width: 100%;
        }
      }

      .donor-stats {
        flex-wrap: wrap;
        gap: 1rem;

        .stat-item {
          min-width: 100px;
          padding: 1rem;
        }
      }

      .donors-list {
        grid-template-columns: 1fr;
        gap: 1rem;

        .donor-card .donor-details .detail-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.5rem;

          .contact-info,
          .address {
            max-width: 100%;
            text-align: left;
          }
        }
      }
    }

    .modal-footer {
      padding: 1rem 1.5rem;
      flex-direction: column;
      gap: 1rem;

      .footer-actions {
        width: 100%;
        justify-content: stretch;

        .btn {
          flex: 1;
        }
      }
    }
  }
}
