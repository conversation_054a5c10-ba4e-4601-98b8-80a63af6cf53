import React, { useEffect, useState } from "react";
import { Modal, Form, Input, Select, Upload, Button } from "antd";
import ProseMirrorEditor from "../../shared/ProseMirrorEditor";

const { Option } = Select;

const BlogEditModal = ({
  visible,
  selectedBlog,
  activeTab,
  editImage,
  tags = [],
  tagsLoading = false,
  onCancel,
  onSubmit,
  onImageChange,
  form,
}) => {
  const [selectedTags, setSelectedTags] = useState([]);

  // Reset form when modal is closed
  useEffect(() => {
    if (!visible) {
      form.resetFields();
      setSelectedTags([]);
    }
  }, [visible, form]);

  // Set form values when selectedBlog or modal visibility changes
  useEffect(() => {
    if (selectedBlog && visible) {
      console.log("BlogEditModal - selectedBlog:", selectedBlog);
      console.log("BlogEditModal - tags:", tags);
      console.log("BlogEditModal - activeTab:", activeTab);
      console.log("BlogEditModal - tagsLoading:", tagsLoading);

      // Format tags for the form - use tagName as value for consistency with Select options
      const formattedTags =
        selectedBlog.tags
          ?.map((tag) => {
            console.log("BlogEditModal - processing tag:", tag);
            if (typeof tag === "object" && tag.tagId && tag.tagName) {
              return tag.tagName;
            } else if (typeof tag === "string") {
              return tag;
            } else {
              return "";
            }
          })
          .filter(Boolean) || [];

      console.log("BlogEditModal - formattedTags:", formattedTags);

      // Always set form values even if tags are still loading
      form.setFieldsValue({
        title: selectedBlog.title,
        content: selectedBlog.content,
        tags: formattedTags,
      });

      setSelectedTags(formattedTags);
    }
  }, [selectedBlog, visible, form]);

  // Update tags when they finish loading
  useEffect(() => {
    if (selectedBlog && visible && !tagsLoading && tags.length > 0) {
      console.log("BlogEditModal - Tags finished loading, updating form...");

      const formattedTags =
        selectedBlog.tags
          ?.map((tag) => {
            if (typeof tag === "object" && tag.tagId && tag.tagName) {
              return tag.tagName;
            } else if (typeof tag === "string") {
              return tag;
            } else {
              return "";
            }
          })
          .filter(Boolean) || [];

      // Only update tags field to preserve other form values
      form.setFieldsValue({
        tags: formattedTags,
      });

      setSelectedTags(formattedTags);
    }
  }, [tags, tagsLoading, selectedBlog, visible, form]);

  if (!selectedBlog) return null;

  return (
    <Modal
      title="Chỉnh sửa bài viết"
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Hủy
        </Button>,
        <Button key="submit" type="primary" onClick={onSubmit}>
          Lưu
        </Button>,
      ]}
      width={700}
    >
      <Form form={form} layout="vertical" initialValues={{ ...selectedBlog }}>
        <Form.Item name="title" label="Tiêu đề">
          <Input />
        </Form.Item>
        <Form.Item name="content" label="Nội dung">
          <ProseMirrorEditor
            height={300}
            placeholder="Nhập nội dung bài viết..."
          />
        </Form.Item>
        <Form.Item name="imgUrl" label="Ảnh thumbnail">
          <Upload
            listType="picture-card"
            showUploadList={false}
            beforeUpload={(file) => {
              const reader = new FileReader();
              reader.onload = (e) => onImageChange(e.target.result);
              reader.readAsDataURL(file);
              return false;
            }}
          >
            {editImage ? (
              <img
                src={editImage}
                alt="thumbnail"
                style={{
                  width: 80,
                  height: 60,
                  objectFit: "cover",
                  borderRadius: 4,
                }}
              />
            ) : (
              <div>Chọn ảnh</div>
            )}
          </Upload>
        </Form.Item>
        {(activeTab === "Tài liệu" || activeTab === "Tin tức") && (
          <Form.Item name="tags" label="Tags">
            <Select
              mode="tags"
              placeholder="Chọn tags hoặc nhập tags mới"
              allowClear
              loading={tagsLoading}
              showSearch
              style={{ width: "100%" }}
              value={selectedTags}
              onChange={(value) => {
                setSelectedTags(value || []);
                form.setFieldsValue({ tags: value });
              }}
            >
              {tags.map((tag) => {
                // Xử lý cả string và object tags - sử dụng tagName làm value để khớp với form
                const tagValue =
                  typeof tag === "object" && tag.tagName ? tag.tagName : tag;
                const tagText =
                  typeof tag === "object" && tag.tagName ? tag.tagName : tag;
                const tagKey =
                  typeof tag === "object" && tag.tagId ? tag.tagId : tag;

                return (
                  <Option key={tagKey} value={tagValue}>
                    {tagText}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default BlogEditModal;
