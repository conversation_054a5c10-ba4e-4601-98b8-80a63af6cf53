import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Card, Typography } from "antd";
import { 
  HeartFilled, 
  UserOutlined, 
  SettingOutlined,
  FileTextOutlined,
  DashboardOutlined 
} from "@ant-design/icons";
import ModernPageHeader from "./ModernPageHeader";

const { Title, Text } = Typography;

/**
 * Demo component để showcase các variant của ModernPageHeader
 */
const ModernPageHeaderDemo = () => {
  const [loading, setLoading] = useState(false);

  const handleReload = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  const variants = [
    {
      name: "Default",
      variant: "default",
      theme: "light",
      description: "Thiết kế mặc định với background trắng và gradient nhẹ"
    },
    {
      name: "Gradient",
      variant: "gradient", 
      theme: "light",
      description: "Thiết kế gradient đầy màu sắc và hiện đại"
    },
    {
      name: "Glass",
      variant: "glass",
      theme: "light", 
      description: "Hiệu ứng glassmorphism với backdrop blur"
    },
    {
      name: "Dark",
      variant: "dark",
      theme: "dark",
      description: "Theme tối cho giao diện dark mode"
    }
  ];

  return (
    <div style={{ padding: "2rem", background: "#f5f5f5", minHeight: "100vh" }}>
      <Card style={{ marginBottom: "2rem" }}>
        <Title level={2}>ModernPageHeader Demo</Title>
        <Text>
          Showcase các variant khác nhau của ModernPageHeader component
        </Text>
      </Card>

      {variants.map((item, index) => (
        <div key={index} style={{ marginBottom: "3rem" }}>
          <Card size="small" style={{ marginBottom: "1rem" }}>
            <Space>
              <Title level={4} style={{ margin: 0 }}>
                {item.name} Variant
              </Title>
              <Text type="secondary">({item.description})</Text>
            </Space>
          </Card>
          
          <ModernPageHeader
            title={`${item.name} Page Header`}
            description={`Đây là demo cho ${item.name.toLowerCase()} variant của ModernPageHeader`}
            icon={
              index === 0 ? <HeartFilled /> :
              index === 1 ? <DashboardOutlined /> :
              index === 2 ? <SettingOutlined /> :
              <FileTextOutlined />
            }
            variant={item.variant}
            theme={item.theme}
            loading={loading}
            onReload={handleReload}
            breadcrumbItems={[
              {
                title: (
                  <Space size={4}>
                    <UserOutlined />
                    <span>Demo</span>
                  </Space>
                ),
              },
              {
                title: (
                  <Space size={4}>
                    <SettingOutlined />
                    <span>Components</span>
                  </Space>
                ),
              },
              {
                title: (
                  <Space size={4}>
                    <HeartFilled />
                    <span>{item.name} Header</span>
                  </Space>
                ),
              },
            ]}
            actions={[
              <Button key="action1" type="default">
                Action 1
              </Button>,
              <Button key="action2" type="primary">
                Action 2
              </Button>
            ]}
          />
        </div>
      ))}

      <Card>
        <Title level={3}>Usage</Title>
        <pre style={{ 
          background: "#f6f8fa", 
          padding: "1rem", 
          borderRadius: "8px",
          overflow: "auto"
        }}>
{`// Basic usage
<ModernPageHeader 
  title="Page Title"
  description="Page description"
  loading={loading}
  onReload={handleReload}
/>

// With variants
<ModernPageHeader 
  variant="gradient"  // default, gradient, glass
  theme="light"       // light, dark
  title="Custom Title"
  description="Custom description"
  icon={<HeartFilled />}
  loading={loading}
  onReload={handleReload}
  actions={[
    <Button key="1" type="primary">Custom Action</Button>
  ]}
/>`}
        </pre>
      </Card>
    </div>
  );
};

export default ModernPageHeaderDemo;
