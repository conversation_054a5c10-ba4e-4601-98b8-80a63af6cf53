import { apiClient } from "./axiosInstance";
import userInfoService from "./userInfoService";
import bloodRequestService from "./bloodRequestService";
import { getBloodArticles } from "./bloodArticleService";
import { fetchAllNews } from "./newsService";

/**
 * Report Service - Handles statistics and report generation
 */
class ReportService {
  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || "https://localhost:7021/api";
  }

  /**
   * Get comprehensive system statistics
   * @returns {Promise<Object>} System statistics
   */
  async getSystemStatistics() {
    try {
      // Fetch data from multiple sources in parallel
      const [users, bloodRequests, articles, news] = await Promise.all([
        userInfoService.getAllUsers().catch(() => []),
        bloodRequestService
          .getBloodRequests()
          .then((result) => (result.success ? result.data : []))
          .catch(() => []),
        getBloodArticles().catch(() => []),
        fetchAllNews().catch(() => []),
      ]);

      // Process user statistics
      const userStats = this.processUserStatistics(users);

      // Process blood request statistics
      const bloodRequestStats =
        this.processBloodRequestStatistics(bloodRequests);

      // Process content statistics
      const contentStats = this.processContentStatistics(articles, news);

      return {
        success: true,
        data: {
          overview: {
            totalUsers: users.length,
            totalBloodRequests: bloodRequests.length,
            totalArticles: articles.length,
            totalNews: news.length,
            totalContent: articles.length + news.length,
            generatedAt: new Date().toISOString(),
          },
          users: userStats,
          bloodRequests: bloodRequestStats,
          content: contentStats,
          charts: {
            usersByRole: this.generateUsersByRoleChart(users),
            requestsByStatus: this.generateRequestsByStatusChart(bloodRequests),
            requestsByMonth: this.generateRequestsByMonthChart(bloodRequests),
            usersByBloodType: this.generateUsersByBloodTypeChart(users),
          },
        },
      };
    } catch (error) {
      console.error("Error fetching system statistics:", error);
      return {
        success: false,
        error: error.message,
        data: null,
      };
    }
  }

  /**
   * Process user statistics
   * @param {Array} users - Array of users
   * @returns {Object} User statistics
   */
  processUserStatistics(users) {
    const stats = {
      total: users.length,
      active: 0,
      inactive: 0,
      byRole: { admin: 0, manager: 0, doctor: 0, member: 0 },
      byGender: { male: 0, female: 0, other: 0 },
      byBloodType: {},
      byDepartment: {},
    };

    users.forEach((user) => {
      // Status
      if (user.status === 1 || user.status === "active") stats.active++;
      else stats.inactive++;

      // Role
      if (user.roleID === 1) stats.byRole.member++;
      else if (user.roleID === 2) stats.byRole.doctor++;
      else if (user.roleID === 3) stats.byRole.manager++;
      else if (user.roleID === 4) stats.byRole.admin++;

      // Gender
      const gender = user.gender?.toLowerCase();
      if (gender === "male") stats.byGender.male++;
      else if (gender === "female") stats.byGender.female++;
      else stats.byGender.other++;

      // Blood type
      if (user.bloodGroup) {
        const bloodType = `${user.bloodGroup}${user.rhType || ""}`;
        stats.byBloodType[bloodType] = (stats.byBloodType[bloodType] || 0) + 1;
      }

      // Department (for doctors)
      if (user.roleID === 2 && user.department) {
        stats.byDepartment[user.department] =
          (stats.byDepartment[user.department] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * Process blood request statistics
   * @param {Array} requests - Array of blood requests
   * @returns {Object} Blood request statistics
   */
  processBloodRequestStatistics(requests) {
    const stats = {
      total: requests.length,
      byStatus: { pending: 0, accepted: 0, completed: 0, rejected: 0 },
      byBloodType: {},
      byMonth: {},
      totalQuantity: 0,
    };

    requests.forEach((request) => {
      // Status
      if (request.status === 0) stats.byStatus.pending++;
      else if (request.status === 1) stats.byStatus.accepted++;
      else if (request.status === 2) stats.byStatus.completed++;
      else if (request.status === 3) stats.byStatus.rejected++;

      // Blood type
      if (request.bloodGroup) {
        const bloodType = `${request.bloodGroup}${request.rhType || ""}`;
        stats.byBloodType[bloodType] = (stats.byBloodType[bloodType] || 0) + 1;
      }

      // Quantity
      if (request.quantity) {
        stats.totalQuantity += parseInt(request.quantity) || 0;
      }

      // Month
      if (request.createdTime) {
        const month = new Date(request.createdTime).toISOString().slice(0, 7);
        stats.byMonth[month] = (stats.byMonth[month] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * Process content statistics
   * @param {Array} articles - Array of articles
   * @param {Array} news - Array of news
   * @returns {Object} Content statistics
   */
  processContentStatistics(articles, news) {
    return {
      articles: {
        total: articles.length,
        published: articles.filter((a) => a.status === "published").length,
        draft: articles.filter((a) => a.status === "draft").length,
      },
      news: {
        total: news.length,
        published: news.filter((n) => n.status === "published").length,
        draft: news.filter((n) => n.status === "draft").length,
      },
    };
  }

  /**
   * Generate chart data for users by role
   * @param {Array} users - Array of users
   * @returns {Array} Chart data
   */
  generateUsersByRoleChart(users) {
    const roleMap = {
      1: "Thành viên",
      2: "Bác sĩ",
      3: "Quản lý",
      4: "Quản trị viên",
    };
    const data = {};

    users.forEach((user) => {
      const roleName = roleMap[user.roleID] || "Khác";
      data[roleName] = (data[roleName] || 0) + 1;
    });

    return Object.entries(data).map(([name, value]) => ({ name, value }));
  }

  /**
   * Generate chart data for requests by status
   * @param {Array} requests - Array of requests
   * @returns {Array} Chart data
   */
  generateRequestsByStatusChart(requests) {
    const statusMap = {
      0: "Chờ duyệt",
      1: "Đã duyệt",
      2: "Hoàn thành",
      3: "Từ chối",
    };
    const data = {};

    requests.forEach((request) => {
      const statusName = statusMap[request.status] || "Khác";
      data[statusName] = (data[statusName] || 0) + 1;
    });

    return Object.entries(data).map(([name, value]) => ({ name, value }));
  }

  /**
   * Generate chart data for requests by month
   * @param {Array} requests - Array of requests
   * @returns {Array} Chart data
   */
  generateRequestsByMonthChart(requests) {
    const data = {};

    requests.forEach((request) => {
      if (request.createdTime) {
        const month = new Date(request.createdTime).toLocaleDateString(
          "vi-VN",
          {
            year: "numeric",
            month: "short",
          }
        );
        data[month] = (data[month] || 0) + 1;
      }
    });

    return Object.entries(data)
      .sort(([a], [b]) => new Date(a) - new Date(b))
      .map(([name, value]) => ({ name, value }));
  }

  /**
   * Generate chart data for users by blood type
   * @param {Array} users - Array of users
   * @returns {Array} Chart data
   */
  generateUsersByBloodTypeChart(users) {
    const data = {};

    users.forEach((user) => {
      if (user.bloodGroup) {
        const bloodType = `${user.bloodGroup}${user.rhType || ""}`;
        data[bloodType] = (data[bloodType] || 0) + 1;
      }
    });

    return Object.entries(data).map(([name, value]) => ({ name, value }));
  }

  /**
   * Get detailed report data for export
   * @param {string} reportType - Type of report (overview, users, requests, content)
   * @param {string} period - Time period (week, month, quarter, year)
   * @returns {Promise<Object>} Report data
   */
  async getDetailedReport(reportType = "overview", period = "month") {
    try {
      const stats = await this.getSystemStatistics();

      if (!stats.success) {
        throw new Error(stats.error);
      }

      const reportData = {
        reportType,
        period,
        generatedAt: new Date().toISOString(),
        generatedBy: "Admin",
        ...stats.data,
      };

      return {
        success: true,
        data: reportData,
      };
    } catch (error) {
      console.error("Error generating detailed report:", error);
      return {
        success: false,
        error: error.message,
        data: null,
      };
    }
  }
}

export default new ReportService();
