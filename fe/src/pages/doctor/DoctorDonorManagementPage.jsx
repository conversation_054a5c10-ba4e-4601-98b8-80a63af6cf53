import React from "react";
import { ReloadOutlined, TeamOutlined } from "@ant-design/icons";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import PageHeader from "../../components/doctor/PageHeader";
import {
  DonorStatistics,
  DonorFilters,
  DonorTable,
  DonorUpdateModal,
  DonorStatusModal,
} from "../../components/doctor/donor-management";
import { useDoctorDonorManagement } from "../../hooks/useDoctorDonorManagement";
import { useDoctorDonorActions } from "../../hooks/useDoctorDonorActions";
import "../../styles/pages/DoctorDonorManagementPage.scss";
import "../../styles/components/DoctorPageHeader.scss";

const DoctorDonorManagementPage = () => {
  // Use custom hooks for state management and actions
  const {
    loading,
    filter,
    selectedDonor,
    showUpdateModal,
    showStatusModal,
    updateData,
    statusUpdateData,
    currentUser,
    isBloodDepartment,
    filteredDonors,
    statistics,
    setFilter,
    setShowUpdateModal,
    setShowStatusModal,
    setUpdateData,
    setStatusUpdateData,
    setSelectedDonor,
    loadDonors,
    handleUpdateDonor,
    handleUpdateStatus,
    handleDeleteAppointment,
  } = useDoctorDonorManagement();

  const { handleSaveUpdate, handleSaveStatusUpdate } = useDoctorDonorActions();

  // Handle save update wrapper
  const onSaveUpdate = () => {
    handleSaveUpdate(
      selectedDonor,
      updateData,
      currentUser,
      () => {
        // Reload data after update
        loadDonors();
      },
      setShowUpdateModal,
      setSelectedDonor
    );
  };

  // Handle save status update wrapper
  const onSaveStatusUpdate = () => {
    handleSaveStatusUpdate(
      selectedDonor,
      statusUpdateData,
      currentUser,
      loadDonors, // Pass loadDonors as callback to refresh data
      setShowStatusModal,
      setSelectedDonor
    );
  };

  // Check access permission
  if (!isBloodDepartment) {
    return (
      <DoctorLayout>
        <div className="access-denied">
          <div className="access-denied-content">
            <h2>🚫 Không có quyền truy cập</h2>
            <p>Chỉ bác sĩ khoa Huyết học mới có thể truy cập trang này.</p>
          </div>
        </div>
      </DoctorLayout>
    );
  }

  return (
    <DoctorLayout>
      <div className="doctor-donor-management-content">
        <PageHeader
          title="Quản lý người hiến máu"
          description="Quản lý thông tin và trạng thái của người hiến máu"
          icon={TeamOutlined}
          actions={[
            {
              label: "Làm mới",
              icon: <ReloadOutlined />,
              onClick: loadDonors,
              loading: loading,
            },
          ]}
        />

        {/* Statistics */}
        <DonorStatistics statistics={statistics} />

        {/* Filters */}
        <DonorFilters
          filter={filter}
          setFilter={setFilter}
          statistics={statistics}
          loading={loading}
          onRefresh={loadDonors}
        />

        {/* Table */}
        <DonorTable
          donors={filteredDonors}
          loading={loading}
          onUpdateDonor={handleUpdateDonor}
          onUpdateStatus={handleUpdateStatus}
          onDeleteAppointment={handleDeleteAppointment}
        />

        {/* Update Modal */}
        <DonorUpdateModal
          visible={showUpdateModal}
          onCancel={() => setShowUpdateModal(false)}
          onSave={onSaveUpdate}
          selectedDonor={selectedDonor}
          updateData={updateData}
          setUpdateData={setUpdateData}
        />

        {/* Status Modal */}
        <DonorStatusModal
          visible={showStatusModal}
          onCancel={() => setShowStatusModal(false)}
          onSave={onSaveStatusUpdate}
          selectedDonor={selectedDonor}
          statusUpdateData={statusUpdateData}
          setStatusUpdateData={setStatusUpdateData}
        />
      </div>
    </DoctorLayout>
  );
};

export default DoctorDonorManagementPage;
