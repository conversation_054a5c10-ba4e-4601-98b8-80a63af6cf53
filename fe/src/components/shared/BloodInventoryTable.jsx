import React from "react";
import { Table, Card } from "antd";
import {
  getBloodComponentName,
  mapRhTypeToSymbol,
  getInventoryStatus,
  getInventoryStatusColor,
  getInventoryStatusText,
  getInventoryStatusIcon,
} from "../../constants/bloodInventoryConstants";

/**
 * Shared Blood Inventory Table Component
 * Được sử dụng chung giữa Manager và <PERSON> với các props khác nhau
 */
const BloodInventoryTable = ({
  data = [],
  loading = false,
  showActions = false, // Manager có actions, Doctor không có
  actionColumns = [], // C<PERSON><PERSON> cột action riêng cho Manager
  pagination = {
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} mục`,
  },
  scroll = { x: 800 },
  className = "",
  ...tableProps
}) => {
  // Build filters for blood types
  const bloodTypes = Array.from(new Set(data.map((item) => item.bloodType)))
    .filter(Boolean)
    .map((type) => ({ text: type, value: type }));

  // Build filters for component types
  const componentTypes = Array.from(
    new Set(data.map((item) => item.componentType))
  )
    .filter(Boolean)
    .map((type) => ({ text: type, value: type }));

  // Build filters for bag types
  const bagTypes = Array.from(new Set(data.map((item) => item.bagType)))
    .filter(Boolean)
    .map((type) => ({ text: type, value: type }));

  // Build filters for status
  const statusFilters = Array.from(new Set(data.map((item) => item.status)))
    .filter(Boolean)
    .map((status) => ({
      text: getInventoryStatusText(status),
      value: status,
    }));

  // Base columns - chung cho cả Manager và Doctor
  const baseColumns = [
    {
      title: <div style={{ textAlign: "center", width: "100%" }}>Nhóm máu</div>,
      dataIndex: "bloodType",
      key: "bloodType",
      width: 120,
      align: "center",
      filters: bloodTypes,
      onFilter: (value, record) => record.bloodType === value,
      render: (bloodType) => {
        const isPositive = bloodType.includes("+");
        return (
          <span
            className={`blood-type-badge ${
              isPositive ? "positive" : "negative"
            }`}
          >
            {bloodType}
          </span>
        );
      },
    },
    {
      title: (
        <div style={{ textAlign: "center", width: "100%" }}>Thành phần</div>
      ),
      dataIndex: "componentType",
      key: "componentType",
      width: 150,
      align: "center",
      filters: componentTypes,
      onFilter: (value, record) => record.componentType === value,
      render: (componentType) => (
        <span style={{ fontWeight: "500", color: "#20374E" }}>
          {componentType}
        </span>
      ),
    },
    {
      title: <div style={{ textAlign: "center", width: "100%" }}>Loại túi</div>,
      dataIndex: "bagType",
      key: "bagType",
      width: 100,
      align: "center",
      filters: bagTypes,
      onFilter: (value, record) => record.bagType === value,
      render: (bagType) => (
        <span style={{ color: "#666", fontSize: "13px" }}>
          {bagType || "250ml"}
        </span>
      ),
    },
    {
      title: <div style={{ textAlign: "center", width: "100%" }}>Số lượng</div>,
      dataIndex: "quantity",
      key: "quantity",
      width: 100,
      align: "center",
      sorter: (a, b) => a.quantity - b.quantity,
      render: (quantity) => (
        <span style={{ fontWeight: "600", fontSize: "14px", color: "#000" }}>
          {quantity}
        </span>
      ),
    },
    {
      title: (
        <div style={{ textAlign: "center", width: "100%" }}>Trạng thái</div>
      ),
      dataIndex: "status",
      key: "status",
      width: 120,
      align: "center",
      filters: statusFilters,
      onFilter: (value, record) => record.status === value,
      render: (status, record) => {
        const statusColor = getInventoryStatusColor(status);
        const statusText = getInventoryStatusText(status);

        // Màu sắc hài hòa hơn cho từng trạng thái
        const getStatusStyle = (status) => {
          switch (status) {
            case "critical":
              return {
                backgroundColor: "#ff4d4f",
                color: "white",
                borderColor: "#ff4d4f",
              };
            case "low":
              return {
                backgroundColor: "#faad14",
                color: "white",
                borderColor: "#faad14",
              };
            case "normal":
              return {
                backgroundColor: "#52c41a",
                color: "white",
                borderColor: "#52c41a",
              };
            case "high":
              return {
                backgroundColor: "#1890ff",
                color: "white",
                borderColor: "#1890ff",
              };
            default:
              return {
                backgroundColor: "#d9d9d9",
                color: "#666",
                borderColor: "#d9d9d9",
              };
          }
        };

        const statusStyle = getStatusStyle(status);

        return (
          <span
            className={`status-badge status-${status}`}
            style={{
              ...statusStyle,
              borderRadius: "16px",
              padding: "6px 14px",
              fontSize: "11px",
              fontWeight: "500",
              display: "inline-block",
              textAlign: "center",
              minWidth: "85px",
              border: `1px solid ${statusStyle.borderColor}`,
              textTransform: "uppercase",
              letterSpacing: "0.5px",
              boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
            }}
          >
            {statusText}
          </span>
        );
      },
    },
    {
      title: <div style={{ textAlign: "center", width: "100%" }}>Hiếm</div>,
      dataIndex: "isRare",
      key: "isRare",
      width: 80,
      align: "center",
      filters: [
        { text: "Có", value: true },
        { text: "Không", value: false },
      ],
      onFilter: (value, record) => record.isRare === value,
      render: (isRare) => (
        <span style={{ fontSize: "14px", fontWeight: "500" }}>
          {isRare ? (
            <span style={{ color: "#faad14" }}>⭐ Hiếm</span>
          ) : (
            <span style={{ color: "#666" }}>Không</span>
          )}
        </span>
      ),
    },
    {
      title: (
        <div style={{ textAlign: "center", width: "100%" }}>Cập nhật cuối</div>
      ),
      dataIndex: "lastUpdated",
      key: "lastUpdated",
      width: 150,
      align: "center",
      sorter: (a, b) => new Date(a.lastUpdated) - new Date(b.lastUpdated),
      render: (lastUpdated) => {
        if (!lastUpdated) return "—";
        const date = new Date(lastUpdated);
        return (
          <span style={{ color: "#666", fontSize: "12px" }}>
            {date.toLocaleDateString("vi-VN")}
            <br />
            {date.toLocaleTimeString("vi-VN", {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </span>
        );
      },
    },
  ];

  // Combine base columns with action columns if needed
  const columns = showActions
    ? [...baseColumns, ...actionColumns]
    : baseColumns;

  return (
    <Card>
      <Table
        className={`blood-inventory-table ${className}`}
        columns={columns}
        dataSource={data}
        rowKey="inventoryId"
        loading={loading}
        pagination={pagination}
        scroll={scroll}
        {...tableProps}
      />
    </Card>
  );
};

export default BloodInventoryTable;
