import React from "react";
import { Card, Row, Col, Select, Space, Typography } from "antd";
import {
  FilterOutlined,
  HeartOutlined,
  MedicineBoxOutlined,
} from "@ant-design/icons";

const { Option } = Select;
const { Text } = Typography;

/**
 * Component Filter Section cho ActivityHistoryPage
 * G<PERSON>ữ nguyên thiết kế và logic từ file gốc
 */
const ActivityFilterSection = ({
  filter,
  onFilterChange,
  activities,
  donationCount,
  requestCount,
}) => {
  return (
    <Card className="filter-section-card">
      <Row align="middle" gutter={16}>
        <Col>
          <Space align="center">
            <div className="filter-icon-wrapper">
              <FilterOutlined className="filter-icon" />
            </div>
            <Text strong className="filter-label">
              Lọc theo loại hoạt động:
            </Text>
          </Space>
        </Col>
        <Col>
          <Select
            value={filter}
            onChange={onFilterChange}
            size="large"
            placeholder="Chọn loại hoạt động"
            className="filter-select"
          >
            <Option value="all">
              <Space>
                <span className="filter-option-icon">🌟</span>
                <span>Tất cả ({activities.length})</span>
              </Space>
            </Option>
            <Option value="donations">
              <Space>
                <HeartOutlined style={{ color: "#d32f2f" }} />
                <span>Hiến máu ({donationCount})</span>
              </Space>
            </Option>
            <Option value="requests">
              <Space>
                <MedicineBoxOutlined style={{ color: "#1890ff" }} />
                <span>Yêu cầu máu ({requestCount})</span>
              </Space>
            </Option>
          </Select>
        </Col>
      </Row>
    </Card>
  );
};

export default ActivityFilterSection;
