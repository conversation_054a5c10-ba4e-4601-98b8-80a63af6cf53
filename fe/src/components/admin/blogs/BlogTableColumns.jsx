import React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>rm, Badge, Tag, Space } from "antd";
import {
  EyeOutlined,
  DeleteOutlined,
  EditOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  NotificationOutlined,
} from "@ant-design/icons";
import { ActivityLogTableColumns } from "./ActivityLogTableColumns";
import {
  getNewsId,
  getNewsUserId,
  getNewsCreatedDate,
} from "../../../utils/newsUtils";
import { getArticleId, getArticleUserId } from "../../../utils/articleUtils";
import dayjs from "dayjs";

const BlogTableColumns = ({
  activeTab,
  userMap,
  onView,
  onEdit,
  onDelete,
  currentUser,
}) => {
  // If it's the activity log tab, use the ActivityLogTableColumns
  if (activeTab === "Theo dõi hoạt động") {
    return ActivityLogTableColumns();
  }

  const actionButtonStyle = {
    borderRadius: "8px",
    border: "none",
    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
    transition: "all 0.2s ease",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  };

  const viewButtonStyle = {
    ...actionButtonStyle,
    background: "#e6f7ff",
    color: "#1890ff",
    border: "1px solid #91d5ff",
  };

  const editButtonStyle = {
    ...actionButtonStyle,
    background: "#f6ffed",
    color: "#52c41a",
    border: "1px solid #b7eb8f",
  };

  const deleteButtonStyle = {
    ...actionButtonStyle,
    background: "#fff2f0",
    color: "#ff4d4f",
    border: "1px solid #ffccc7",
  };

  const columns = [
    {
      title: "ID",
      dataIndex: activeTab === "Tài liệu" ? "contentID" : "contentID",
      key: "id",
      width: 80,
      sorter: (a, b) => {
        const aId = activeTab === "Tài liệu" ? getArticleId(a) : getNewsId(a);
        const bId = activeTab === "Tài liệu" ? getArticleId(b) : getNewsId(b);
        return aId - bId;
      },
      render: (id, record) => {
        // Xử lý ID cho cả tài liệu và tin tức
        let displayId = id;
        if (activeTab === "Tài liệu") {
          displayId = getArticleId(record) || id;
        } else if (activeTab === "Tin tức") {
          displayId = getNewsId(record) || id;
        }
        return (
          <span
            style={{
              fontFamily: "monospace",
              background: "#f0f2f5",
              padding: "4px 8px",
              borderRadius: "8px",
              fontSize: "12px",
              fontWeight: "500",
              color: "#1890ff",
            }}
          >
            #{displayId}
          </span>
        );
      },
    },
    {
      title: "Tiêu đề",
      dataIndex: "title",
      key: "title",
      width: 180,
      ellipsis: false,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => (
        <div style={{ padding: 8 }}>
          <input
            placeholder="Tìm kiếm tiêu đề"
            value={selectedKeys[0]}
            onChange={(e) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: "block" }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              size="small"
              style={{ width: 90 }}
            >
              Tìm
            </Button>
            <Button
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size="small"
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </Space>
        </div>
      ),
      onFilter: (value, record) =>
        record.title &&
        record.title.toLowerCase().includes(value.toLowerCase()),
      render: (text) => (
        <div
          style={{
            fontWeight: "600",
            fontSize: "14px",
            color: "#262626",
            lineHeight: "1.4",
            wordBreak: "break-word",
            whiteSpace: "pre-wrap",
            maxWidth: 180,
            minWidth: 120,
            overflow: "hidden",
            textOverflow: "ellipsis",
            display: "-webkit-box",
            WebkitLineClamp: 3,
            WebkitBoxOrient: "vertical",
          }}
        >
          {text}
        </div>
      ),
    },
    {
      title: "Người viết",
      dataIndex: "userId",
      key: "userId",
      width: 100,
      filters: Object.keys(userMap || {}).map((userId) => ({
        text: userMap[userId],
        value: userId,
      })),
      onFilter: (value, record) => {
        // Handle different possible userId fields using utility functions
        const recordUserId =
          activeTab === "Tài liệu"
            ? getArticleUserId(record)
            : getNewsUserId(record);
        return recordUserId && recordUserId.toString() === value.toString();
      },
      render: (userId) => (
        <Space>
          <UserOutlined style={{ color: "#1890ff", fontSize: "14px" }} />
          <span style={{ fontWeight: "500", fontSize: "13px" }}>
            {userMap[userId] || userId || "Unknown"}
          </span>
        </Space>
      ),
    },
    {
      title: "Ngày đăng",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 120,
      sorter: (a, b) => {
        const aDate = a.createdAt;
        const bDate = b.createdAt;
        if (!aDate && !bDate) return 0;
        if (!aDate) return 1;
        if (!bDate) return -1;
        return new Date(aDate) - new Date(bDate);
      },
      render: (date, record) => {
        // For news, try to get a valid date using utility function
        let actualDate = date;
        if (activeTab === "Tin tức") {
          const newsDate = getNewsCreatedDate(record);
          if (newsDate) {
            actualDate = newsDate;
          }
        }

        if (!actualDate)
          return (
            <span
              style={{ color: "#999", fontStyle: "italic", fontSize: "12px" }}
            >
              Chưa có
            </span>
          );

        // Handle different date formats from API
        let formattedDate;
        try {
          // Check if it's the default "1900-01-01T00:00:00" date
          if (
            actualDate === "1900-01-01T00:00:00" ||
            dayjs(actualDate).year() === 1900
          ) {
            return (
              <Space>
                <CalendarOutlined
                  style={{ color: "#faad14", fontSize: "14px" }}
                />
                <span
                  style={{
                    color: "#faad14",
                    fontStyle: "italic",
                    fontSize: "12px",
                  }}
                >
                  Chưa cập nhật
                </span>
              </Space>
            );
          }
          formattedDate = dayjs(actualDate).format("DD/MM/YYYY HH:mm");
        } catch (error) {
          console.error("Error formatting date:", error);
          formattedDate = "Không hợp lệ";
        }

        return (
          <Space>
            <CalendarOutlined style={{ color: "#52c41a", fontSize: "14px" }} />
            <span
              style={{
                fontSize: "12px",
                color: "#666",
                fontWeight: "500",
              }}
            >
              {formattedDate}
            </span>
          </Space>
        );
      },
    },
    {
      title: "Thumbnail",
      dataIndex: "imgUrl",
      key: "imgUrl",
      width: 80,
      render: (url) =>
        url ? (
          <div
            style={{
              width: 60,
              height: 40,
              borderRadius: "8px",
              overflow: "hidden",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
            }}
          >
            <img
              src={url}
              alt="thumbnail"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          </div>
        ) : (
          <div
            style={{
              width: 60,
              height: 40,
              borderRadius: "8px",
              background: "#f5f5f5",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "#999",
              fontSize: "12px",
              border: "1px dashed #d9d9d9",
            }}
          >
            {activeTab === "Tài liệu" ? (
              <FileTextOutlined style={{ fontSize: "16px" }} />
            ) : (
              <NotificationOutlined style={{ fontSize: "16px" }} />
            )}
          </div>
        ),
    },
    {
      title: "Tags",
      dataIndex: "tags",
      key: "tags",
      width: 150,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }) => (
        <div style={{ padding: 8 }}>
          <input
            placeholder="Tìm kiếm tag"
            value={selectedKeys[0]}
            onChange={(e) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: "block" }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              size="small"
              style={{ width: 90 }}
            >
              Tìm
            </Button>
            <Button
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size="small"
              style={{ width: 90 }}
            >
              Reset
            </Button>
          </Space>
        </div>
      ),
      onFilter: (value, record) => {
        if (!record.tags || !Array.isArray(record.tags)) return false;
        return record.tags.some((tag) => {
          const tagName =
            typeof tag === "object" && tag.tagName ? tag.tagName : tag;
          return tagName && tagName.toLowerCase().includes(value.toLowerCase());
        });
      },
      render: (tags) => {
        if (!tags || !Array.isArray(tags) || tags.length === 0) {
          return (
            <span
              style={{ color: "#999", fontStyle: "italic", fontSize: "12px" }}
            >
              Không có tags
            </span>
          );
        }

        return (
          <div style={{ display: "flex", flexWrap: "wrap", gap: "4px" }}>
            {tags.map((tag, index) => {
              // Xử lý cả format cũ (string) và format mới (object với tagId, tagName)
              const tagName =
                typeof tag === "object" && tag.tagName ? tag.tagName : tag;
              const tagId =
                typeof tag === "object" && tag.tagId ? tag.tagId : index;

              return (
                <Tag
                  key={tagId}
                  color="blue"
                  style={{
                    fontSize: "11px",
                    padding: "2px 6px",
                    margin: 0,
                    borderRadius: "12px",
                    border: "none",
                    background: "#e6f7ff",
                    color: "#1890ff",
                  }}
                >
                  {tagName}
                </Tag>
              );
            })}
          </div>
        );
      },
    },
    {
      title: "Thao tác",
      key: "actions",
      align: "center",
      width: 120,
      render: (_, record) => {
        // Kiểm tra xem user hiện tại có quyền chỉnh sửa bài viết này không
        const recordUserId =
          activeTab === "Tin tức"
            ? getNewsUserId(record)
            : record.userId ||
              record.userID ||
              record.authorId ||
              record.createdBy;
        const currentUserId =
          currentUser?.id || currentUser?.userId || currentUser?.userID;

        const canEdit =
          !currentUser ||
          currentUser.role === "4" || // Admin role
          currentUser.role === "admin" || // Fallback for string role
          currentUser.role === "Admin" || // Fallback for string role
          String(recordUserId) === String(currentUserId);

        console.log("Permission check:", {
          currentUser,
          recordUserId,
          currentUserId,
          canEdit,
          record,
        });

        return (
          <Row gutter={8} justify="center">
            <Col>
              <Button
                icon={<EyeOutlined />}
                onClick={() => onView(record)}
                size="small"
                style={viewButtonStyle}
                onMouseEnter={(e) => {
                  e.target.style.transform = "translateY(-1px)";
                  e.target.style.boxShadow = "0 4px 8px rgba(0,0,0,0.15)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                }}
              />
            </Col>
            {canEdit && (
              <Col>
                <Button
                  icon={<EditOutlined />}
                  onClick={() => onEdit(record)}
                  size="small"
                  style={editButtonStyle}
                  onMouseEnter={(e) => {
                    e.target.style.transform = "translateY(-1px)";
                    e.target.style.boxShadow = "0 4px 8px rgba(0,0,0,0.15)";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = "translateY(0)";
                    e.target.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                  }}
                />
              </Col>
            )}
            {canEdit && (
              <Col>
                <Popconfirm
                  title="Bạn có chắc chắn muốn xóa bài viết này?"
                  onConfirm={() => {
                    // Xử lý ID cho cả tài liệu và tin tức
                    let deleteId;
                    if (activeTab === "Tài liệu") {
                      deleteId = getArticleId(record);
                    } else {
                      // Cho tin tức, sử dụng utility
                      deleteId = getNewsId(record);
                    }

                    onDelete(deleteId);
                  }}
                  okText="Xóa"
                  cancelText="Hủy"
                >
                  <Button
                    icon={<DeleteOutlined />}
                    danger
                    size="small"
                    style={deleteButtonStyle}
                    onMouseEnter={(e) => {
                      e.target.style.transform = "translateY(-1px)";
                      e.target.style.boxShadow = "0 4px 8px rgba(0,0,0,0.15)";
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.transform = "translateY(0)";
                      e.target.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
                    }}
                  />
                </Popconfirm>
              </Col>
            )}
          </Row>
        );
      },
    },
  ];

  return columns;
};

export default BlogTableColumns;
