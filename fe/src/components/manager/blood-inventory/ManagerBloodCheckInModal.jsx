import { Modal, Select, InputNumber, Input, Row, Col } from "antd";
import {
  BLOOD_COMPONENT_MAP,
  BLOOD_GROUPS,
  RH_TYPES,
  BAG_TYPES,
} from "../../../constants/bloodInventoryConstants";
const { Option } = Select;

const COMPONENT_TYPES = Object.values(BLOOD_COMPONENT_MAP); // Sử dụng tên tiếng Việt

export default function ManagerBloodCheckInModal({
  open,
  onOk,
  onCancel,
  confirmLoading,
  form,
  setForm,
}) {
  // Nếu inventoryId đã chọn, tự động fill các trường còn lại
  // console.log("[CheckInModal] form.inventoryId:", form.inventoryId);
  // if (inventory && inventory.length > 0) {
  //   console.log("[CheckInModal] inventory[0]:", inventory[0]);
  //   console.log("[CheckInModal] inventory[0] keys:", Object.keys(inventory[0]));
  // }

  return (
    <Modal
      title="Nhập kho máu"
      open={open}
      onOk={onOk}
      onCancel={onCancel}
      okText="Nhập kho"
      cancelText="Hủy"
      confirmLoading={confirmLoading}
      okButtonProps={{
        disabled:
          !form.bloodGroup ||
          !form.rhType ||
          !form.componentType ||
          !form.quantity ||
          form.quantity <= 0,
      }}
    >
      <Row gutter={[12, 12]}>
        <Col span={12}>
          <label>Nhóm máu:</label>
          <Select
            style={{ width: "100%" }}
            placeholder="Chọn nhóm máu"
            value={form.bloodGroup || undefined}
            onChange={(val) => setForm((f) => ({ ...f, bloodGroup: val }))}
          >
            {BLOOD_GROUPS.map((group) => (
              <Option key={group} value={group}>
                {group}
              </Option>
            ))}
          </Select>
        </Col>
        <Col span={12}>
          <label>Rh:</label>
          <Select
            style={{ width: "100%" }}
            placeholder="Chọn Rh"
            value={form.rhType || undefined}
            onChange={(val) => setForm((f) => ({ ...f, rhType: val }))}
          >
            {RH_TYPES.map((rh) => (
              <Option key={rh} value={rh}>
                {rh}
              </Option>
            ))}
          </Select>
        </Col>
        <Col span={12}>
          <label>Thành phần:</label>
          <Select
            style={{ width: "100%" }}
            placeholder="Chọn thành phần"
            value={form.componentType || undefined}
            onChange={(val) => setForm((f) => ({ ...f, componentType: val }))}
          >
            {COMPONENT_TYPES.map((c) => (
              <Option key={c} value={c}>
                {c}
              </Option>
            ))}
          </Select>
        </Col>
        <Col span={12}>
          <label>Loại túi:</label>
          <Select
            style={{ width: "100%" }}
            placeholder="Chọn loại túi"
            value={form.bagType || "450ml"} // Default value
            onChange={(val) => setForm((f) => ({ ...f, bagType: val }))}
          >
            {BAG_TYPES.map((b) => (
              <Option key={b} value={b}>
                {b}
              </Option>
            ))}
          </Select>
        </Col>
        <Col span={12}>
          <label>Số lượng:</label>
          <InputNumber
            min={1}
            style={{ width: "100%" }}
            value={form.quantity}
            onChange={(val) => setForm((f) => ({ ...f, quantity: val }))}
          />
        </Col>
        <Col span={24}>
          <label>Ghi chú:</label>
          <Input.TextArea
            rows={2}
            value={form.notes}
            onChange={(e) => setForm((f) => ({ ...f, notes: e.target.value }))}
          />
        </Col>
      </Row>
    </Modal>
  );
}
