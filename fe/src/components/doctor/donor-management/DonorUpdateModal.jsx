import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>, <PERSON>ton, Card, Avatar, Divider, Row, Col,
  Select, InputNumber, Input, Spin, message, Badge, Tag
} from "antd";
import {
  EditOutlined, UserOutlined, PhoneOutlined, MailOutlined
} from "@ant-design/icons";
import bloodDonationService from "../../../services/bloodDonationService";

const { TextArea } = Input;

/**
 * Modal cập nhật thông tin người hiến máu
 */
const DonorUpdateModal = ({
  visible,
  onCancel,
  onSave,
  selectedDonor,
  updateData,
  setUpdateData
}) => {
  const [loading, setLoading] = useState(false);
  const [appointmentDetails, setAppointmentDetails] = useState(null);
  const [userInfo, setUserInfo] = useState(null);

  // Fetch appointment details when modal opens
  useEffect(() => {
    if (visible && selectedDonor?.id) {
      fetchAppointmentDetails();
      fetchUserInfo();
    }
  }, [visible, selectedDonor?.id]);

  const fetchAppointmentDetails = async () => {
    if (!selectedDonor?.id) return;

    try {
      setLoading(true);
      // Use GET /api/Appointment/{id} to get specific appointment details
      const details = await bloodDonationService.getAppointmentById(selectedDonor.id);
      setAppointmentDetails(details);

      console.log('Appointment details from API:', details);
      console.log('Weight/Height from API:', {
        weightAppointment: details.weightAppointment,
        WeightAppointment: details.WeightAppointment,
        heightAppointment: details.heightAppointment,
        HeightAppointment: details.HeightAppointment
      });

      // Check if weight/height data exists in database
      if (details.weightAppointment || details.WeightAppointment) {
        console.log('✅ Weight data found in database:', details.weightAppointment || details.WeightAppointment);
      } else {
        console.log('❌ No weight data in database');
      }

      if (details.heightAppointment || details.HeightAppointment) {
        console.log('✅ Height data found in database:', details.heightAppointment || details.HeightAppointment);
      } else {
        console.log('❌ No height data in database');
      }

      // Update form data with fetched details from GET /api/Appointment/{id}
      // Map database fields: Notes, BloodPressure, HeartRate, Hemoglobin, Temperature, WeightAppointment, HeightAppointment
      setUpdateData(prev => ({
        ...prev,
        notes: details.notes || "",
        bloodPressure: details.bloodPressure || "",
        heartRate: details.heartRate || 0,
        hemoglobin: details.hemoglobin || 0,
        temperature: details.temperature || 0,
        WeightAppointment: details.weightAppointment || details.WeightAppointment || 0,
        HeightAppointment: details.heightAppointment || details.HeightAppointment || 0,
        process: details.process || 1,
        status: details.status !== null ? details.status : true
      }));
    } catch (error) {
      console.error("Error fetching appointment details:", error);
      message.error("Không thể tải thông tin appointment");
    } finally {
      setLoading(false);
    }
  };

  const fetchUserInfo = async () => {
    try {
      const info = await bloodDonationService.getUserInfo(selectedDonor.userId);
      setUserInfo(info);

      // Update form data with user info
      setUpdateData(prev => ({
        ...prev,
        WeightAppointment: info.WeightAppointment || info.weightAppointment || 0,
        HeightAppointment: info.HeightAppointment || info.heightAppointment || 0
      }));
    } catch (error) {
      console.error("Error fetching user info:", error);
    }
  };

  const getTimeSlotText = (slot) => {
    if (slot === "morning" || slot === "Sáng (7:00-12:00)") {
      return "7:00 - 12:00";
    } else if (slot === "afternoon" || slot === "Chiều (13:00-17:00)") {
      return "13:00 - 17:00";
    }
    return slot || "N/A";
  };

  const getProcessStepText = (process) => {
    const steps = {
      1: "Đăng ký",
      2: "Khám sức khỏe cơ bản",
      3: "Lấy máu",
      4: "Xét nghiệm máu",
      5: "Nhập kho"
    };
    return steps[process] || "Không xác định";
  };

  if (!selectedDonor) return null;

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <EditOutlined style={{ color: '#1890ff' }} />
          <span>Cập nhật thông tin người hiến máu</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={onSave}>
          <EditOutlined /> Lưu thay đổi
        </Button>
      ]}
    >
      {/* Donor Summary Card */}
      <Card
        size="small"
        style={{ marginBottom: 16, backgroundColor: '#f8f9fa' }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              <Avatar size={64} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
              <div style={{ marginTop: 8, fontWeight: 'bold', color: '#1890ff' }}>
                {selectedDonor.name}
              </div>
            </div>
          </Col>
          <Col span={18}>
            <Row gutter={[16, 8]}>
              <Col span={12}>
                <div><PhoneOutlined /> <strong>SĐT:</strong> {selectedDonor.phone}</div>
              </Col>
              <Col span={12}>
                <div><MailOutlined /> <strong>Email:</strong> {selectedDonor.email}</div>
              </Col>

              <Col span={12}>
                <div>👤 <strong>Tuổi:</strong> {selectedDonor.age} ({selectedDonor.gender === 'male' ? 'Nam' : selectedDonor.gender === 'female' ? 'Nữ' : 'N/A'})</div>
              </Col>

              <Col span={12}>
                <div>📅 <strong>Lịch hẹn:</strong> {new Date(selectedDonor.appointmentDate).toLocaleDateString("vi-VN")} - {getTimeSlotText(selectedDonor.timeSlot)}</div>
              </Col>

              <Col span={12}>
                <div>
                  <strong>Trạng thái:</strong>{" "}
                  <Tag color={appointmentDetails?.status ? "green" : "red"}>
                    {appointmentDetails?.status ? "Chấp nhận" : "Không chấp nhận"}
                  </Tag>
                </div>
              </Col>

              <Col span={12}>
                <div>
                  <strong>Quy trình:</strong>{" "}
                  <Tag color="blue">
                    Bước {appointmentDetails?.process || 1}: {getProcessStepText(appointmentDetails?.process || 1)}
                  </Tag>
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      <Divider orientation="left">🩺 Cập nhật thông tin sức khỏe</Divider>
      <Row gutter={16}>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Cân nặng (kg):
            </label>
            <InputNumber
              style={{ width: '100%' }}
              min={30}
              max={200}
              value={updateData.WeightAppointment || updateData.weightAppointment}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  WeightAppointment: value,
                }))
              }
              placeholder="VD: 65"
            />
          </div>
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Chiều cao (cm):
            </label>
            <InputNumber
              style={{ width: '100%' }}
              min={100}
              max={250}
              value={updateData.HeightAppointment || updateData.heightAppointment}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  HeightAppointment: value,
                }))
              }
              placeholder="VD: 170"
            />
          </div>
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Huyết áp (mmHg):
            </label>
            <Input
              value={updateData.bloodPressure}
              onChange={(e) =>
                setUpdateData((prev) => ({
                  ...prev,
                  bloodPressure: e.target.value,
                }))
              }
              placeholder="VD: 120/80"
            />
          </div>
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Nhiệt độ (°C):
            </label>
            <InputNumber
              style={{ width: '100%' }}
              min={35}
              max={42}
              step={0.1}
              value={updateData.temperature}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  temperature: value,
                }))
              }
              placeholder="VD: 36.5"
            />
          </div>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Nhịp tim (bpm):
            </label>
            <InputNumber
              style={{ width: '100%' }}
              min={40}
              max={200}
              value={updateData.heartRate}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  heartRate: value,
                }))
              }
              placeholder="VD: 72"
            />
          </div>
        </Col>
        <Col span={12}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Hemoglobin (g/dL):
            </label>
            <InputNumber
              style={{ width: '100%' }}
              min={5}
              max={25}
              step={0.1}
              value={updateData.hemoglobin}
              onChange={(value) =>
                setUpdateData((prev) => ({
                  ...prev,
                  hemoglobin: value,
                }))
              }
              placeholder="VD: 14.5"
            />
          </div>
        </Col>

      </Row>

      <Divider orientation="left">📝 Ghi chú</Divider>
      <Row gutter={16}>
        <Col span={24}>
          <div style={{ marginBottom: 16 }}>
            <label style={{ display: 'block', marginBottom: 4, fontWeight: 'bold' }}>
              Ghi chú của bác sĩ:
            </label>
            <TextArea
              rows={4}
              value={updateData.notes}
              onChange={(e) =>
                setUpdateData((prev) => ({
                  ...prev,
                  notes: e.target.value,
                }))
              }
              placeholder="Nhập ghi chú về tình trạng sức khỏe, kết quả khám..."
            />
          </div>
        </Col>

      </Row>


    </Modal>
  );
};

export default DonorUpdateModal;
