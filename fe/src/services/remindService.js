import { apiClient } from './axiosInstance';
import { message } from 'antd';

/**
 * Service for managing reminders using the Remind API endpoints
 */
class RemindService {
  /**
   * Get all reminders for a specific user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} API response with user's reminders
   */
  static async getUserReminders(userId) {
    try {
      const response = await apiClient.get(`/Remind/user/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user reminders:', error);
      throw error;
    }
  }

  /**
   * Create a new reminder
   * @param {Object} reminderData - Reminder data
   * @param {number} reminderData.userId - User ID to send reminder to
   * @param {string} reminderData.title - Reminder title
   * @param {string} reminderData.message - Reminder message
   * @param {string} reminderData.type - Reminder type (appointment_reminder, donation_reminder, etc.)
   * @param {string} reminderData.scheduledTime - When to send the reminder (ISO string)
   * @param {Object} reminderData.data - Additional data for the reminder
   * @returns {Promise<Object>} API response with created reminder
   */
  static async createReminder(reminderData) {
    try {
      // Match database schema: ReminderId, UserId, Type, Message, RemindAt, IsDisabled, CreatedAt, IsSent, SentAt
      const payload = {
        UserId: parseInt(reminderData.userId),
        Type: reminderData.type || 'BloodDonation', // Use database enum values
        Message: reminderData.message,
        RemindAt: reminderData.scheduledTime, // Use RemindAt instead of ScheduledTime
        IsDisabled: false, // Use IsDisabled instead of IsEnabled (inverted logic)
        IsSent: false // Set as not sent initially
      };

      const response = await apiClient.post('/Remind', payload);

      // Show success message to manager only
      message.success('Đã tạo nhắc nhở thành công!');

      return response.data;
    } catch (error) {
      console.error('Error creating reminder:', error);
      throw error;
    }
  }

  /**
   * Disable a reminder
   * @param {number} reminderId - Reminder ID
   * @returns {Promise<Object>} API response
   */
  static async disableReminder(reminderId) {
    try {
      const response = await apiClient.patch(`/Remind/disable/${reminderId}`);
      return response.data;
    } catch (error) {
      console.error('Error disabling reminder:', error);
      throw error;
    }
  }

  /**
   * Enable a reminder
   * @param {number} reminderId - Reminder ID
   * @returns {Promise<Object>} API response
   */
  static async enableReminder(reminderId) {
    try {
      const response = await apiClient.patch(`/Remind/enable/${reminderId}`);
      return response.data;
    } catch (error) {
      console.error('Error enabling reminder:', error);
      throw error;
    }
  }

  /**
   * Delete a reminder
   * @param {number} reminderId - Reminder ID
   * @returns {Promise<Object>} API response
   */
  static async deleteReminder(reminderId) {
    try {
      const response = await apiClient.delete(`/Remind/${reminderId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting reminder:', error);
      throw error;
    }
  }

  /**
   * Create appointment reminder for blood donation
   * @param {Object} appointmentData - Appointment data
   * @param {number} appointmentData.userId - User ID
   * @param {number} appointmentData.appointmentId - Appointment ID
   * @param {string} appointmentData.appointmentDate - Appointment date
   * @param {string} appointmentData.bloodType - Blood type
   * @param {string} appointmentData.donorName - Donor name
   * @param {string} appointmentData.donorEmail - Donor email
   * @param {string} appointmentData.donorPhone - Donor phone
   * @returns {Promise<Object>} API response
   */
  static async createAppointmentReminder(appointmentData) {
    const appointmentDate = new Date(appointmentData.appointmentDate);
    const formattedDate = appointmentDate.toLocaleDateString('vi-VN');
    const formattedTime = appointmentDate.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit'
    });

    // Schedule reminder for 1 day before appointment
    const reminderTime = new Date(appointmentDate);
    reminderTime.setDate(reminderTime.getDate() - 1);
    reminderTime.setHours(9, 0, 0, 0); // Send at 9 AM

    const reminderData = {
      userId: appointmentData.userId,
      message: `Nhắc nhở: Bạn có lịch hiến máu vào ${formattedDate} lúc ${formattedTime} với loại máu ${appointmentData.bloodType}. Vui lòng đến đúng giờ. Địa điểm: Bệnh viện Đa khoa Ánh Dương - Khoa Huyết học, Tầng 2`,
      type: 'BloodDonation', // Use database enum value
      scheduledTime: reminderTime.toISOString()
    };

    return this.createReminder(reminderData);
  }

  /**
   * Create manual reminder (sent immediately)
   * @param {Object} donationData - Donation data
   * @returns {Promise<Object>} API response
   */
  static async createManualReminder(donationData) {
    const appointmentDate = new Date(donationData.appointmentDate);
    const formattedDate = appointmentDate.toLocaleDateString('vi-VN');

    const reminderData = {
      userId: donationData.donorId,
      message: `Nhắc nhở: Bạn có lịch hiến máu vào ${formattedDate} với loại máu ${donationData.bloodType}. Vui lòng đến đúng giờ. Địa điểm: Bệnh viện Đa khoa Ánh Dương - Khoa Huyết học, Tầng 2`,
      type: 'BloodDonation', // Use database enum value
      scheduledTime: new Date().toISOString() // Send immediately
    };

    return this.createReminder(reminderData);
  }

  /**
   * Create donation eligibility reminder
   * @param {Object} userData - User data
   * @returns {Promise<Object>} API response
   */
  static async createDonationEligibilityReminder(userData) {
    const nextEligibleDate = this.calculateNextEligibleDate(userData.lastDonationDate, userData.gender);
    const daysUntilEligible = this.getDaysUntilEligible(userData.lastDonationDate, userData.gender);

    if (daysUntilEligible <= 3 && daysUntilEligible >= 0) {
      const message = daysUntilEligible === 0
        ? 'Bạn đã có thể hiến máu trở lại! Hãy đăng ký lịch hẹn ngay.'
        : `Còn ${daysUntilEligible} ngày nữa bạn có thể hiến máu trở lại.`;

      const reminderData = {
        userId: userData.userId,
        message: `🩸 ${message}`,
        type: 'Recovery', // Use Recovery type for eligibility reminders
        scheduledTime: new Date().toISOString()
      };

      return this.createReminder(reminderData);
    }
    return null;
  }

  /**
   * Calculate next eligible donation date
   * @param {string} lastDonationDate - Last donation date
   * @param {string} gender - User gender
   * @returns {Date} Next eligible date
   */
  static calculateNextEligibleDate(lastDonationDate, gender = 'male') {
    if (!lastDonationDate) return new Date();

    const lastDate = new Date(lastDonationDate);
    const intervalDays = gender === 'female' ? 112 : 84; // 16 weeks for female, 12 weeks for male

    const nextDate = new Date(lastDate);
    nextDate.setDate(nextDate.getDate() + intervalDays);

    return nextDate;
  }

  /**
   * Get days until next eligible donation
   * @param {string} lastDonationDate - Last donation date
   * @param {string} gender - User gender
   * @returns {number} Days until eligible
   */
  static getDaysUntilEligible(lastDonationDate, gender = 'male') {
    if (!lastDonationDate) return 0;

    const nextEligibleDate = this.calculateNextEligibleDate(lastDonationDate, gender);
    const today = new Date();
    const diffTime = nextEligibleDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }


}

export default RemindService;
