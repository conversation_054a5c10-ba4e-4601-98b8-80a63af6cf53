// Blood Request Table Styles - Synchronized with Blog Management
.blood-request-table {
  .ant-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  // Remove custom header styling - keep default

  .ant-table-tbody > tr {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9ff;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px;
    vertical-align: middle;
  }

  // Status badges - smaller size
  .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    display: inline-block;
    min-width: 60px;
    text-align: center;

    &.status-warning {
      background: #fff7e6;
      color: #fa8c16;
      border: 1px solid #ffd591;
    }

    &.status-success {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }

    &.status-info {
      background: #e6f7ff;
      color: #1890ff;
      border: 1px solid #91d5ff;
    }

    &.status-danger {
      background: #fff2f0;
      color: #ff4d4f;
      border: 1px solid #ffccc7;
    }

    &.status-secondary {
      background: #f5f5f5;
      color: #8c8c8c;
      border: 1px solid #d9d9d9;
    }
  }

  // Action buttons - synchronized with blog management
  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;

    .ant-btn {
      border-radius: 8px;
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 32px;
      height: 32px;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      &.view-btn {
        background: #e6f7ff;
        color: #1890ff;
        border: 1px solid #91d5ff;

        &:hover {
          background: #bae7ff;
          color: #1890ff;
          border-color: #69c0ff;
        }
      }

      &.accept-btn {
        background: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;

        &:hover {
          background: #d9f7be;
          color: #52c41a;
          border-color: #95de64;
        }
      }

      &.reject-btn {
        background: #fff2f0;
        color: #ff4d4f;
        border: 1px solid #ffccc7;

        &:hover {
          background: #ffccc7;
          color: #ff4d4f;
          border-color: #ff7875;
        }
      }

      .anticon {
        font-size: 14px;
      }
    }
  }

  // Blood type tags - synchronized with blood inventory management
  .blood-type-tag {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 12px;
    text-align: center;

    &.positive {
      background-color: #e3f2fd;
      color: #1976d2;
      border: 1px solid #bbdefb;
    }

    &.negative {
      background-color: #ffebee;
      color: #c62828;
      border: 1px solid #ffcdd2;
    }
  }

  // Patient info styling
  .patient-info {
    .patient-name {
      font-weight: 600;
      color: #1890ff;
      margin-bottom: 4px;
    }

    .patient-details {
      font-size: 12px;
      color: #666;
      display: flex;
      gap: 8px;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  // Doctor info styling
  .doctor-info {
    .doctor-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 2px;
    }

    .doctor-department {
      font-size: 12px;
      color: #666;
    }
  }

  // Quantity styling
  .quantity-display {
    font-weight: 600;
    color: #52c41a;
    font-size: 14px;

    .unit {
      font-size: 12px;
      color: #8c8c8c;
      margin-left: 2px;
    }
  }

  // Date styling
  .date-display {
    font-size: 13px;
    color: #666;

    .date-icon {
      margin-right: 4px;
      color: #1890ff;
    }
  }

  // Request ID styling
  .request-id {
    font-family: "Courier New", monospace;
    background: #f0f2f5;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    color: #1890ff;
    display: inline-block;
  }

  // Responsive design
  @media (max-width: 768px) {
    .action-buttons {
      flex-direction: column;
      gap: 4px;

      .ant-btn {
        width: 100%;
        min-width: auto;
      }
    }

    .ant-table-tbody > tr > td {
      padding: 12px 8px;
    }

    .patient-details {
      flex-direction: column;
      gap: 4px !important;
    }
  }

  // Loading state
  .ant-spin-container {
    min-height: 200px;
  }

  // Empty state
  .ant-empty {
    padding: 40px 20px;

    .ant-empty-description {
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  // Hover effects for rows
  .ant-table-tbody > tr:hover {
    .action-buttons .ant-btn {
      transform: scale(1.05);
    }
  }
}

// Animation keyframes
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.blood-request-table {
  animation: fadeInUp 0.3s ease-out;
}

// Tooltip customization
.ant-tooltip {
  .ant-tooltip-inner {
    background: #1890ff;
    border-radius: 6px;
    font-size: 12px;
    padding: 6px 8px;
  }

  .ant-tooltip-arrow::before {
    background: #1890ff;
  }
}
