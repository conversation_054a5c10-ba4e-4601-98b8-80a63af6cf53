import React, { useState } from "react";
import { Modal, Form, Input, Button, Alert } from "antd";
import "../../../styles/components/RejectRequestModal.scss";

/**
 * Modal để doctor kho<PERSON> huyết học nhập lý do từ chối yêu cầu máu
 */
const RejectRequestModal = ({
  isOpen,
  onClose,
  onConfirm,
  loading,
  requestInfo,
}) => {
  const [form] = Form.useForm();
  const [reason, setReason] = useState("");

  const handleSubmit = async () => {
    try {
      await form.validateFields();
      if (reason.trim()) {
        onConfirm(reason.trim());
        handleClose();
      }
    } catch (error) {
      console.error("Form validation failed:", error);
    }
  };

  const handleClose = () => {
    form.resetFields();
    setReason("");
    onClose();
  };

  return (
    <Modal
      title="❌ Từ chối yêu cầu máu"
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      width={600}
      destroyOnClose
      className="reject-request-modal"
    >
      <div className="request-info-alert">
        <Alert
          message="Xác nhận từ chối yêu cầu máu"
          description={
            requestInfo && (
              <div>
                <strong>Mã yêu cầu:</strong> #{requestInfo.requestID}
                <br />
                <strong>Bệnh nhân:</strong> {requestInfo.patientInfo?.name}
                <br />
                <strong>Nhóm máu:</strong> {requestInfo.bloodType}
                <br />
                <strong>Số lượng:</strong> {requestInfo.quantity} ml
              </div>
            )
          }
          type="warning"
          showIcon
        />
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="reason-form"
      >
        <Form.Item
          label="Lý do từ chối"
          name="reason"
          rules={[
            {
              required: true,
              message: "Vui lòng nhập lý do từ chối",
            },
            {
              min: 10,
              message: "Lý do phải có ít nhất 10 ký tự",
            },
            {
              max: 500,
              message: "Lý do không được vượt quá 500 ký tự",
            },
          ]}
        >
          <Input.TextArea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Nhập lý do từ chối yêu cầu máu (ví dụ: Không đủ máu trong kho, Thông tin bệnh nhân không đầy đủ, Yêu cầu không phù hợp...)"
            rows={4}
            showCount
            maxLength={500}
          />
        </Form.Item>

        <div className="modal-actions">
          <Button
            onClick={handleClose}
            style={{ marginRight: 8 }}
            disabled={loading}
          >
            Hủy
          </Button>
          <Button
            type="primary"
            danger
            onClick={handleSubmit}
            loading={loading}
            disabled={!reason.trim() || reason.trim().length < 10}
          >
            Xác nhận từ chối
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default RejectRequestModal;
