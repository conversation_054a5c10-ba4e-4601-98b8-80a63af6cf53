import React from 'react';
import { Card, Typography, Tag } from 'antd';

const { Text } = Typography;

/**
 * Test component để kiểm tra hiển thị thông tin sức khỏe trong lịch sử hiến máu
 */
const ActivityHistoryTest = () => {
  // Mock data để test hiển thị thông tin sức khỏe
  const mockDonationActivity = {
    id: "test-001",
    type: "donation",
    title: "Đặt lịch hiến máu",
    status: "completed",
    bloodType: "O+",
    quantity: "450ml",
    appointmentDate: "2024-12-15T09:00:00Z",
    timeSlot: "Sáng (7:00-12:00)",
    location: "Bệnh viện Đa khoa Ánh Dương - Khoa Hu<PERSON> học, Tầng 2",
    notes: "Hiến máu thành công",
    doctorNotes: "Người hiến máu có sức khỏe tốt, các chỉ số đều trong giới hạn bình thường. <PERSON><PERSON><PERSON><PERSON><PERSON> khích tiếp tục hiến máu định kỳ.",
    weight: 65,
    height: 170,
    hasDonated: true,
    lastDonationDate: "2024-06-15T09:00:00Z",
    createdAt: "2024-12-10T14:30:00Z",
    completedAt: "2024-12-15T10:30:00Z",
    isCancelled: false,
    // Thông tin sức khỏe chi tiết từ bác sĩ
    healthCheck: {
      heartRate: "72",
      bloodPressure: "120/80",
      hemoglobin: "14.5",
      temperature: "36.5",
      weight: 65,
      height: 170,
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🧪 Test: Hiển thị thông tin sức khỏe trong lịch sử hiến máu</h2>
      
      {/* Test Health Check Info Display */}
      <Card
        title="🏥 Thông tin khám sức khỏe"
        style={{ marginBottom: 16 }}
      >
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
          {/* Cân nặng */}
          <div className="detail-item">
            <Text strong style={{ color: "#1976d2" }}>⚖️ Cân nặng:</Text>
            <br />
            <Text style={{ fontSize: "16px", fontWeight: "600", color: "#d32f2f" }}>
              {mockDonationActivity.healthCheck.weight} kg
            </Text>
          </div>

          {/* Chiều cao */}
          <div className="detail-item">
            <Text strong style={{ color: "#1976d2" }}>📏 Chiều cao:</Text>
            <br />
            <Text style={{ fontSize: "16px", fontWeight: "600", color: "#d32f2f" }}>
              {mockDonationActivity.healthCheck.height} cm
            </Text>
          </div>

          {/* Nhịp tim */}
          <div className="detail-item">
            <Text strong style={{ color: "#1976d2" }}>💓 Nhịp tim:</Text>
            <br />
            <Text style={{ fontSize: "16px", fontWeight: "600", color: "#d32f2f" }}>
              {mockDonationActivity.healthCheck.heartRate} bpm
            </Text>
          </div>

          {/* Huyết áp */}
          <div className="detail-item">
            <Text strong style={{ color: "#1976d2" }}>🩸 Huyết áp:</Text>
            <br />
            <Text style={{ fontSize: "16px", fontWeight: "600", color: "#d32f2f" }}>
              {mockDonationActivity.healthCheck.bloodPressure} mmHg
            </Text>
          </div>

          {/* Huyết sắc tố */}
          <div className="detail-item">
            <Text strong style={{ color: "#1976d2" }}>🔬 Huyết sắc tố:</Text>
            <br />
            <Text style={{ fontSize: "16px", fontWeight: "600", color: "#d32f2f" }}>
              {mockDonationActivity.healthCheck.hemoglobin} g/dL
            </Text>
          </div>

          {/* Nhiệt độ */}
          <div className="detail-item">
            <Text strong style={{ color: "#1976d2" }}>🌡️ Nhiệt độ:</Text>
            <br />
            <Text style={{ fontSize: "16px", fontWeight: "600", color: "#d32f2f" }}>
              {mockDonationActivity.healthCheck.temperature}°C
            </Text>
          </div>

          {/* Lịch sử hiến máu */}
          <div className="detail-item">
            <Text strong style={{ color: "#1976d2" }}>🩸 Đã hiến máu:</Text>
            <br />
            <Tag color={mockDonationActivity.hasDonated ? "success" : "default"} style={{ fontSize: "14px" }}>
              {mockDonationActivity.hasDonated ? "✅ Có" : "❌ Không"}
            </Tag>
          </div>

          {/* Lần hiến máu gần nhất */}
          <div className="detail-item">
            <Text strong style={{ color: "#1976d2" }}>📅 Lần hiến máu gần nhất:</Text>
            <br />
            <Text style={{ fontSize: "15px", color: "#666" }}>
              {formatDate(mockDonationActivity.lastDonationDate)}
            </Text>
          </div>
        </div>

        {/* Ghi chú của bác sĩ */}
        <div style={{ marginTop: '20px' }}>
          <Text strong style={{ color: "#1976d2" }}>👨‍⚕️ Ghi chú của bác sĩ:</Text>
          <br />
          <div
            style={{
              background: "linear-gradient(135deg, #f0f8ff, #e6f3ff)",
              padding: "16px",
              borderRadius: "12px",
              border: "2px solid #d1ecf1",
              marginTop: "8px",
              boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
            }}
          >
            <Text style={{ fontSize: "15px", lineHeight: "1.6" }}>
              {mockDonationActivity.doctorNotes}
            </Text>
          </div>
        </div>
      </Card>

      {/* Test Summary */}
      <Card title="📋 Tóm tắt thông tin được hiển thị" type="inner">
        <ul>
          <li><strong>Cân nặng:</strong> {mockDonationActivity.healthCheck.weight} kg</li>
          <li><strong>Chiều cao:</strong> {mockDonationActivity.healthCheck.height} cm</li>
          <li><strong>Nhịp tim:</strong> {mockDonationActivity.healthCheck.heartRate} bpm</li>
          <li><strong>Huyết áp:</strong> {mockDonationActivity.healthCheck.bloodPressure} mmHg</li>
          <li><strong>Huyết sắc tố:</strong> {mockDonationActivity.healthCheck.hemoglobin} g/dL</li>
          <li><strong>Nhiệt độ:</strong> {mockDonationActivity.healthCheck.temperature}°C</li>
          <li><strong>Ghi chú bác sĩ:</strong> {mockDonationActivity.doctorNotes}</li>
        </ul>
      </Card>

      <div style={{ marginTop: '20px', padding: '16px', background: '#f0f9ff', borderRadius: '8px', border: '1px solid #bae6fd' }}>
        <Text strong style={{ color: '#0369a1' }}>
          ✅ Test thành công! Modal lịch sử hiến máu sẽ hiển thị đầy đủ thông tin sức khỏe mà bác sĩ đã điền.
        </Text>
      </div>
    </div>
  );
};

export default ActivityHistoryTest;
