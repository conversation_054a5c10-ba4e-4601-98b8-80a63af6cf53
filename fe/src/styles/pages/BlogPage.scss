@use "../base/variables" as vars;
@use "../base/mixin" as mix;

.article-group {
  margin-bottom: 48px;
}

.article-group-title {
  color: #1890ff;
  font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 24px;
  letter-spacing: 1px;
}

.ant-divider {
  background: #e6f7ff;
}

.ant-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.article-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 2px 16px rgba(24, 144, 255, 0.08);
  transition: box-shadow 0.3s, transform 0.3s;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 340px;
  font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;

  &:hover {
    box-shadow: 0 8px 32px rgba(24, 144, 255, 0.18);
    transform: scale(1.025);
  }

  .ant-card-cover img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;
    transition: transform 0.4s;
  }

  .ant-card-meta-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1890ff;
    margin-bottom: 8px;
    font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  }

  .ant-card-meta-description {
    color: #595959;
    font-size: 1rem;
    margin-bottom: 12px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    max-height: 2.8em; // 2 dòng
    font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  }
}

// Responsive
@media (max-width: 900px) {
  .article-card {
    min-height: 280px;
    .ant-card-cover img {
      height: 140px;
    }
  }
}

// =============================
// Article Detail Page
// =============================

.article-detail-page {
  background: #e6f7ff;
  min-height: 100vh;
  padding: 32px 0;
  font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;

  .ant-card {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 4px 24px rgba(24, 144, 255, 0.1);
    padding: 0 0 24px 0;
    max-width: 800px;
    margin: 0 auto;
  }

  .ant-card-head-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: #1890ff;
    text-align: center;
    font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  }

  .ant-card-cover img {
    display: block;
    margin: 0 auto;
    max-height: 340px;
    border-radius: 16px;
    object-fit: cover;
    box-shadow: 0 2px 12px rgba(24, 144, 255, 0.1);
  }

  .ant-card-body {
    padding: 32px 24px 16px 24px;
  }

  .article-content {
    color: #222;
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 24px;
    font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  }

  .article-tags {
    margin-top: 16px;
    .ant-tag {
      background: #e6f7ff;
      color: #1890ff;
      border: none;
      font-weight: 500;
      font-size: 1rem;
      margin-right: 8px;
      margin-bottom: 4px;
      border-radius: 8px;
      padding: 4px 14px;
    }
  }

  .article-author {
    color: #595959;
    font-size: 1rem;
    margin-bottom: 12px;
    font-style: italic;
    text-align: right;
  }
}

// Button style
.ant-btn-primary,
.read-more-btn {
  background: #1890ff;
  border: none;
  color: #fff;
  font-weight: 600;
  border-radius: 8px;
  padding: 8px 20px;
  font-size: 1rem;
  transition: background 0.3s, box-shadow 0.3s;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);

  &:hover,
  &:focus {
    background: #1765ad;
    color: #fff;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.18);
  }
}

// =============================
// Page Header
// =============================

.page-header {
  position: relative;
  height: 240px;
  background: linear-gradient(
      135deg,
      rgba(66, 153, 225, 0.1) 0%,
      transparent 50%
    ),
    linear-gradient(225deg, rgba(44, 96, 192, 0.15) 0%, transparent 50%),
    linear-gradient(45deg, rgba(26, 54, 93, 0.2) 0%, transparent 50%),
    linear-gradient(135deg, #0f1e3d 0%, #2c64b4 100%);
  background-size: 300% 300%, 250% 250%, 200% 200%, cover;
  background-position: -50% -50%, 0% 0%, 50% 50%, center center;
  background-repeat: no-repeat;
  margin-bottom: 48px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);

  .header-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    padding: 0 24px;
    text-align: center;
    justify-content: center;

    h1 {
      font-family: "Roboto", sans-serif;
      font-size: 2.8rem;
      font-weight: 700;
      margin-bottom: 16px;
      letter-spacing: 0.5px;
      text-transform: uppercase;
      background: linear-gradient(to right, #ffffff, #e2e8f0);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    p {
      font-family: "Roboto", sans-serif;
      font-size: 1.2rem;
      font-weight: 400;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.9);
      max-width: 600px;
      margin: 0 auto;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
}

.carousel-section {
  width: 100vw;
  max-width: none;
  margin: 0 0 40px 0;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;

  .carousel-card {
    min-height: 320px;

    .ant-card-cover img {
      width: 100vw;
      height: 260px;
      object-fit: cover;
    }
  }
}

.pagination-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin: 32px 0 0 0;
  gap: 24px;
  width: 100%;

  .pagination-total {
    flex: 1;
    color: #888;
    font-size: 1rem;
    font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
    text-align: left;
  }

  .ant-pagination {
    flex-shrink: 0;

    .ant-pagination-item {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      background: white;
      transition: all 0.3s ease;
      margin: 0 4px;

      a {
        color: #1e293b;
        font-weight: 500;
        font-family: "Roboto", sans-serif;
      }

      &:hover {
        border-color: #2b6cb0;
        background: #f8fafc;
      }
    }

    .ant-pagination-item-active {
      border-radius: 8px;
      background: linear-gradient(135deg, #2b6cb0, #4299e1);
      border-color: #4299e1;

      a {
        color: white;
      }

      &:hover {
        border-color: #4299e1;
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      background: white;
      transition: all 0.3s ease;

      .ant-pagination-item-link {
        color: #1e293b;
        border-radius: 8px;
      }

      &:hover {
        border-color: #2b6cb0;
        background: #f8fafc;
      }
    }

    .ant-pagination-disabled {
      .ant-pagination-item-link {
        color: #94a3b8;
      }
    }
  }
}

.no-news {
  text-align: center;
  color: #888;
  font-size: 1.1rem;
  margin: 32px 0;
}

// Add styles for HTML content in blog detail pages
.content-html {
  color: #2d3748;
  line-height: 1.8;
  font-size: 1.1rem;

  p {
    margin-bottom: 1.5em;
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1.5em 0;
    display: block;
  }

  h2,
  h3,
  h4 {
    color: #1a365d;
    margin: 1.5em 0 0.75em;
  }

  h2 {
    font-size: 1.8rem;
    font-weight: 700;
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
  }

  h4 {
    font-size: 1.3rem;
    font-weight: 500;
  }

  ul,
  ol {
    margin: 1.5em 0;
    padding-left: 2em;
  }

  li {
    margin-bottom: 0.5em;
  }

  blockquote {
    border-left: 4px solid #4299e1;
    padding-left: 1em;
    margin: 1.5em 0;
    color: #666;
    font-style: italic;
  }

  code {
    background: #f1f1f1;
    padding: 0.2em 0.4em;
    border-radius: 4px;
    font-family: "Courier New", monospace;
  }

  pre {
    background: #f1f1f1;
    padding: 1em;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1.5em 0;

    code {
      background: none;
      padding: 0;
    }
  }
}

.content-paragraph {
  margin-bottom: 1.5em;
  line-height: 1.8;
  color: #2d3748;
}
