@use "../base/variables" as vars;

.location-picker {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 15px;
  border: 1px solid #e9ecef;
  margin-bottom: 2rem;

  .location-input-section {
    margin-bottom: 2rem;

    .location-label {
      display: block;
      margin-bottom: 1rem;
      font-weight: 700;
      color: #495057;
      font-size: 1.1rem;

      .required {
        color: #dc3545;
        margin-left: 0.25rem;
      }
    }

    .input-group {
      display: flex;
      gap: 1rem;
      align-items: flex-start;

      .address-input {
        flex: 1;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        resize: vertical;
        min-height: 80px;

        &:focus {
          outline: none;
          border-color: #17a2b8;
          box-shadow: 0 0 0 4px rgba(23, 162, 184, 0.1);
          transform: translateY(-2px);
        }

        &:disabled {
          background: #e9ecef;
          cursor: not-allowed;
        }

        &::placeholder {
          color: #adb5bd;
        }

        &.geocoding {
          border-color: #ffc107;
          background-color: #fff3cd;
        }
      }

      .geocoding-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 6px;
        color: #856404;
        font-size: 0.9rem;

        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid #ffeaa7;
          border-top: 2px solid #856404;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }

      .current-location-btn {
        background: linear-gradient(45deg, #17a2b8, #20c997);
        color: white;
        border: none;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }

    .error-message {
      margin-top: 1rem;
      padding: 1rem;
      background: linear-gradient(135deg, #f8d7da, #f5c6cb);
      color: #721c24;
      border-radius: 10px;
      border: 1px solid #f5c6cb;
      font-weight: 600;
    }
  }

  .distance-info-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    margin-bottom: 1.5rem;

    h4 {
      margin: 0 0 1.5rem 0;
      background: linear-gradient(45deg, #17a2b8, #20c997);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 1.2rem;
      font-weight: 700;
    }

    .distance-details {
      .distance-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;

        &:last-child {
          border-bottom: none;
        }

        .distance-label {
          font-weight: 600;
          color: #6c757d;
        }

        .distance-value {
          font-weight: 700;
          font-size: 1.1rem;
        }

        .travel-time {
          background: linear-gradient(45deg, #28a745, #20c997);
          color: white;
          padding: 0.5rem 0.75rem;
          border-radius: 10px;
          font-weight: 700;
          box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .priority-badge {
          padding: 0.5rem 0.75rem;
          border-radius: 10px;
          font-weight: 700;
          font-size: 0.9rem;
          text-transform: uppercase;

          &.priority-very_high {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
          }

          &.priority-high {
            background: linear-gradient(45deg, #fd7e14, #e55a4e);
            color: white;
            box-shadow: 0 4px 15px rgba(253, 126, 20, 0.3);
          }

          &.priority-medium {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: #212529;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
          }

          &.priority-low {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
          }

          &.priority-very_low {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
          }
        }
      }
    }

    .hospital-info {
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid #e9ecef;

      h5 {
        margin: 0 0 1rem 0;
        color: #495057;
        font-size: 1.1rem;
        font-weight: 700;
      }

      .hospital-details {
        margin-bottom: 1rem;

        .hospital-item {
          margin-bottom: 0.5rem;
          color: #6c757d;
          font-weight: 500;

          &:first-child {
            color: #495057;
            font-weight: 700;
            font-size: 1.1rem;
          }
        }
      }

      .directions-link {
        display: inline-block;
        background: linear-gradient(45deg, #17a2b8, #20c997);
        color: white;
        text-decoration: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 700;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
          text-decoration: none;
          color: white;
        }
      }
    }
  }

  .location-preview {
    background: linear-gradient(135d, #e3f2fd, #bbdefb);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #2196f3;

    h4 {
      margin: 0 0 1rem 0;
      color: #1976d2;
      font-size: 1.1rem;
      font-weight: 700;
    }

    .coordinates {
      margin-bottom: 1rem;
      font-family: "Courier New", monospace;
      background: rgba(255, 255, 255, 0.7);
      padding: 0.75rem;
      border-radius: 8px;
      font-weight: 600;
      color: #495057;
    }

    .accuracy-note {
      background: rgba(255, 255, 255, 0.9);
      padding: 1rem;
      border-radius: 10px;
      border-left: 4px solid #2196f3;
      color: #495057;
      line-height: 1.5;

      strong {
        color: #1976d2;
      }
    }
  }
}

// Animations
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .location-picker {
    padding: 1.5rem;

    .location-input-section {
      .input-group {
        flex-direction: column;

        .current-location-btn {
          align-self: stretch;
          justify-content: center;
        }
      }
    }

    .distance-info-section {
      .distance-details .distance-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }
  }
}
