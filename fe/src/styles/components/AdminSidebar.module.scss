@use "../base/variables" as vars;
@use "../base/mixin" as mix;

.admin-sidebar {
  position: fixed !important;
  left: 0;
  top: 0;
  bottom: 0;
  height: 100vh;
  z-index: 1000;
  background: vars.$manager-bg !important;
  border-right: 1px solid vars.$manager-border;
  box-shadow: 2px 0 8px vars.$manager-shadow;

  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .sidebar-header {
    padding: vars.$spacing-lg;
    border-bottom: 1px solid vars.$manager-border;
    background: vars.$manager-bg;

    .logo {
      @include mix.flex-align(center, center);
      gap: vars.$spacing-sm;
      text-decoration: none;

      img {
        height: 40px;
        width: auto;
      }

      h1 {
        margin: 0;
        font-size: vars.$font-size-lg;
        font-weight: vars.$font-weight-semibold;
        color: vars.$manager-text;
        font-family: vars.$font-manager;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .sidebar-menu {
    flex: 1;
    overflow-y: auto;
    padding: vars.$spacing-md 0;
    background: transparent !important;
    border-right: none;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: vars.$manager-border;
      border-radius: 3px;
    }

    .ant-menu-item {
      margin: 4px vars.$spacing-sm;
      border-radius: vars.$border-radius-md;
      height: 48px;
      line-height: 48px;
      font-family: vars.$font-manager;

      &:hover {
        background: vars.$manager-bg-hover !important;
      }

      &.ant-menu-item-selected {
        background: vars.$manager-primary !important;
        color: vars.$manager-text-light !important;

        &::after {
          display: none;
        }

        .anticon {
          color: vars.$manager-text-light !important;
        }
      }

      .anticon {
        font-size: vars.$font-size-lg;
        color: vars.$manager-text;
      }
    }
  }

  .sidebar-footer {
    padding: vars.$spacing-md;
    border-top: 1px solid vars.$manager-border;
    background: vars.$manager-bg;

    .user-info {
      @include mix.flex-align(center, center);
      gap: vars.$spacing-sm;
      padding: vars.$spacing-sm;
      border-radius: vars.$border-radius-md;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: vars.$manager-bg-hover;
      }

      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: vars.$manager-primary;
        @include mix.flex-align(center, center);
        color: vars.$manager-text-light;
        font-weight: vars.$font-weight-semibold;
        font-size: vars.$font-size-sm;
      }

      .user-details {
        flex: 1;
        min-width: 0;

        .user-name {
          margin: 0;
          font-size: vars.$font-size-base;
          font-weight: vars.$font-weight-semibold;
          color: vars.$manager-text;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .user-role {
          margin: 0;
          font-size: vars.$font-size-sm;
          color: vars.$manager-text-light;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .logout-icon {
        color: vars.$manager-text;
        font-size: vars.$font-size-lg;
        transition: all 0.3s ease;

        &:hover {
          color: vars.$manager-danger;
        }
      }
    }
  }
}

// Responsive Design
@include mix.tablet {
  .admin-sidebar {
    .sidebar-header {
      padding: vars.$spacing-md;

      .logo {
        h1 {
          display: none;
        }
      }
    }

    .sidebar-menu {
      .ant-menu-item {
        margin: 4px vars.$spacing-xs;
        padding: 0 vars.$spacing-sm !important;

        span {
          display: none;
        }
      }
    }

    .sidebar-footer {
      padding: vars.$spacing-sm;

      .user-info {
        justify-content: center;
        padding: vars.$spacing-xs;

        .user-details {
          display: none;
        }
      }
    }
  }
}

@include mix.mobile {
  .admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.ant-layout-sider-collapsed {
      transform: translateX(0);
    }
  }
}
