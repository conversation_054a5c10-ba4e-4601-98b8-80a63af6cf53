.user-type-selection {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;

  .selection-container {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    max-width: 1000px;
    width: 100%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .selection-header {
    text-align: center;
    margin-bottom: 3rem;

    h1 {
      font-size: 2.5rem;
      color: #333;
      margin: 0 0 1rem 0;
      font-weight: 700;
    }

    p {
      font-size: 1.2rem;
      color: #666;
      margin: 0;
      line-height: 1.5;
    }
  }

  .type-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .type-card {
    position: relative;
    background: #f8f9fa;
    border: 3px solid #e9ecef;
    border-radius: 16px;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      border-color: #007bff;
    }

    &.selected {
      border-color: #007bff;
      background: linear-gradient(135deg, #007bff, #0056b3);
      color: white;
      transform: translateY(-5px);
      box-shadow: 0 15px 35px rgba(0, 123, 255, 0.3);

      .type-description {
        color: rgba(255, 255, 255, 0.9);
      }

      .type-features {
        h3 {
          color: white;
        }

        ul li {
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }

    .type-icon {
      font-size: 4rem;
      text-align: center;
      margin-bottom: 1rem;
    }

    h2 {
      font-size: 1.8rem;
      font-weight: 700;
      margin: 0 0 1rem 0;
      text-align: center;
    }

    .type-description {
      font-size: 1.1rem;
      color: #666;
      text-align: center;
      margin-bottom: 2rem;
      line-height: 1.5;
    }

    .type-features {
      h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin: 0 0 1rem 0;
        color: #333;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          position: relative;
          padding: 0.5rem 0 0.5rem 2rem;
          color: #555;
          line-height: 1.4;

          &::before {
            content: "✓";
            position: absolute;
            left: 0;
            top: 0.5rem;
            color: #28a745;
            font-weight: bold;
            font-size: 1.1rem;
          }
        }
      }
    }

    .selection-indicator {
      position: absolute;
      top: 1rem;
      right: 1rem;

      .checkmark {
        width: 2.5rem;
        height: 2.5rem;
        background: #28a745;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
        animation: checkmarkPop 0.3s ease;
      }
    }
  }

  .selection-actions {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;

    .btn {
      padding: 1rem 3rem;
      border: none;
      border-radius: 10px;
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 150px;

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      &.btn-outline {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;

        &:hover:not(:disabled) {
          background: #6c757d;
          color: white;
          transform: translateY(-2px);
        }
      }

      &.btn-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border: 2px solid transparent;

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #0056b3, #004085);
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
        }

        &:disabled {
          background: #6c757d;
        }
      }
    }
  }

  .help-text {
    text-align: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #17a2b8;

    p {
      margin: 0;
      color: #555;
      font-size: 0.95rem;
      line-height: 1.5;

      strong {
        color: #17a2b8;
      }
    }
  }
}

@keyframes checkmarkPop {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@media (max-width: 768px) {
  .user-type-selection {
    padding: 1rem;

    .selection-container {
      padding: 2rem;
    }

    .selection-header {
      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .type-options {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .type-card {
      padding: 1.5rem;

      .type-icon {
        font-size: 3rem;
      }

      h2 {
        font-size: 1.5rem;
      }

      .type-description {
        font-size: 1rem;
      }
    }

    .selection-actions {
      flex-direction: column;
      gap: 1rem;

      .btn {
        width: 100%;
      }
    }
  }
}
