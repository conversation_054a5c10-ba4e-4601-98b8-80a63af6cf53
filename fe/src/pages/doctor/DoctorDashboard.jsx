import React from "react";
import { <PERSON>, <PERSON><PERSON>, But<PERSON> } from "antd";
import { ReloadOutlined } from "@ant-design/icons";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import WelcomeBanner from "../../components/doctor/dashboard/WelcomeBanner";
import useDoctorDashboard from "../../hooks/useDoctorDashboard";
import "../../styles/pages/DoctorDashboard.scss";

import StatisticsCards from "../../components/doctor/dashboard/StatisticsCards";
import ChartsSection from "../../components/doctor/dashboard/ChartsSection";

const DoctorDashboard = () => {
  const {
    loading,
    error,
    dashboardData,
    isBloodDepartment,
    currentUser,
    refreshDashboard,
    markNotificationAsRead,
  } = useDoctorDashboard();

  // Handle loading state
  if (loading) {
    return (
      <DoctorLayout>
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "400px",
          }}
        >
          <Spin size="large" />
          <span style={{ marginLeft: 16 }}><PERSON><PERSON> tải dữ liệu dashboard...</span>
        </div>
      </DoctorLayout>
    );
  }

  // Handle error state
  if (error) {
    return (
      <DoctorLayout>
        <Alert
          message="Lỗi tải dữ liệu"
          description={error}
          type="error"
          showIcon
          action={
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={refreshDashboard}
            >
              Thử lại
            </Button>
          }
          style={{ marginBottom: 24 }}
        />
      </DoctorLayout>
    );
  }

  return (
    <DoctorLayout>
      <div className="doctor-dashboard-content">
        {/* Welcome Banner */}
        <WelcomeBanner />

        {/* Statistics Cards */}
        <StatisticsCards
          statistics={dashboardData.statistics}
          isBloodDepartment={isBloodDepartment}
        />

        {/* Charts Section */}
        <ChartsSection
          bloodGroupData={dashboardData.bloodInventory}
          recentRequests={dashboardData.recentRequests}
        />
      </div>
    </DoctorLayout>
  );
};

export default DoctorDashboard;
