import { Card, Row, Col, Statistic } from "antd";

/**
 * Component hiển thị thống kê người hiến máu
 * Đồng bộ với style của trang yêu cầu máu
 */
const DonorStatistics = ({ statistics }) => {
  const {
    todayCount,
    pendingCount,
    approvedCount,
    rejectedCount,
    cancelledCount,
    totalCount
  } = statistics;

  // Định nghĩa stats với màu sắc giống BloodRequestStats
  const stats = [
    {
      title: "Tổng cộng",
      value: totalCount,
      color: "#1677ff",
    },
    {
      title: "Chờ duyệt",
      value: pendingCount,
      color: "#faad14",
    },
    {
      title: "Chấp nhận",
      value: approvedCount,
      color: "#52c41a",
    },
    {
      title: "Hôm nay",
      value: todayCount,
      color: "#1890ff",
    },
  ];

  // Stats bổ sung
  const additionalStats = [
    {
      title: "Không chấp nhận",
      value: rejectedCount,
      color: "#ff4d4f",
    },
    {
      title: "Đ<PERSON> hủy",
      value: cancelledCount,
      color: "#8c8c8c",
    },
  ];

  return (
    <>
      {/* Thống kê chính */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {stats.map((stat) => (
          <Col xs={24} sm={12} md={6} key={stat.title}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                valueStyle={{ color: stat.color, fontWeight: 600 }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Thống kê bổ sung */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {additionalStats.map((stat) => (
          <Col xs={24} sm={12} md={6} key={stat.title}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                valueStyle={{ color: stat.color, fontWeight: 600 }}
              />
            </Card>
          </Col>
        ))}
      </Row>
    </>
  );
};

export default DonorStatistics;
