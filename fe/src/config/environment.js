// Environment configuration
const config = {
  // API configuration - all URLs come from .env.local
  api: {
    baseUrl: import.meta.env.VITE_API_URL || "https://localhost:7021/api",
    news: import.meta.env.VITE_NEWS_API || "https://localhost:7021/api/News",
    bloodArticles:
      import.meta.env.VITE_BLOOD_ARTICLES_API ||
      "https://localhost:7021/api/BloodArticles",
    auth: import.meta.env.VITE_AUTH_API || "https://localhost:7021/api/Auth",
    information:
      import.meta.env.VITE_INFORMATION_API ||
      "https://localhost:7021/api/Information",
    nominatim:
      import.meta.env.VITE_NOMINATIM_API ||
      "https://nominatim.openstreetmap.org",
    activityLog:
      import.meta.env.VITE_ACTIVITY_LOG_API ||
      "https://localhost:7021/api/ActivityLog/admin",
    googleLogin:
      import.meta.env.VITE_GOOGLE_LOGIN_API ||
      "https://localhost:7021/api/Auth/google-login",
    googleCallback:
      import.meta.env.VITE_GOOGLE_CALLBACK_API ||
      "https://localhost:7021/api/Auth/google-callback",
    bloodInventory:
      import.meta.env.VITE_BLOOD_INVENTORY_API ||
      "https://localhost:7021/api/BloodInventory",
    bloodDonation:
      import.meta.env.VITE_BLOOD_DONATION_API ||
      "https://localhost:7021/api/blood-donation-submissions",
    bloodRequest:
      import.meta.env.VITE_BLOOD_REQUEST_API ||
      "https://localhost:7021/api/BloodRequest",
    patient:
      import.meta.env.VITE_PATIENT_API || "https://localhost:7021/api/Patient",
  },

  // Application environment
  app: {
    environment: import.meta.env.VITE_ENVIRONMENT || "development",
    isDevelopment: function () {
      return this.environment === "development";
    },
    isProduction: function () {
      return this.environment === "production";
    },
  },

  // Hospital information (static data)
  hospital: {
    name: "Bệnh viện Đa khoa Ánh Dương",
    address: "Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam",
    coordinates: {
      lat: 10.7751237,
      lng: 106.6862143,
    },
  },
};

export default config;
