@use '../base/variables' as vars;
@use '../base/mixin' as mix;

.manager-dashboard {
  width: 100%;
  font-family: vars.$font-manager;
  
  .dashboard-content {
    height: 100%;
    width: 100%;
    max-width: 1600px;
    margin: 0 auto;
      display: grid;
      gap: vars.$spacing-lg;
      max-width: 100%; // Changed to full width
      margin: 0;
      padding: 20px; // Added padding for content spacing

      // Welcome Banner
      .welcome-banner {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: vars.$spacing-lg;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

        .welcome-content {
          display: flex;
          align-items: center;
          gap: vars.$spacing-lg;
          color: white;

          .welcome-icon {
            font-size: 48px;
            background: rgba(255, 255, 255, 0.2);
            padding: vars.$spacing-md;
            border-radius: 50%;
          }

          .welcome-text {
            h2 {
              color: white !important;
              margin-bottom: vars.$spacing-sm !important;
            }

            .ant-typography {
              color: rgba(255, 255, 255, 0.85);
            }
          }
        }
      }

      // Statistics Cards
      .statistics-cards {
        .ant-card {
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          .ant-statistic {
            .ant-statistic-title {
              color: vars.$manager-text-light;
              font-size: 1rem;
              margin-bottom: vars.$spacing-sm;
            }

            .ant-statistic-content {
              font-size: 2rem;
              font-weight: 600;
            }
          }
        }
      }

      // Charts Section - Use same layout as doctor dashboard
      // Remove custom styling to use shared ChartsSection component styling

      // Notifications Panel
      .notifications-panel {
        background: white;
        border-radius: 12px;
        padding: vars.$spacing-lg;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .notification-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: vars.$spacing-md;

          h3 {
            margin: 0;
            color: vars.$manager-text;
          }
        }

        .ant-list-item {
          padding: vars.$spacing-md;
          border-radius: 8px;
          margin-bottom: vars.$spacing-sm;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.02);
          }
        }
      }
    }
  }
}

// Responsive Design
@include mix.tablet {
  .manager-dashboard {
    .dashboard-main {
      margin-left: 0;
      padding: vars.$spacing-md;

      &.collapsed {
        margin-left: 80px;
      }

      .dashboard-content {
        padding: vars.$spacing-sm;

        .statistics-cards {
          .ant-col {
            flex: 0 0 100%;
            max-width: 100%;
            margin-bottom: vars.$spacing-md;
          }
        }

        .welcome-banner {
          .welcome-content {
            flex-direction: column;
            text-align: center;
            gap: vars.$spacing-md;

            .welcome-text {
              h2 {
                font-size: 1.5rem;
              }
            }
          }
        }
      }
    }
  }
}

@include mix.mobile {
  .manager-dashboard {
    .dashboard-main {
      margin-left: 0;
      padding: vars.$spacing-sm;

      .dashboard-content {
        padding: vars.$spacing-xs;

        .welcome-banner {
          padding: vars.$spacing-md;
        }

        // Charts section styling handled by shared component

        .notifications-panel {
          padding: vars.$spacing-md;

          .notification-header {
            flex-direction: column;
            gap: vars.$spacing-sm;
            text-align: center;
          }
        }
      }
    }
  }
}
