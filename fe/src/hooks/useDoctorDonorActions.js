import { message } from "antd";
import bloodDonationService from "../services/bloodDonationService";
import NotificationService from "../services/notificationService";

/**
 * Custom hook for handling doctor donor management actions (save, update, etc.)
 */
export const useDoctorDonorActions = () => {

  // Save donor info update (Doctor Update Modal)
  const handleSaveUpdate = async (selectedDonor, updateData, currentUser, onSuccess, setShowUpdateModal, setSelectedDonor) => {
    if (!selectedDonor) return;

    try {
      // Use POST /api/Appointment/doctor-update/{id} with proper DTO format (PascalCase)
      const doctorUpdatePayload = {
        Notes: updateData.notes || "",
        BloodPressure: updateData.bloodPressure || "",
        HeartRate: parseInt(updateData.heartRate) || 0,
        Hemoglobin: parseFloat(updateData.hemoglobin) || 0,
        Temperature: parseFloat(updateData.temperature) || 0,
        WeightAppointment: parseFloat(updateData.WeightAppointment || updateData.weightAppointment || updateData.weight) || 0,
        HeightAppointment: parseFloat(updateData.HeightAppointment || updateData.heightAppointment || updateData.height) || 0,
        DoctorId: parseInt(currentUser?.id) || 0,
        Process: updateData.process || 2,
        Status: updateData.status !== undefined ? updateData.status : true,
      };

      console.log('UpdateData received:', updateData);
      console.log('Weight values:', {
        WeightAppointment: updateData.WeightAppointment,
        weight: updateData.weight,
        final: parseFloat(updateData.WeightAppointment || updateData.weight) || 0
      });
      console.log('Height values:', {
        HeightAppointment: updateData.HeightAppointment,
        height: updateData.height,
        final: parseFloat(updateData.HeightAppointment || updateData.height) || 0
      });
      console.log('Doctor update payload (PascalCase DTO):', doctorUpdatePayload);
      console.log('Weight/Height in payload:', {
        WeightAppointment: doctorUpdatePayload.WeightAppointment,
        HeightAppointment: doctorUpdatePayload.HeightAppointment
      });

      await bloodDonationService.doctorUpdateAppointment(selectedDonor.id, doctorUpdatePayload);



      // Call success callback to refresh data
      if (onSuccess) {
        onSuccess();
      }

      setShowUpdateModal(false);
      setSelectedDonor(null);
      message.success("Cập nhật thông tin thành công!");
    } catch (error) {
      console.error("Failed to update donor information:", error);
      message.error("Có lỗi xảy ra khi cập nhật thông tin!");
    }
  };

  // Save status update
  const handleSaveStatusUpdate = async (selectedDonor, statusUpdateData, currentUser, onSuccess, setShowStatusModal, setSelectedDonor) => {
    if (!selectedDonor) return;

    try {
      const statusToSend = typeof statusUpdateData.status === 'string' ?
        parseInt(statusUpdateData.status) : statusUpdateData.status;

      const processToSend = typeof statusUpdateData.process === 'string' ?
        parseInt(statusUpdateData.process) : statusUpdateData.process;



      // Update process if changed
      if (processToSend && processToSend !== selectedDonor.process) {
        try {
          await bloodDonationService.updateAppointmentProcess(
            selectedDonor.id,
            processToSend,
            "", // Don't send notes with process update
            selectedDonor.userId
          );
        } catch (processError) {
          console.error('Failed to update appointment process:', processError);
          throw processError;
        }
      }

      // Update status if changed
      const newStatus = statusToSend === 2; // 2 = approved (true), others = rejected (false)
      if (selectedDonor.status !== newStatus) {
        try {
          await bloodDonationService.updateAppointmentStatus(
            selectedDonor.id,
            newStatus,
            "", // Don't send notes with status update
            selectedDonor.userId
          );
        } catch (statusError) {
          console.error('Failed to update appointment status:', statusError);
          // Don't throw error here - process update was successful
        }
      }

      // Always update notes using PATCH /api/Appointment/{id}/note
      if (statusUpdateData.notes !== undefined && statusUpdateData.notes !== null) {
        try {
          console.log('Updating notes:', {
            appointmentId: selectedDonor.id,
            notes: statusUpdateData.notes,
            notesLength: statusUpdateData.notes.length,
            notesType: typeof statusUpdateData.notes
          });

          await bloodDonationService.updateAppointmentNote(
            selectedDonor.id,
            statusUpdateData.notes || ""
          );
          console.log('Notes updated successfully');
        } catch (noteError) {
          console.error('Failed to update appointment note:', noteError);
          console.error('Note error response:', noteError.response?.data);
          // Don't throw error here - other updates were successful
        }
      }





      setShowStatusModal(false);
      setSelectedDonor(null);

      // Show appropriate success message based on status
      if (statusToSend === 2) {
        message.success("Cập nhật ghi chú và quy trình thành công!");
      } else {
        message.success("Đã từ chối - không đủ điều kiện hiến máu!");
      }

      // Call success callback to refresh data
      if (typeof onSuccess === 'function') {
        onSuccess();
      }

      // Send notifications based on updates
      // Send process notification if process changed
      if (processToSend && processToSend !== selectedDonor.process) {
        const processNames = {
          1: "Đăng ký",
          2: "Khám sức khỏe cơ bản",
          3: "Lấy máu",
          4: "Xét nghiệm máu",
          5: "Nhập kho"
        };

        try {
          await NotificationService.createNotification({
            userId: selectedDonor.userId,
            type: "donation_process_update",
            title: "📋 Cập nhật quy trình hiến máu",
            message: `Quy trình hiến máu của bạn đã được cập nhật sang bước: ${processNames[processToSend] || processToSend}`,
            data: {
              appointmentId: selectedDonor.id,
              process: processToSend,
              processName: processNames[processToSend] || processToSend,
              updateDate: new Date().toISOString().split("T")[0],
            },
          });
        } catch (notificationError) {
          console.warn("Could not send process notification:", notificationError);
        }
      }

      // Send status notification if status changed
      if (selectedDonor.status !== newStatus) {
        try {
          if (newStatus) {
            // Approved notification
            await NotificationService.createNotification({
              userId: selectedDonor.userId,
              type: "donation_status_update",
              title: "📋 Cập nhật trạng thái hiến máu",
              message: "Đăng ký hiến máu của bạn đã được chấp nhận. Vui lòng đến đúng giờ hẹn.",
              data: {
                appointmentId: selectedDonor.id,
                status: "Chấp nhận",
                updateDate: new Date().toISOString().split("T")[0],
              },
            });
          } else {
            // Rejected notification
            await NotificationService.createNotification({
              userId: selectedDonor.userId,
              type: "donation_status_update",
              title: "📋 Cập nhật trạng thái hiến máu",
              message: "Cảm ơn bạn đã đăng ký hiến máu. Mặc dù lần này chưa phù hợp nhưng chúng tôi rất trân trọng tinh thần của bạn.",
              data: {
                appointmentId: selectedDonor.id,
                status: "Không chấp nhận",
                updateDate: new Date().toISOString().split("T")[0],
              },
            });
          }
        } catch (notificationError) {
          console.warn("Could not send status notification:", notificationError);
        }
      }

    } catch (error) {
      console.error("Error updating donor process:", error);
      message.error("Có lỗi xảy ra khi cập nhật quy trình!");
    }
  };

  return {
    handleSaveUpdate,
    handleSaveStatusUpdate,
  };
};
