import React from "react";
import { FiDroplet, FiClipboard, FiCheckCircle } from "react-icons/fi";
import "../../../styles/components/manager/StatisticsCards.scss";

const StatisticsCards = ({ statistics, isBloodDepartment }) => {
  const {
    totalRequests = 0,
    pendingRequests = 0,
    approvedRequests = 0,
    completedRequests = 0,
    urgentNotifications = 0,
  } = statistics;

  // Different cards for blood department vs regular doctors
  const cards = isBloodDepartment
    ? [
        {
          id: "total-requests",
          title: "Tổng yêu cầu",
          value: totalRequests,
          subtitle: "Tất cả yêu cầu",
          icon: FiClipboard,
          color: "primary",
        },
        {
          id: "pending-requests",
          title: "Chờ xử lý",
          value: pendingRequests,
          subtitle: "<PERSON>êu cầu đang chờ",
          icon: FiDroplet,
          color: "warning",
        },
        {
          id: "approved-requests",
          title: "<PERSON><PERSON> chấp nhận",
          value: approvedRequests,
          subtitle: "<PERSON><PERSON><PERSON> c<PERSON>u hợp lệ",
          icon: FiClipboard,
          color: "success",
        },
      ]
    : [
        {
          id: "completed-requests",
          title: "<PERSON>àn thành",
          value: completedRequests,
          subtitle: "<PERSON><PERSON>u cầu đã hoàn thành",
          icon: FiCheckCircle,
          color: "success",
        },
        {
          id: "approved-requests",
          title: "Chấp nhận",
          value: approvedRequests,
          subtitle: "Yêu cầu đã chấp nhận",
          icon: FiClipboard,
          color: "primary",
        },
      ];

  return (
    <div className="statistics-cards">
      {cards.map((card) => {
        const IconComponent = card.icon;
        return (
          <div key={card.id} className={`stat-card ${card.color}`}>
            <div className="stat-header">
              <div className="stat-icon">
                <IconComponent />
              </div>
              <div className="stat-title">{card.title}</div>
            </div>

            <div className="stat-content">
              <div className="stat-value">
                {card.value.toLocaleString("vi-VN")}
              </div>
              <div className="stat-subtitle">{card.subtitle}</div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default StatisticsCards;
