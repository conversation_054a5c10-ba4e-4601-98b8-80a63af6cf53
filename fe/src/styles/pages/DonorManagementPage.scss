@use "../base/variables" as vars;

.donor-management {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

  .donor-management-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    transition: margin-left 0.3s ease;

    .access-denied {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 60vh;
      text-align: center;
      background: white;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

      h1 {
        color: #dc3545;
        font-size: 3rem;
        margin-bottom: 1rem;
        background: linear-gradient(45deg, #dc3545, #c82333);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      p {
        color: #666;
        font-size: 1.2rem;
      }
    }

    .page-header {
      margin-bottom: 2rem;
      background: white;
      padding: 2rem;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(40, 167, 69, 0.1);

      div {
        h1 {
          background: linear-gradient(45deg, #28a745, #20c997);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 0.5rem;
          font-size: 2.5rem;
          font-weight: 700;
        }

        p {
          color: #6c757d;
          font-size: 1.2rem;
          margin: 0 0 1rem 0;
          font-weight: 500;
        }

        .blood-dept-notice {
          background: linear-gradient(135deg, #d4edda, #c3e6cb);
          color: #155724;
          padding: 0.75rem 1.5rem;
          border-radius: 15px;
          font-size: 1rem;
          font-weight: 600;
          border: 2px solid #c3e6cb;
          display: inline-block;
          box-shadow: 0 4px 15px rgba(21, 87, 36, 0.1);
        }
      }
    }

    .filters-section {
      background: white;
      padding: 2rem;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;
      border: 1px solid rgba(40, 167, 69, 0.1);

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        label {
          font-weight: 700;
          color: #495057;
          font-size: 1rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        select {
          padding: 0.75rem 1rem;
          border: 2px solid #e9ecef;
          border-radius: 12px;
          font-size: 1rem;
          min-width: 180px;
          background: white;
          transition: all 0.3s ease;
          font-weight: 500;

          &:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.1);
            transform: translateY(-2px);
          }

          &:hover {
            border-color: #28a745;
            transform: translateY(-1px);
          }
        }
      }
    }

    .donors-section {
      margin-bottom: 2rem;

      h2 {
        background: linear-gradient(45deg, #28a745, #20c997);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 1.5rem;
        font-size: 1.8rem;
        font-weight: 700;
        text-align: center;
      }

      .donors-table-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        border: 1px solid rgba(40, 167, 69, 0.1);

        .donors-table {
          width: 100%;
          border-collapse: collapse;

          th,
          td {
            padding: 1.5rem 1rem;
            text-align: left;
            border-bottom: 1px solid #f8f9fa;
          }

          th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: 700;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: sticky;
            top: 0;
            z-index: 10;
          }

          td {
            font-size: 0.95rem;
            font-weight: 500;
          }

          tr {
            transition: all 0.3s ease;

            &:hover {
              background: linear-gradient(135deg, #f8f9fa, #e9ecef);
              transform: scale(1.01);
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }
          }

          .donor-info {
            .donor-name {
              font-weight: 700;
              color: #495057;
              margin-bottom: 0.25rem;
            }

            .donor-contact {
              color: #6c757d;
              font-size: 0.85rem;
            }
          }

          .blood-type-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 10px;
            font-weight: 700;
            font-size: 0.9rem;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
          }

          .rare-badge {
            background: linear-gradient(45deg, #6f42c1, #e83e8c);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 700;
            margin-left: 0.5rem;
            box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3);
          }

          .donation-count {
            background: linear-gradient(45deg, #17a2b8, #20c997);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 10px;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
          }

          .health-status {
            padding: 0.5rem 0.75rem;
            border-radius: 10px;
            font-weight: 700;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.health-success {
              background: linear-gradient(45deg, #d4edda, #c3e6cb);
              color: #155724;
              border: 1px solid #c3e6cb;
            }

            &.health-warning {
              background: linear-gradient(45deg, #fff3cd, #ffeaa7);
              color: #856404;
              border: 1px solid #ffeaa7;
            }

            &.health-danger {
              background: linear-gradient(45deg, #f8d7da, #f5c6cb);
              color: #721c24;
              border: 1px solid #f5c6cb;
            }
          }

          .eligibility-badge {
            padding: 0.5rem 0.75rem;
            border-radius: 10px;
            font-weight: 700;
            font-size: 0.85rem;
            margin-bottom: 0.5rem;
            display: inline-block;

            &.eligible {
              background: linear-gradient(45deg, #d4edda, #c3e6cb);
              color: #155724;
              border: 1px solid #c3e6cb;
            }

            &.not-eligible {
              background: linear-gradient(45deg, #f8d7da, #f5c6cb);
              color: #721c24;
              border: 1px solid #f5c6cb;
            }
          }

          .next-eligible {
            font-size: 0.8rem;
            color: #6c757d;
            font-style: italic;
          }

          .action-buttons {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;

            .btn {
              padding: 0.5rem 1rem;
              border: none;
              border-radius: 10px;
              font-size: 0.85rem;
              font-weight: 600;
              cursor: pointer;
              transition: all 0.3s ease;
              text-transform: uppercase;
              letter-spacing: 0.5px;

              &.btn-sm {
                padding: 0.5rem 1rem;
              }

              &.btn-info {
                background: linear-gradient(45deg, #17a2b8, #20c997);
                color: white;
                box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
                }
              }

              &.btn-primary {
                background: linear-gradient(45deg, #28a745, #20c997);
                color: white;
                box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);

                &:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
                }
              }
            }
          }
        }
      }
    }

    .statistics-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;

      .stat-card {
        background: white;
        padding: 2rem;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        text-align: center;
        border: 1px solid rgba(40, 167, 69, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(45deg, #28a745, #20c997);
        }

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        h3 {
          margin: 0 0 1rem 0;
          color: #6c757d;
          font-size: 1rem;
          text-transform: uppercase;
          letter-spacing: 1px;
          font-weight: 700;
        }

        .stat-number {
          font-size: 3rem;
          font-weight: 800;
          margin: 0;
          background: linear-gradient(45deg, #28a745, #20c997);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;

          &.success {
            background: linear-gradient(45deg, #28a745, #20c997);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          &.rare {
            background: linear-gradient(45deg, #6f42c1, #e83e8c);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
  }
}

// Modal styles với thiết kế hiện đại
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;

  .modal-content {
    background: white;
    border-radius: 25px;
    max-width: 900px;
    width: 90%;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(40, 167, 69, 0.1);
    animation: slideUp 0.3s ease;

    .modal-header {
      padding: 2rem;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 25px 25px 0 0;

      h2 {
        margin: 0;
        background: linear-gradient(45deg, #28a745, #20c997);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 1.5rem;
        font-weight: 700;
      }

      .close-btn {
        background: linear-gradient(45deg, #dc3545, #c82333);
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:hover {
          transform: rotate(90deg) scale(1.1);
          box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }
      }
    }

    .modal-body {
      padding: 2rem;

      .detail-section {
        margin-bottom: 2rem;
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 15px;
        border: 1px solid #e9ecef;

        &:last-child {
          margin-bottom: 0;
        }

        h3 {
          margin: 0 0 1.5rem 0;
          background: linear-gradient(45deg, #28a745, #20c997);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-size: 1.3rem;
          font-weight: 700;
          border-bottom: 2px solid #e9ecef;
          padding-bottom: 0.5rem;
        }

        .detail-row {
          display: flex;
          align-items: center;
          gap: 1rem;
          margin-bottom: 1rem;
          padding: 0.75rem;
          background: white;
          border-radius: 10px;
          border: 1px solid #e9ecef;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            min-width: 150px;
            color: #495057;
            font-size: 0.95rem;
            font-weight: 700;
          }

          .rare-badge {
            background: linear-gradient(45deg, #6f42c1, #e83e8c);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 700;
            margin-left: 0.5rem;
          }

          .health-status,
          .eligibility-badge {
            padding: 0.5rem 0.75rem;
            border-radius: 10px;
            font-weight: 700;
            font-size: 0.85rem;
            margin-left: 0.5rem;
          }
        }
      }

      .form-group {
        margin-bottom: 1.5rem;

        label {
          display: block;
          margin-bottom: 0.75rem;
          font-weight: 700;
          color: #495057;
          font-size: 1rem;
        }

        select,
        input,
        textarea {
          width: 100%;
          padding: 1rem;
          border: 2px solid #e9ecef;
          border-radius: 12px;
          font-size: 1rem;
          font-weight: 500;
          transition: all 0.3s ease;

          &:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.1);
            transform: translateY(-2px);
          }
        }

        textarea {
          resize: vertical;
          min-height: 100px;
        }
      }

      .modal-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;

        .btn {
          padding: 1rem 2rem;
          border: none;
          border-radius: 12px;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.btn-secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
            }
          }

          &.btn-primary {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
              transform: none;
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive
@media (max-width: 768px) {
  .donor-management {
    .donor-management-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        padding: 1.5rem;

        div h1 {
          font-size: 2rem;
        }
      }

      .filters-section {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem;

        .filter-group select {
          min-width: 100%;
        }
      }

      .donors-section .donors-table-container {
        overflow-x: auto;

        .donors-table {
          min-width: 800px;
        }
      }

      .statistics-section {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1.5rem;

          .stat-number {
            font-size: 2rem;
          }
        }
      }
    }
  }
}
