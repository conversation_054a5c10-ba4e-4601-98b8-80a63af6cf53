import { Table, Tag, Button } from "antd";

/**
 * Component bảng danh sách người hiến máu
 */
const DonorTable = ({
  donors,
  loading,
  onUpdateDonor,
  onUpdateStatus,
  onDeleteAppointment
}) => {

  const getTimeSlotText = (slot) => {
    if (slot === "morning" || slot === "Sáng (7:00-12:00)") {
      return "7:00 - 12:00";
    } else if (slot === "afternoon" || slot === "Chiều (13:00-17:00)") {
      return "13:00 - 17:00";
    }
    return slot || "N/A";
  };

  const BLOOD_TYPES = [
    "O+", "O-", "A+", "A-", "B+", "B-", "AB+", "AB-"
  ];



  const columns = [
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
      width: 150,
      render: (name, record) => (
        <div className="donor-info">
          <div className="donor-name">{name}</div>
          <div className="donor-details">
            <span className="detail-item">{record.age} tuổi</span>
            <span className="detail-item">{record.gender === "male" ? "Nam" : record.gender === "female" ? "Nữ" : "N/A"}</span>
          </div>
        </div>
      ),
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      width: 100,
      render: (bloodType, record) => {
        // Try to combine BloodGroup and RhType if available, otherwise use bloodType
        let displayBloodType = bloodType;

        if (record.bloodGroup && record.rhType) {
          // If we have separate fields, combine them
          const rhSymbol = record.rhType.includes('+') ? '+' :
            record.rhType.includes('-') ? '-' :
              record.rhType.replace('Rh', '');
          displayBloodType = `${record.bloodGroup}${rhSymbol}`;
        } else if (record.BloodGroup && record.RhType) {
          // Try uppercase field names
          const rhSymbol = record.RhType.includes('+') ? '+' :
            record.RhType.includes('-') ? '-' :
              record.RhType.replace('Rh', '');
          displayBloodType = `${record.BloodGroup}${rhSymbol}`;
        }

        return <Tag color="red">{displayBloodType || 'N/A'}</Tag>;
      },
      filters: BLOOD_TYPES.map(type => ({ text: type, value: type })),
      onFilter: (value, record) => {
        // Check multiple possible field combinations
        let bloodTypeToCheck = record.bloodType;

        if (record.bloodGroup && record.rhType) {
          const rhSymbol = record.rhType.includes('+') ? '+' :
            record.rhType.includes('-') ? '-' :
              record.rhType.replace('Rh', '');
          bloodTypeToCheck = `${record.bloodGroup}${rhSymbol}`;
        } else if (record.BloodGroup && record.RhType) {
          const rhSymbol = record.RhType.includes('+') ? '+' :
            record.RhType.includes('-') ? '-' :
              record.RhType.replace('Rh', '');
          bloodTypeToCheck = `${record.BloodGroup}${rhSymbol}`;
        }

        return bloodTypeToCheck === value;
      },
    },
    {
      title: "Liên hệ",
      dataIndex: "phone",
      key: "contact",
      width: 180,
      render: (phone, record) => (
        <div className="contact-info">
          <div className="phone">{phone || "N/A"}</div>
          <div className="email" style={{ fontSize: '12px', color: '#666' }}>
            {record.email || "N/A"}
          </div>
        </div>
      ),
    },
    {
      title: "Ngày đăng ký",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 120,
      render: (d) => (d ? new Date(d).toLocaleDateString("vi-VN") : "N/A"),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: "Lịch hẹn",
      dataIndex: "appointmentDate",
      key: "appointment",
      width: 150,
      render: (appointmentDate, record) => (
        <div className="appointment-info">
          <div className="appointment-date">
            {appointmentDate ? new Date(appointmentDate).toLocaleDateString("vi-VN") : "N/A"}
          </div>
          <div className="appointment-time">
            <Tag color="blue" size="small">
              {getTimeSlotText(record.timeSlot)}
            </Tag>
          </div>
        </div>
      ),
    },
    {
      title: "Quy trình hiến máu",
      dataIndex: "process",
      key: "process",
      width: 200,
      render: (process, record) => {
        // Check if rejected (status = false or 1)
        const isRejected = record.status === false || record.status === 1;

        // Process steps mapping (move outside if/else for reuse)
        const processSteps = {
          1: "Đăng ký",
          2: "Khám sức khỏe cơ bản",
          3: "Lấy máu",
          4: "Xét nghiệm máu",
          5: "Nhập kho"
        };

        // Get current process
        const currentProcess = record.process || record.Process || process || 1;
        const processText = processSteps[currentProcess] || "Đăng ký";

        // Color coding for different steps
        const stepColors = {
          1: "blue",      // Đăng ký
          2: "orange",    // Khám sức khỏe cơ bản
          3: "purple",    // Lấy máu
          4: "cyan",      // Xét nghiệm máu
          5: "green"      // Nhập kho
        };

        if (isRejected) {
          // Show rejection status first, then process step
          return (
            <div>
              <Tag color={stepColors[currentProcess] || "blue"}>
                Bước {currentProcess}: {processText}
              </Tag>
              <br />
              <Tag color="red" style={{ marginBottom: '4px' }}>Không chấp nhận</Tag>


              {record.notes && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px', maxWidth: '200px' }}>
                  {record.notes}
                </div>
              )}
            </div>
          );
        } else {
          // Show process steps for approved cases
          return (
            <Tag color={stepColors[currentProcess] || "blue"}>
              Bước {currentProcess}: {processText}
            </Tag>
          );
        }
      },
      filters: [
        { text: "Khám sức khỏe cơ bản", value: 2 },
        { text: "Lấy máu", value: 3 },
        { text: "Xét nghiệm máu", value: 4 },
        { text: "Nhập kho", value: 5 },
        { text: "Không chấp nhận", value: "rejected" },
      ],
      onFilter: (value, record) => {
        if (value === "rejected") {
          return record.status === false || record.status === 1;
        }
        const currentProcess = record.process || record.Process || 1;
        return currentProcess === value;
      },
    },
    {
      title: "Hành động",
      key: "actions",
      width: 150,
      fixed: 'right',
      render: (_, donor) => {
        const viewButtonStyle = {
          backgroundColor: "#1890ff",
          borderColor: "#1890ff",
          color: "white",
        };

        const statusButtonStyle = {
          backgroundColor: "#52c41a",
          borderColor: "#52c41a",
          color: "white",
        };

        return (
          <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }} className="action-buttons">
            <Button
              size="small"
              onClick={() => onUpdateDonor(donor)}
              style={viewButtonStyle}
              className="view-btn"
            >
              Thông tin
            </Button>
            <Button
              size="small"
              onClick={() => onUpdateStatus(donor)}
              style={statusButtonStyle}
              className="status-btn"
            >
              Trạng thái
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <Table
      className="donor-management-table"
      dataSource={donors}
      columns={columns}
      rowKey="id"
      loading={loading}
      scroll={{ x: 1200 }}
      size="middle"
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} của ${total} người hiến máu`,
      }}
    />
  );
};

export default DonorTable;
