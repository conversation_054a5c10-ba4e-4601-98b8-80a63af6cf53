// Blood Inventory Management Constants and Utilities

// Blood component mapping by ID - API sử dụng componentId (number)
export const BLOOD_COMPONENT_MAP = {
  1: "Toàn phần",
  2: "<PERSON><PERSON><PERSON> cầu",
  3: "<PERSON><PERSON><PERSON><PERSON> tương",
  4: "<PERSON><PERSON><PERSON><PERSON> cầu",
};

// Reverse mapping - component name to ID
export const BLOOD_COMPONENT_NAME_TO_ID = {
  "Toàn phần": 1,
  "Hồng cầu": 2,
  "Huyết tương": 3,
  "Tiểu cầu": 4,
};

// Blood component types (legacy - for backward compatibility)
export const COMPONENT_TYPES = {
  WHOLE: "Whole",
  RED_CELLS: "RedCells",
  PLASMA: "Plasma",
  PLATELETS: "Platelets",
};

// Blood component types as array (for UI dropdowns)
export const COMPONENT_TYPES_ARRAY = Object.values(BLOOD_COMPONENT_MAP);

// Blood groups for inventory
export const BLOOD_GROUPS = ["A", "B", "AB", "O"];

// Blood groups as object (for backward compatibility)
export const BLOOD_GROUP_ENUM = {
  A: "A",
  B: "B",
  AB: "AB",
  O: "O",
};

// Rh types for inventory
export const RH_TYPES = ["Rh+", "Rh-"];

// Rh types as object (for backward compatibility)
export const RH_TYPE_ENUM = {
  POSITIVE: "Rh+",
  NEGATIVE: "Rh-",
};

// Bag types for blood storage
export const BAG_TYPES = ["250ml", "350ml", "450ml"];

// Inventory status levels
export const INVENTORY_STATUS = {
  CRITICAL: "critical",
  LOW: "low",
  NORMAL: "normal",
  HIGH: "high",
};

// Status thresholds
export const INVENTORY_THRESHOLDS = {
  CRITICAL: 2,
  LOW: 5,
  HIGH: 30,
};

// Helper function to get component name by ID
export const getBloodComponentName = (componentId) => {
  // Convert to number to handle both string and number inputs
  const id = parseInt(componentId);
  return BLOOD_COMPONENT_MAP[id] || "Toàn phần";
};

// Helper function to get component ID by name
export const getBloodComponentId = (componentName) => {
  return BLOOD_COMPONENT_NAME_TO_ID[componentName] || 0;
};

// Helper function to map Rh type to symbol
export const mapRhTypeToSymbol = (rhType) => {
  if (rhType === "Rh+") return "+";
  if (rhType === "Rh-") return "-";
  return rhType;
};

// Helper function to get inventory status based on quantity
export const getInventoryStatus = (quantity) => {
  if (quantity <= INVENTORY_THRESHOLDS.CRITICAL) {
    return INVENTORY_STATUS.CRITICAL;
  } else if (quantity <= INVENTORY_THRESHOLDS.LOW) {
    return INVENTORY_STATUS.LOW;
  } else if (quantity >= INVENTORY_THRESHOLDS.HIGH) {
    return INVENTORY_STATUS.HIGH;
  }
  return INVENTORY_STATUS.NORMAL;
};

// Helper function to get status color
export const getInventoryStatusColor = (status) => {
  switch (status) {
    case INVENTORY_STATUS.CRITICAL:
      return "#D91022"; // đỏ
    case INVENTORY_STATUS.LOW:
      return "#fa8c16"; // cam
    case INVENTORY_STATUS.NORMAL:
      return "#FFD600"; // vàng
    case INVENTORY_STATUS.HIGH:
      return "#52c41a"; // xanh lá
    default:
      return "#666666";
  }
};

// Helper function to get status text
export const getInventoryStatusText = (status) => {
  switch (status) {
    case INVENTORY_STATUS.CRITICAL:
      return "Cảnh báo khẩn cấp";
    case INVENTORY_STATUS.LOW:
      return "Thiếu máu";
    case INVENTORY_STATUS.NORMAL:
      return "Trung bình";
    case INVENTORY_STATUS.HIGH:
      return "An toàn";
    default:
      return "Không xác định";
  }
};

// Helper function to get status icon (returns icon name for use with Ant Design)
export const getInventoryStatusIcon = (status) => {
  switch (status) {
    case INVENTORY_STATUS.CRITICAL:
      return "ExclamationCircleOutlined";
    case INVENTORY_STATUS.LOW:
      return "WarningOutlined";
    case INVENTORY_STATUS.NORMAL:
      return "WarningOutlined";
    case INVENTORY_STATUS.HIGH:
      return "CheckCircleOutlined";
    default:
      return "ExclamationCircleOutlined";
  }
};

// Helper function to format blood type display
export const formatBloodType = (bloodGroup, rhType) => {
  return `${bloodGroup}${mapRhTypeToSymbol(rhType)}`;
};

// Validation helpers
export const validateInventoryForm = (form) => {
  const errors = {};

  if (!form.bloodGroup) {
    errors.bloodGroup = "Vui lòng chọn nhóm máu";
  }

  if (!form.rhType) {
    errors.rhType = "Vui lòng chọn Rh";
  }

  if (!form.componentType) {
    errors.componentType = "Vui lòng chọn thành phần máu";
  }

  if (!form.quantity || form.quantity <= 0) {
    errors.quantity = "Số lượng phải lớn hơn 0";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

// Mock blood inventory data (for development/testing)
export const mockBloodInventory = [
  {
    inventoryID: 1,
    bloodGroup: BLOOD_GROUP_ENUM.A,
    rhType: RH_TYPE_ENUM.POSITIVE,
    componentType: COMPONENT_TYPES.WHOLE,
    quantity: 25,
    isRare: false,
    lastUpdated: "2024-01-15T10:00:00Z",
  },
  {
    inventoryID: 2,
    bloodGroup: BLOOD_GROUP_ENUM.A,
    rhType: RH_TYPE_ENUM.NEGATIVE,
    componentType: COMPONENT_TYPES.WHOLE,
    quantity: 8,
    isRare: false,
    lastUpdated: "2024-01-15T10:00:00Z",
  },
  {
    inventoryID: 3,
    bloodGroup: BLOOD_GROUP_ENUM.B,
    rhType: RH_TYPE_ENUM.POSITIVE,
    componentType: COMPONENT_TYPES.WHOLE,
    quantity: 18,
    isRare: false,
    lastUpdated: "2024-01-15T10:00:00Z",
  },
  {
    inventoryID: 4,
    bloodGroup: BLOOD_GROUP_ENUM.B,
    rhType: RH_TYPE_ENUM.NEGATIVE,
    componentType: COMPONENT_TYPES.WHOLE,
    quantity: 3,
    isRare: true,
    lastUpdated: "2024-01-15T10:00:00Z",
  },
  {
    inventoryID: 5,
    bloodGroup: BLOOD_GROUP_ENUM.AB,
    rhType: RH_TYPE_ENUM.POSITIVE,
    componentType: COMPONENT_TYPES.WHOLE,
    quantity: 12,
    isRare: false,
    lastUpdated: "2024-01-15T10:00:00Z",
  },
  {
    inventoryID: 6,
    bloodGroup: BLOOD_GROUP_ENUM.AB,
    rhType: RH_TYPE_ENUM.NEGATIVE,
    componentType: COMPONENT_TYPES.WHOLE,
    quantity: 2,
    isRare: true,
    lastUpdated: "2024-01-15T10:00:00Z",
  },
  {
    inventoryID: 7,
    bloodGroup: BLOOD_GROUP_ENUM.O,
    rhType: RH_TYPE_ENUM.POSITIVE,
    componentType: COMPONENT_TYPES.WHOLE,
    quantity: 35,
    isRare: false,
    lastUpdated: "2024-01-15T10:00:00Z",
  },
  {
    inventoryID: 8,
    bloodGroup: BLOOD_GROUP_ENUM.O,
    rhType: RH_TYPE_ENUM.NEGATIVE,
    componentType: COMPONENT_TYPES.WHOLE,
    quantity: 5,
    isRare: true,
    lastUpdated: "2024-01-15T10:00:00Z",
  },
];

// Function to get blood inventory with status
export const getBloodInventoryWithStatus = () => {
  return mockBloodInventory.map((item) => {
    const status = getInventoryStatus(item.quantity);

    return {
      ...item,
      bloodType: formatBloodType(item.bloodGroup, item.rhType),
      status,
      statusColor: getInventoryStatusColor(status),
      statusText: getInventoryStatusText(status),
      statusIcon:
        status === INVENTORY_STATUS.CRITICAL
          ? "🚨"
          : status === INVENTORY_STATUS.LOW
          ? "⚠️"
          : status === INVENTORY_STATUS.HIGH
          ? "✅"
          : "📊",
    };
  });
};
