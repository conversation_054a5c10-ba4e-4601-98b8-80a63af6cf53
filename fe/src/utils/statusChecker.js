import authService from '../services/authService';
import userInfoService from '../services/userInfoService';

/**
 * Status Checker - Monitors user account status and handles suspended accounts
 */
class StatusChecker {
  constructor() {
    this.checkInterval = null;
    this.isChecking = false;
  }

  /**
   * Start monitoring user status
   * @param {number} intervalMs - Check interval in milliseconds (default: 30 seconds)
   */
  startMonitoring(intervalMs = 30000) {
    if (this.checkInterval) {
      this.stopMonitoring();
    }

    this.checkInterval = setInterval(() => {
      this.checkUserStatus();
    }, intervalMs);

    // Also check immediately
    this.checkUserStatus();
  }

  /**
   * Stop monitoring user status
   */
  stopMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Check current user's status
   */
  async checkUserStatus() {
    if (this.isChecking || !authService.isUserAuthenticated()) {
      return;
    }

    try {
      this.isChecking = true;
      const currentUser = authService.getCurrentUser();
      
      if (!currentUser || !currentUser.userID) {
        return;
      }

      // Fetch latest user info from API
      const users = await userInfoService.getAllUsers();
      const userInfo = users.find(u => u.userID === currentUser.userID);

      if (userInfo) {
        // Check if user status has changed to suspended (status = 0)
        if (userInfo.status === 0) {
          this.handleSuspendedAccount();
        } else if (currentUser.status !== userInfo.status) {
          // Update user status in local storage
          authService.updateStatus(userInfo.status);
        }
      }
    } catch (error) {
      console.error('Error checking user status:', error);
    } finally {
      this.isChecking = false;
    }
  }

  /**
   * Handle suspended account - force logout and show message
   */
  handleSuspendedAccount() {
    // Stop monitoring
    this.stopMonitoring();
    
    // Force logout
    authService.logout();
    
    // Show alert message
    if (typeof window !== 'undefined') {
      alert('Tài khoản của bạn đã bị đình chỉ hoạt động. Bạn sẽ được đăng xuất khỏi hệ thống.');
      
      // Redirect to login page
      window.location.href = '/login';
    }
  }

  /**
   * Manual check for user status (can be called from components)
   */
  async manualCheck() {
    await this.checkUserStatus();
  }

  /**
   * Check if user is suspended based on status value
   * @param {number} status - User status (0 = suspended, 1 = active)
   * @returns {boolean} True if user is suspended
   */
  static isSuspended(status) {
    return status === 0;
  }

  /**
   * Check if user is active based on status value
   * @param {number} status - User status (0 = suspended, 1 = active)
   * @returns {boolean} True if user is active
   */
  static isActive(status) {
    return status === 1;
  }

  /**
   * Get status label for display
   * @param {number} status - User status
   * @returns {string} Status label
   */
  static getStatusLabel(status) {
    switch (status) {
      case 1:
        return 'Hoạt động';
      case 0:
        return 'Đình chỉ hoạt động';
      default:
        return 'Không xác định';
    }
  }

  /**
   * Get status color for UI display
   * @param {number} status - User status
   * @returns {string} Color code
   */
  static getStatusColor(status) {
    switch (status) {
      case 1:
        return 'green';
      case 0:
        return 'red';
      default:
        return 'gray';
    }
  }
}

// Create singleton instance
const statusChecker = new StatusChecker();

export default statusChecker;

// Export utility functions
export const {
  isSuspended,
  isActive,
  getStatusLabel,
  getStatusColor
} = StatusChecker;
