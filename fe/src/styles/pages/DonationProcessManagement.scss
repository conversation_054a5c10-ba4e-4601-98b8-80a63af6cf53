@import "../base/variables";
@use "sass:color";

.donation-process-management {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;

  .donation-process-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    transition: margin-left 0.3s ease;

    .page-header {
      margin-bottom: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 1rem;

      div {
        h1 {
          color: $primary-color;
          margin-bottom: 0.5rem;
          font-size: 2rem;
          font-weight: 600;
        }

        p {
          color: #666;
          font-size: 1.1rem;
          margin: 0;
        }
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &.btn-primary {
          background: color.adjust($primary-color, $lightness: -10%);
          color: white;

          &:hover {
            background: color.adjust(
              color.adjust($primary-color, $lightness: -10%),
              $lightness: -10%
            );
            transform: translateY(-2px);
          }
        }
      }
    }

    .process-flow {
      background: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      gap: 1rem;

      .flow-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem;
        border-radius: 8px;
        background: #f8f9fa;
        min-width: 120px;

        .step-icon {
          font-size: 2rem;
          margin-bottom: 0.5rem;
        }

        .step-title {
          font-weight: 600;
          color: #333;
          text-align: center;
          font-size: 0.9rem;
        }
      }

      .flow-arrow {
        font-size: 1.5rem;
        color: $primary-color;
        font-weight: bold;
      }
    }

    .filters-section {
      background: white;
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        label {
          font-weight: 600;
          color: #333;
          font-size: 0.9rem;
        }

        select {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;
          min-width: 150px;

          &:focus {
            outline: none;
            border-color: $primary-color;
            box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
          }
        }
      }
    }

    .donations-table-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      margin-bottom: 2rem;

      .donations-table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
          padding: 1rem;
          text-align: left;
          border-bottom: 1px solid #eee;
        }

        th {
          background: #f8f9fa;
          font-weight: 600;
          color: #333;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        td {
          font-size: 0.9rem;
        }

        tr:hover {
          background: #f8f9fa;
        }

        .blood-type-badge {
          background: $primary-color;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-weight: 600;
          font-size: 0.8rem;
        }

        .status-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-weight: 600;
          font-size: 0.8rem;
          text-transform: uppercase;

          &.status-info {
            background: #d1ecf1;
            color: #0c5460;
          }

          &.status-warning {
            background: #fff3cd;
            color: #856404;
          }

          &.status-primary {
            background: rgba($primary-color, 0.1);
            color: $primary-color;
          }

          &.status-success {
            background: #d4edda;
            color: #155724;
          }

          &.status-danger {
            background: #f8d7da;
            color: #721c24;
          }

          &.status-secondary {
            background: #e2e3e5;
            color: #383d41;
          }
        }

        .action-buttons {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;

          .btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;

            &.btn-sm {
              padding: 0.25rem 0.5rem;
            }

            &.btn-info {
              background: #17a2b8;
              color: white;

              &:hover {
                background: #138496;
              }
            }

            &.btn-warning {
              background: #ffc107;
              color: #212529;

              &:hover {
                background: #e0a800;
              }
            }

            &.btn-primary {
              background: $primary-color;
              color: white;

              &:hover {
                background: color.adjust($primary-color, $lightness: -10%);
              }
            }

            &.btn-success {
              background: #28a745;
              color: white;

              &:hover {
                background: #218838;
              }
            }

            &.btn-danger {
              background: #dc3545;
              color: white;

              &:hover {
                background: #c82333;
              }
            }
          }
        }
      }
    }

    .statistics-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;

      .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;

        h3 {
          margin: 0 0 1rem 0;
          color: #666;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .stat-number {
          font-size: 2rem;
          font-weight: 700;
          margin: 0;
          color: $primary-color;

          &.warning {
            color: #ffc107;
          }

          &.success {
            color: #28a745;
          }

          &.danger {
            color: #dc3545;
          }
        }
      }
    }
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    .modal-header {
      padding: 1.5rem;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        color: $primary-color;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;

        &:hover {
          color: #333;
        }
      }
    }

    .modal-body {
      padding: 1.5rem;

      .detail-row {
        margin-bottom: 1rem;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        strong {
          min-width: 120px;
          color: #333;
        }

        .test-results {
          width: 100%;
          margin-top: 0.5rem;
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 6px;

          div {
            margin-bottom: 0.5rem;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      .form-group {
        margin-bottom: 1rem;

        label {
          display: block;
          margin-bottom: 0.5rem;
          font-weight: 600;
          color: #333;
        }

        select,
        input,
        textarea {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 0.9rem;

          &:focus {
            outline: none;
            border-color: $primary-color;
            box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
          }
        }

        textarea {
          resize: vertical;
          min-height: 80px;
        }
      }

      .modal-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;

        .btn {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;

          &.btn-secondary {
            background: #6c757d;
            color: white;

            &:hover {
              background: #5a6268;
            }
          }

          &.btn-primary {
            background: $primary-color;
            color: white;

            &:hover {
              background: color.adjust($primary-color, $lightness: -10%);
            }

            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .donation-process-management {
    .donation-process-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        flex-direction: column;
        align-items: stretch;
      }

      .process-flow {
        .flow-step {
          min-width: 100px;
          padding: 0.75rem;

          .step-icon {
            font-size: 1.5rem;
          }

          .step-title {
            font-size: 0.8rem;
          }
        }

        .flow-arrow {
          font-size: 1.2rem;
        }
      }

      .filters-section {
        flex-direction: column;
        gap: 1rem;

        .filter-group {
          select {
            min-width: 100%;
          }
        }
      }

      .donations-table-container {
        overflow-x: auto;

        .donations-table {
          min-width: 800px;
        }
      }

      .statistics-section {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1rem;

          .stat-number {
            font-size: 1.5rem;
          }
        }
      }
    }
  }
}
