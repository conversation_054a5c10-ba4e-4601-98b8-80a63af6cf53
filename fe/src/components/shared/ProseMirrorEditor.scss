.prosemirror-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: border-color 0.3s;
  background: white;

  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  // New custom toolbar
  .prosemirror-toolbar {
    border-bottom: 1px solid #d9d9d9;
    background: #fafafa;
    padding: 8px 12px;
    border-radius: 6px 6px 0 0;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;

    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 8px;

      .toolbar-button {
        background: white;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 6px 12px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover:not(:disabled) {
          background: rgba(24, 144, 255, 0.1);
          border-color: #91d5ff;
          color: #1890ff;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .color-group {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;

        label {
          margin: 0;
          color: #666;
          white-space: nowrap;
        }

        .color-button {
          input[type="color"] {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            padding: 0;
            background: none;

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }

  // Upload instructions
  .upload-instructions {
    padding: 8px 12px;
    background: #f6f8fa;
    border-top: 1px solid #e1e8ed;
    border-radius: 0 0 6px 6px;

    small {
      color: #666;
      font-size: 11px;
    }
  }

  .prosemirror-container {
    position: relative;

    // Menu bar styles
    .ProseMirror-menubar {
      border-bottom: 1px solid #d9d9d9;
      background: #fafafa;
      padding: 8px 12px;
      border-radius: 6px 6px 0 0;

      .ProseMirror-menu {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        margin: 0;

        .ProseMirror-menuitem {
          background: transparent;
          border: 1px solid transparent;
          border-radius: 4px;
          padding: 6px 8px;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 32px;
          height: 32px;

          &:hover {
            background: rgba(24, 144, 255, 0.1);
            border-color: #91d5ff;
          }

          &.ProseMirror-menu-active {
            background: rgba(24, 144, 255, 0.2);
            border-color: #40a9ff;
            color: #1890ff;
          }

          &.ProseMirror-menu-disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .ProseMirror-menuseparator {
          width: 1px;
          height: 20px;
          background: #d9d9d9;
          margin: 0 4px;
        }
      }
    }

    // Editor content styles
    .ProseMirror {
      padding: 16px;
      outline: none;
      min-height: 200px;
      line-height: 1.6;
      font-size: 14px;
      color: #262626;

      &.disabled {
        background: #f5f5f5;
        color: #8c8c8c;
        cursor: not-allowed;
      }

      // Placeholder
      &:empty:before {
        content: attr(data-placeholder);
        color: #bfbfbf;
        pointer-events: none;
        position: absolute;
      }

      // Typography
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 16px 0 8px 0;
        font-weight: 600;
        line-height: 1.4;
      }

      h1 {
        font-size: 32px;
      }
      h2 {
        font-size: 24px;
      }
      h3 {
        font-size: 20px;
      }
      h4 {
        font-size: 16px;
      }
      h5 {
        font-size: 14px;
      }
      h6 {
        font-size: 12px;
      }

      p {
        margin: 8px 0;

        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }

      // Lists
      ul,
      ol {
        padding-left: 24px;
        margin: 8px 0;

        li {
          margin: 4px 0;
        }

        // Nested lists
        ul,
        ol {
          margin: 4px 0;
        }
      }

      ul {
        list-style-type: disc;
      }

      ol {
        list-style-type: decimal;
      }

      // Blockquote
      blockquote {
        border-left: 4px solid #1890ff;
        padding: 8px 16px;
        margin: 16px 0;
        background: #f6f8fa;
        border-radius: 0 4px 4px 0;
        font-style: italic;
        color: #595959;
      }

      // Code
      code {
        background: #f5f5f5;
        border: 1px solid #e8e8e8;
        border-radius: 3px;
        padding: 2px 6px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", "Consolas",
          "source-code-pro", monospace;
        font-size: 13px;
        color: #d73a49;
      }

      pre {
        background: #f5f5f5;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        padding: 16px;
        margin: 12px 0;
        overflow-x: auto;

        code {
          background: transparent;
          border: none;
          padding: 0;
          color: #262626;
        }
      }

      // Links
      a {
        color: #1890ff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      // Strong and emphasis
      strong {
        font-weight: 600;
      }

      em {
        font-style: italic;
      }

      // Horizontal rule
      hr {
        border: none;
        border-top: 2px solid #d9d9d9;
        margin: 24px 0;
      }

      // Color support - background colors with padding
      span[style*="background-color"] {
        padding: 1px 2px;
        border-radius: 2px;
      }
    }
  }

  // Editor content styles
  .ProseMirror {
    // Image styles
    img {
      max-width: 100%;
      height: auto;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin: 8px 0;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.ProseMirror-selectednode {
        outline: 2px solid #1890ff;
        outline-offset: 2px;
      }
    }

    // Drag and drop styles
    &.ProseMirror-hideselection {
      .ProseMirror-selectednode {
        outline: none;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .prosemirror-wrapper {
    .prosemirror-toolbar {
      padding: 6px 8px;
      gap: 8px;

      .toolbar-group {
        gap: 4px;

        .toolbar-button {
          padding: 4px 8px;
          font-size: 12px;
        }

        .color-group {
          .color-button input[type="color"] {
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    .prosemirror-container {
      .ProseMirror-menubar {
        padding: 6px 8px;

        .ProseMirror-menu {
          gap: 2px;

          .ProseMirror-menuitem {
            min-width: 28px;
            height: 28px;
            padding: 4px 6px;
          }
        }
      }

      .ProseMirror {
        padding: 12px;
        font-size: 13px;
      }
    }

    .upload-instructions {
      padding: 6px 8px;

      small {
        font-size: 10px;
      }
    }
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .prosemirror-wrapper {
    border-color: #434343;
    background: #1f1f1f;

    &:focus-within {
      border-color: #1890ff;
    }

    .prosemirror-toolbar {
      background: #2f2f2f;
      border-color: #434343;

      .toolbar-group {
        .toolbar-button {
          background: #1f1f1f;
          border-color: #434343;
          color: #fff;

          &:hover:not(:disabled) {
            background: rgba(24, 144, 255, 0.2);
            border-color: #1890ff;
          }
        }

        .color-group {
          label {
            color: #d9d9d9;
          }

          .color-button input[type="color"] {
            border-color: #434343;
          }
        }
      }
    }

    .upload-instructions {
      background: #2f2f2f;
      border-color: #434343;

      small {
        color: #d9d9d9;
      }
    }

    .prosemirror-container {
      .ProseMirror-menubar {
        background: #2f2f2f;
        border-color: #434343;
      }

      .ProseMirror {
        background: #1f1f1f;
        color: #fff;

        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p {
          color: #fff;
        }

        blockquote {
          background: #2f2f2f;
          color: #d9d9d9;
        }

        code,
        pre {
          background: #2f2f2f;
          border-color: #434343;
          color: #fff;
        }

        img {
          box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);

          &:hover {
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.15);
          }
        }
      }
    }
  }
}
