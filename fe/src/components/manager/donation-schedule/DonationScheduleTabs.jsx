import { Tabs } from "antd";
import { CalendarOutlined, HeartOutlined } from "@ant-design/icons";
import ScheduleTab from "./ScheduleTab";
import ProcessTab from "./ProcessTab";

/**
 * Main tabs component for donation schedule management
 */
const DonationScheduleTabs = ({
  activeTab,
  onTabChange,
  scheduleData,
  processData,
  loading,
  onViewDetails,
  onViewWorkflow,
  onSendReminder,
  onStoreBlood,
  isManager,
}) => {
  const tabItems = [
    {
      key: "1",
      label: (
        <span>
          <CalendarOutlined />
          <PERSON><PERSON>ch hiến máu
        </span>
      ),
      children: (
        <ScheduleTab
          {...scheduleData}
          loading={loading}
          onViewDetails={onViewDetails}
          onViewWorkflow={onViewWorkflow}
          onSendReminder={onSendReminder}
        />
      ),
    },
    {
      key: "2",
      label: (
        <span>
          <HeartOutlined />
          Quy trình xử lý
        </span>
      ),
      children: (
        <ProcessTab
          {...processData}
          loading={loading}
          onViewDetails={onViewDetails}
          onViewWorkflow={onViewWorkflow}
          onStoreBlood={onStoreBlood}
          isManager={isManager}
        />
      ),
    },
  ];

  return (
    <div className="schedule-content">
      <Tabs
        activeKey={activeTab}
        onChange={onTabChange}
        size="large"
        type="card"
        items={tabItems}
      />
    </div>
  );
};

export default DonationScheduleTabs;
