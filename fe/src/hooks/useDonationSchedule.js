import { useState, useEffect, useCallback } from "react";
import { message } from "antd";
import authService from "../services/authService";
import bloodDonationService from "../services/bloodDonationService";
import userInfoService from "../services/userInfoService";
import { DONATION_STATUSES } from "../components/shared/ProcessWorkflowModal";
import { formatBloodType } from "../utils/bloodDonationUtils";
import useRemindManagement from "./useRemindManagement";

/**
 * Custom hook for managing donation schedule data and operations
 */
const useDonationSchedule = () => {
  const [allDonations, setAllDonations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDonation, setSelectedDonation] = useState(null);
  const [processModalVisible, setProcessModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState("1");
  const [scheduleSort, setScheduleSort] = useState({
    field: "appointmentDate",
    order: "desc",
  });
  const [processSort, setProcessSort] = useState({
    field: "status",
    order: "asc",
  });
  const [filters, setFilters] = useState({
    bloodType: "all",
    status: "all",
  });

  const currentUser = authService.getCurrentUser();
  const isManager = currentUser?.role === "manager";

  // Use remind management hook
  const { createManualReminder, createAppointmentReminder } = useRemindManagement();

  // Helper function to map API status to component status
  const mapApiStatusToComponentStatus = (apiStatus) => {
    const statusMap = {
      // Numeric statuses
      0: DONATION_STATUSES.REGISTERED,
      1: DONATION_STATUSES.HEALTH_CHECKED,
      2: DONATION_STATUSES.BLOOD_TAKEN,
      3: DONATION_STATUSES.BLOOD_TESTED,
      4: DONATION_STATUSES.STORED,
      // String statuses
      "registered": DONATION_STATUSES.REGISTERED,
      "pending": DONATION_STATUSES.REGISTERED,
      "health_checked": DONATION_STATUSES.HEALTH_CHECKED,
      "blood_taken": DONATION_STATUSES.BLOOD_TAKEN,
      "donated": DONATION_STATUSES.BLOOD_TAKEN,
      "blood_tested": DONATION_STATUSES.BLOOD_TESTED,
      "stored": DONATION_STATUSES.STORED,
    };
    return statusMap[apiStatus] || DONATION_STATUSES.REGISTERED;
  };

  // Load all donations with detailed donor information
  const loadAllDonations = async () => {
    setLoading(true);
    try {
      // Use GET /api/Appointment to get all appointments
      const appointmentsData = await bloodDonationService.getAllAppointments();

      // Transform API data and fetch detailed user information
      const transformedDonations = await Promise.all(
        appointmentsData.map(async (appointment) => {
          let donorInfo = {};

          // Fetch detailed donor information from Information API
          try {
            const userId = appointment.UserId || appointment.userId;
            if (userId) {
              donorInfo = await userInfoService.getUserInfo(userId);
            }
          } catch (error) {
            console.warn(`⚠️ Could not fetch donor info for user ${appointment.UserId || appointment.userId}:`, error);
            donorInfo = {};
          }

          // Map API fields to component expected fields with detailed donor info
          return {
            id: appointment.AppointmentId || appointment.appointmentId,
            donorId: appointment.UserId || appointment.userId,
            donorName: donorInfo.Name || donorInfo.fullName || donorInfo.name || `User ${appointment.UserId || appointment.userId}`,
            donorPhone: donorInfo.Phone || donorInfo.phoneNumber || donorInfo.phone || "",
            donorEmail: donorInfo.Email || donorInfo.email || "",
            bloodType: donorInfo.BloodGroup && donorInfo.RhType
              ? formatBloodType(donorInfo.BloodGroup, donorInfo.RhType)
              : donorInfo.bloodGroup && donorInfo.rhType
                ? formatBloodType(donorInfo.bloodGroup, donorInfo.rhType)
                : "O+",
            expectedQuantity: "450ml", // Default quantity
            registrationDate: appointment.CreatedAt || appointment.createdAt,
            appointmentDate: appointment.AppointmentDate || appointment.appointmentDate,
            timeSlot: appointment.TimeSlot || appointment.timeSlot || "morning",
            // Use new process and status structure
            process: appointment.Process || appointment.process || 1,
            status: appointment.Status !== undefined ? appointment.Status : appointment.status,
            notes: appointment.Notes || appointment.notes || "",
            notificationStatus: "pending", // Default notification status
            notificationSentAt: null,
            healthSurvey: {
              weight: donorInfo.Weight || donorInfo.weight || 0,
              height: donorInfo.Height || donorInfo.height || 0,
              bloodPressure: appointment.BloodPressure || appointment.bloodPressure || "120/80",
              heartRate: appointment.HeartRate || appointment.heartRate || 0,
              hemoglobin: appointment.Hemoglobin || appointment.hemoglobin || 0,
              temperature: appointment.Temperature || appointment.temperature || 0,
              eligibilityChecked: appointment.status !== undefined,
            },
            location: {
              address: donorInfo.Address || donorInfo.address || "Chưa có địa chỉ",
              distance: donorInfo.Distance || donorInfo.distance || 0,
            },
            donorDetails: {
              dateOfBirth: donorInfo.DateOfBirth || donorInfo.dateOfBirth || null,
              gender: donorInfo.Gender || donorInfo.gender || null,
              identityCard: donorInfo.IdentityCard || donorInfo.identityCard || null,
              occupation: donorInfo.Occupation || donorInfo.occupation || null,
              emergencyContact: donorInfo.EmergencyContact || donorInfo.emergencyContact || null,
              medicalHistory: donorInfo.MedicalHistory || donorInfo.medicalHistory || null,
            }
          };
        })
      );

      setAllDonations(transformedDonations);

    } catch (error) {
      console.error("Error loading donations:", error);

      if (error.response?.status === 404) {
        message.warning("API endpoint chưa sẵn sàng. Đang sử dụng dữ liệu mẫu.");
      } else {
        message.error("Có lỗi xảy ra khi tải danh sách hiến máu!");
      }

      // Fallback to mock data
      setAllDonations([]);
    } finally {
      setLoading(false);
    }
  };

  // Refresh data function
  const refreshData = () => {
    loadAllDonations();
  };

  // Handle store blood action
  const handleStoreBlood = async (donationId) => {
    try {
      await bloodDonationService.updateBloodDonationSubmissionStatus(
        donationId,
        DONATION_STATUSES.STORED
      );

      setAllDonations((prev) =>
        prev.map((d) =>
          d.id === donationId ? { ...d, status: DONATION_STATUSES.STORED } : d
        )
      );
      message.success("Đã nhập kho thành công!");
    } catch (error) {
      console.error("Error storing blood:", error);
      message.error("Có lỗi xảy ra khi nhập kho!");
    }
  };

  // Manual send reminder using RemindService
  const handleSendReminder = async (donation) => {
    try {
      // Create manual reminder using hook
      await createManualReminder(donation);

      // Update donation status
      setAllDonations((prev) =>
        prev.map((d) =>
          d.id === donation.id
            ? {
              ...d,
              notificationStatus: "sent",
              notificationSentAt: new Date().toISOString(),
            }
            : d
        )
      );
    } catch (error) {
      console.error("Error sending manual reminder:", error);

      // Update status to failed
      setAllDonations((prev) =>
        prev.map((d) =>
          d.id === donation.id
            ? { ...d, notificationStatus: "failed" }
            : d
        )
      );
    }
  };

  // Auto notification system using RemindService
  const checkAndSendReminders = useCallback(async () => {
    try {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      const tomorrowEnd = new Date(tomorrow);
      tomorrowEnd.setHours(23, 59, 59, 999);

      const donationsToRemind = allDonations.filter((donation) => {
        if (donation.status !== DONATION_STATUSES.REGISTERED) return false;
        if (donation.notificationStatus === "sent") return false;

        const appointmentDate = new Date(donation.appointmentDate);
        return appointmentDate >= tomorrow && appointmentDate <= tomorrowEnd;
      });

      for (const donation of donationsToRemind) {
        try {
          // Create appointment reminder using hook
          const appointmentData = {
            userId: donation.donorId,
            appointmentId: donation.id,
            appointmentDate: donation.appointmentDate,
            bloodType: donation.bloodType,
            donorName: donation.donorName,
            donorEmail: donation.donorEmail,
            donorPhone: donation.donorPhone
          };

          await createAppointmentReminder(appointmentData);

          // Update donation status
          setAllDonations((prev) =>
            prev.map((d) =>
              d.id === donation.id
                ? {
                  ...d,
                  notificationStatus: "sent",
                  notificationSentAt: new Date().toISOString(),
                }
                : d
            )
          );
        } catch (error) {
          console.error(`Error creating reminder for ${donation.donorName}:`, error);
          setAllDonations((prev) =>
            prev.map((d) =>
              d.id === donation.id ? { ...d, notificationStatus: "failed" } : d
            )
          );
        }
      }

      // Auto reminder completed
    } catch (error) {
      console.error("Error in auto reminder system:", error);
    }
  }, [allDonations]);

  // Setup reminder system
  useEffect(() => {
    const reminderInterval = setInterval(
      checkAndSendReminders,
      24 * 60 * 60 * 1000
    );

    const initialCheck = setTimeout(checkAndSendReminders, 5000);

    return () => {
      clearInterval(reminderInterval);
      clearTimeout(initialCheck);
    };
  }, [checkAndSendReminders]);

  useEffect(() => {
    loadAllDonations();
  }, []);

  return {
    // Data
    allDonations,
    loading,
    selectedDonation,
    processModalVisible,
    detailModalVisible,
    activeTab,
    scheduleSort,
    processSort,
    filters,
    isManager,

    // Actions
    setSelectedDonation,
    setProcessModalVisible,
    setDetailModalVisible,
    setActiveTab,
    setScheduleSort,
    setProcessSort,
    setFilters,
    refreshData,
    handleStoreBlood,
    handleSendReminder,
  };
};

export default useDonationSchedule;
