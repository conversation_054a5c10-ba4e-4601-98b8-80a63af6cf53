@use "../base/variables" as vars;

.doctor-donor-management-page {
  display: flex;
  min-height: 100vh;
  background: #f6f6f6;

  .access-denied {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32px;

    .access-denied-content {
      background: #fff;
      padding: 48px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      text-align: center;
      border: 1px solid #e8e8e8;

      h2 {
        color: #d93e4c;
        margin-bottom: 16px;
        font-size: 2rem;
      }

      p {
        color: #666;
        font-size: 1.1rem;
        margin: 0;
      }
    }
  }

  .donor-content {
    flex: 1;
    margin-left: 0px;
    padding: 0 4px;
    transition: margin-left 0.3s ease;
    font-family: "Inter", sans-serif;

    .page-header {
      margin-bottom: 24px;
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8e8e8;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      h1 {
        color: #20374e;
        margin-bottom: 8px;
        font-size: 2rem;
        font-weight: 700;
        background: none;
        -webkit-text-fill-color: unset;
      }

      p {
        color: #666;
        font-size: 1.1rem;
        margin: 0 0 8px 0;
        font-weight: 500;
      }
    }

    .stats-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 16px;
      margin-bottom: 24px;

      .stat-card {
        background: #fff;
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
          font-size: 2.2rem;
          flex-shrink: 0;
        }

        .stat-content {
          .stat-number {
            font-size: 2rem;
            font-weight: 800;
            margin: 0 0 4px 0;
            color: #d93e4c;
            background: none;
            -webkit-text-fill-color: unset;
          }

          .stat-label {
            color: #666;
            font-size: 0.95rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }

        &.today {
          border-left: 4px solid #d93e4c;
          .stat-icon {
            color: #d93e4c;
          }
        }

        &.pending {
          border-left: 4px solid #faad14;
          .stat-icon {
            color: #faad14;
          }
        }

        &.completed {
          border-left: 4px solid #52c41a;
          .stat-icon {
            color: #52c41a;
          }
        }

        &.total {
          border-left: 4px solid #1890ff;
          .stat-icon {
            color: #1890ff;
          }
        }
      }
    }

    .filters-section {
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8e8e8;
      margin-bottom: 24px;
      display: flex;
      gap: 24px;
      align-items: center;
      flex-wrap: wrap;

      .filter-group {
        display: flex;
        align-items: center;
        gap: 12px;

        label {
          font-weight: 700;
          color: #20374e;
          font-size: 0.95rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        select {
          padding: 0.75rem 1rem;
          border: 2px solid #e9ecef;
          border-radius: 10px;
          font-size: 1rem;
          background: white;
          transition: all 0.3s ease;
          font-weight: 500;
          min-width: 150px;

          &:focus {
            outline: none;
            border-color: #17a2b8;
            box-shadow: 0 0 0 4px rgba(23, 162, 184, 0.1);
          }
        }
      }
    }

    .donor-table-container {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      border: 1px solid #e8e8e8;
      .donor-table {
        width: 100%;
        border-collapse: collapse;
        th,
        td {
          padding: 16px;
          text-align: left;
          border-bottom: 1px solid #f0f0f0;
          font-family: "Inter", sans-serif;
        }
        th {
          background: #f8f9fa;
          font-weight: 600;
          color: #20374e;
          font-size: 0.95rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        td {
          font-size: 0.95rem;
          color: #20374e;
        }
        tr:hover {
          background: #f8f9fa;
        }
        .blood-type-badge {
          display: inline-block;
          padding: 6px 25px;
          border-radius: 20px;
          font-weight: 700;
          font-size: 20px;
          text-align: center;
          min-height: 40px;
          min-width: 50px;

          &.positive {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
          }

          &.negative {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
          }
        }
      }
    }
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .update-modal,
    .status-modal {
      background: white;
      border-radius: 20px;
      max-width: 900px;
      width: 95%;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);

      .modal-header {
        padding: 1.5rem 2rem;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 20px 20px 0 0;

        h3 {
          margin: 0;
          background: linear-gradient(45deg, #17a2b8, #20c997);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-size: 1.3rem;
          font-weight: 700;
        }

        .close-btn {
          background: linear-gradient(45deg, #dc3545, #c82333);
          border: none;
          color: white;
          font-size: 1.5rem;
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &:hover {
            transform: rotate(90deg) scale(1.1);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
          }
        }
      }

      .modal-body {
        padding: 2rem;

        .donor-summary {
          margin-bottom: 2rem;
          text-align: center;

          h4 {
            margin: 0 0 0.5rem 0;
            color: #495057;
            font-size: 1.2rem;
          }

          p {
            margin: 0;
            color: #6c757d;
            font-weight: 500;
          }
        }

        .update-form {
          .form-section {
            margin-bottom: 2rem;

            h4 {
              margin: 0 0 1rem 0;
              color: #495057;
              font-size: 1.1rem;
              font-weight: 700;
              padding-bottom: 0.5rem;
              border-bottom: 2px solid #e9ecef;
            }

            .form-row {
              display: flex;
              gap: 1rem;
              margin-bottom: 1rem;

              .form-group {
                flex: 1;

                label {
                  display: block;
                  margin-bottom: 0.5rem;
                  font-weight: 700;
                  color: #495057;
                  font-size: 0.9rem;
                }

                input,
                select,
                textarea {
                  width: 100%;
                  padding: 0.75rem;
                  border: 2px solid #e9ecef;
                  border-radius: 8px;
                  font-size: 1rem;
                  font-weight: 500;
                  transition: all 0.3s ease;

                  &:focus {
                    outline: none;
                    border-color: #17a2b8;
                    box-shadow: 0 0 0 4px rgba(23, 162, 184, 0.1);
                  }
                }

                textarea {
                  resize: vertical;
                  min-height: 100px;
                }
              }
            }
          }
        }
      }

      .modal-footer {
        padding: 1.5rem 2rem;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        border-radius: 0 0 20px 20px;

        .btn {
          padding: 0.75rem 1.5rem;
          border: none;
          border-radius: 10px;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.btn-secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
            }
          }

          &.btn-primary {
            background: linear-gradient(45deg, #17a2b8, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .doctor-donor-management-page {
    .donor-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        padding: 1.5rem;
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;

        div h1 {
          font-size: 2rem;
        }
      }

      .stats-section {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1rem;
          flex-direction: column;
          text-align: center;

          .stat-icon {
            font-size: 2rem;
          }

          .stat-content .stat-number {
            font-size: 1.5rem;
          }
        }
      }

      .filters-section {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;

        .filter-group {
          flex-direction: column;
          align-items: stretch;

          select {
            min-width: auto;
          }
        }
      }

      .donor-table-container .donor-table {
        th,
        td {
          padding: 12px;
        }
      }
    }

    .modal-overlay .update-modal {
      width: 98%;
      max-height: 95vh;

      .modal-body {
        padding: 1.5rem;

        .update-form .form-section .form-row {
          flex-direction: column;
          gap: 0.5rem;
        }
      }

      .modal-footer {
        flex-direction: column;

        .btn {
          width: 100%;
        }
      }
    }
  }
}

// Status Modal specific styling
.doctor-donor-management-page .modal-overlay .status-modal {
  .donor-summary {
    background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.1),
      rgba(118, 75, 162, 0.1)
    );
    padding: 2rem;
    border-radius: 16px;
    margin-bottom: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    border: 1px solid rgba(102, 126, 234, 0.2);

    .summary-item {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      label {
        font-weight: 700;
        color: #667eea;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      span {
        font-weight: 700;
        color: #495057;
        font-size: 1.1rem;

        &.blood-type-badge {
          background: linear-gradient(135deg, #dc3545, #fd7e14);
          color: white;
          padding: 0.75rem 1rem;
          border-radius: 12px;
          font-size: 1rem;
          display: inline-block;
          width: fit-content;
          box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
          font-weight: 800;
        }

        &.status-badge {
          color: white;
          padding: 0.75rem 1rem;
          border-radius: 12px;
          font-size: 1rem;
          display: inline-block;
          width: fit-content;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
          font-weight: 800;
        }
      }
    }
  }

  .form-section {
    margin-bottom: 2rem;

    label {
      display: block;
      margin-bottom: 0.75rem;
      font-weight: 700;
      color: #667eea;
      font-size: 1rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    select,
    textarea {
      width: 100%;
      padding: 1rem;
      border: 2px solid rgba(102, 126, 234, 0.2);
      border-radius: 12px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.9);

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        background: white;
      }
    }

    select {
      cursor: pointer;
    }

    textarea {
      resize: vertical;
      min-height: 100px;
    }
  }

  .health-check-section {
    background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.05),
      rgba(118, 75, 162, 0.05)
    );
    padding: 2rem;
    border-radius: 16px;
    margin-bottom: 2rem;
    border: 1px solid rgba(102, 126, 234, 0.15);

    h4 {
      margin: 0 0 1.5rem 0;
      color: #667eea;
      font-weight: 800;
      font-size: 1.2rem;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .health-form {
      .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
      }

      .form-group {
        label {
          font-size: 0.9rem;
          margin-bottom: 0.5rem;
          display: block;
          font-weight: 700;
          color: #667eea;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        input {
          width: 100%;
          padding: 0.75rem;
          border: 2px solid rgba(102, 126, 234, 0.2);
          border-radius: 10px;
          font-size: 1rem;
          transition: all 0.3s ease;
          background: rgba(255, 255, 255, 0.9);

          &:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
            transform: translateY(-1px);
          }

          &::placeholder {
            color: #adb5bd;
            font-style: italic;
          }
        }
      }
    }
  }

  .modal-footer {
    padding: 2rem;
    background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.05),
      rgba(118, 75, 162, 0.05)
    );
    border-top: 1px solid rgba(102, 126, 234, 0.2);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    border-radius: 0 0 24px 24px;

    .btn {
      padding: 1rem 2rem;
      border: none;
      border-radius: 12px;
      font-weight: 700;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 1rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.btn-secondary {
        background: #6c757d;
        color: white;
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);

        &:hover {
          background: #5a6268;
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
      }

      &.btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
      }
    }
  }
}
