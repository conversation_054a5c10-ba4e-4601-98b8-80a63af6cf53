import React, { useState } from "react";
import { Tabs } from "antd";
import {
  ReloadOutlined,
  CalendarOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import DoctorLayout from "../../components/doctor/DoctorLayout";
import PageHeader from "../../components/doctor/PageHeader";
import BloodRequestDetailModal from "../../components/doctor/BloodRequestDetailModal";
import DoctorBloodRequestsTable from "../../components/doctor/blood-requests/DoctorBloodRequestsTable";
import DoctorBloodRequestsFilters from "../../components/doctor/blood-requests/DoctorBloodRequestsFilters";
import CreateBloodRequestModal from "../../components/doctor/blood-requests/CreateBloodRequestModal";
import BloodRequestStats from "../../components/doctor/blood-requests/BloodRequestStats";
import { useBloodRequestManagement } from "../../hooks/useBloodRequestManagement";
import { useBloodRequestCreation } from "../../hooks/useBloodRequestCreation";
import { COMPONENT_TYPES } from "../../constants/bloodInventoryConstants";
import {
  filterBloodRequests,
  getBloodTypes,
  getStatusOptions,
} from "../../utils/bloodRequestUtils";

/**
 * Get unique blood types from requests data (similar to manager page)
 * @param {Array} requests - Array of blood requests
 * @returns {Array} Array of unique blood types
 */
const getBloodTypesFromRequests = (requests) => {
  const bloodTypes = new Set();

  requests.forEach((request) => {
    let bloodType = "";

    // Try different ways to get blood type
    if (request.bloodType) {
      bloodType = request.bloodType;
    } else if (request.bloodTypeDisplay) {
      bloodType = request.bloodTypeDisplay;
    } else if (request.bloodGroup && request.rhType) {
      // Convert from separate fields to combined format
      let rhSymbol = "";
      if (
        request.rhType === "Rh+" ||
        request.rhType === "+" ||
        request.rhType === "Positive"
      ) {
        rhSymbol = "+";
      } else if (
        request.rhType === "Rh-" ||
        request.rhType === "-" ||
        request.rhType === "Negative"
      ) {
        rhSymbol = "-";
      }
      bloodType = `${request.bloodGroup}${rhSymbol}`;
    }

    if (bloodType) {
      bloodTypes.add(bloodType);
    }
  });

  return Array.from(bloodTypes).sort();
};
import "../../styles/pages/DoctorBloodRequestsPage.scss";
import "../../styles/components/DoctorPageHeader.scss";

/**
 * Optimized Doctor Blood Requests Page
 * Refactored with custom hooks and smaller components
 */
const DoctorBloodRequestsPage = () => {
  // Use custom hooks for state management
  const {
    requests,
    externalRequests,
    loading,
    activeTab,
    setActiveTab,
    isBloodDepartment,
    currentUser,
    loadBloodRequests,
    handleUpdateRequest,
    handleApproveExternal,
    handleRejectExternal,
    // handleCompleteRequest, // Removed - no longer needed for hematology doctors
  } = useBloodRequestManagement();

  const {
    showCreateModal,
    loading: createLoading,
    newRequest,
    setNewRequest,
    handleCreateRequest,
    openCreateModal,
    closeCreateModal,
  } = useBloodRequestCreation(loadBloodRequests);

  // Local state for UI
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [filters, setFilters] = useState({
    bloodType: "all",
    componentType: "all",
    status: "all",
  });

  // Helper functions
  const handleViewDetails = (request) => {
    setSelectedRequest(request);
    setShowDetailModal(true);
  };

  // Filter requests based on current filters
  const currentRequests =
    isBloodDepartment && activeTab === "external" ? externalRequests : requests;
  const filteredRequests = filterBloodRequests(currentRequests, filters);

  // Get data for components - use actual data like manager page
  const allCurrentRequests = [...requests, ...externalRequests];
  const bloodTypes = getBloodTypesFromRequests(allCurrentRequests);
  const statusOptions = getStatusOptions();

  // Tabs items for Ant Design
  const tabItems = isBloodDepartment
    ? [
        {
          key: "internal",
          label: `🏥 Yêu cầu máu nội bộ (${requests.length})`,
        },
        {
          key: "external",
          label: `🌐 Yêu cầu máu từ bên ngoài (${externalRequests.length})`,
        },
      ]
    : [];

  // Get page title and description based on context
  const getPageTitle = () => {
    if (isBloodDepartment) {
      return activeTab === "internal"
        ? "Yêu cầu máu nội bộ"
        : "Yêu cầu máu từ bên ngoài";
    }
    return "Yêu cầu Máu";
  };

  const getPageDescription = () => {
    if (isBloodDepartment) {
      return activeTab === "internal"
        ? "Xử lý các yêu cầu máu từ bác sĩ khoa khác trong bệnh viện"
        : "Xử lý các yêu cầu máu từ thành viên bên ngoài";
    }
    return "Quản lý và theo dõi các yêu cầu máu";
  };

  // Get page actions
  const getPageActions = () => {
    const actions = [
      {
        label: "Làm mới",
        icon: <ReloadOutlined />,
        onClick: loadBloodRequests,
        loading: loading,
      },
    ];

    // Add create button for non-blood department doctors
    if (!isBloodDepartment) {
      actions.push({
        label: "Tạo yêu cầu mới",
        type: "primary",
        icon: <PlusOutlined />,
        onClick: openCreateModal,
        loading: createLoading,
      });
    }

    return actions;
  };

  return (
    <DoctorLayout>
      <div className="doctor-blood-requests-content">
        <PageHeader
          title={getPageTitle()}
          description={getPageDescription()}
          icon={CalendarOutlined}
          actions={getPageActions()}
        />

        {/* Tabs for blood department */}
        {isBloodDepartment && (
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            style={{ marginBottom: 16 }}
          />
        )}

        {/* Statistics */}
        <BloodRequestStats
          requests={requests}
          externalRequests={externalRequests}
          activeTab={activeTab}
          isBloodDepartment={isBloodDepartment}
        />

        {/* Filters */}
        <DoctorBloodRequestsFilters
          filters={filters}
          setFilters={setFilters}
          bloodTypes={bloodTypes}
          statusOptions={statusOptions}
          componentTypes={Object.values(COMPONENT_TYPES)}
        />

        {/* Table */}
        <DoctorBloodRequestsTable
          data={filteredRequests}
          loading={loading}
          onViewDetails={handleViewDetails}
          onApprove={handleApproveExternal}
          onReject={handleRejectExternal}
          isBloodDepartment={isBloodDepartment}
          activeTab={activeTab}
        />

        {/* Detail Modal */}
        {showDetailModal && selectedRequest && (
          <BloodRequestDetailModal
            isOpen={showDetailModal}
            onClose={() => setShowDetailModal(false)}
            request={selectedRequest}
            onUpdate={handleUpdateRequest}
          />
        )}

        {/* Create Request Modal - Only for non-hematology doctors */}
        <CreateBloodRequestModal
          showCreateModal={showCreateModal}
          loading={createLoading}
          newRequest={newRequest}
          setNewRequest={setNewRequest}
          handleCreateRequest={handleCreateRequest}
          closeCreateModal={closeCreateModal}
          isBloodDepartment={isBloodDepartment}
        />
      </div>
    </DoctorLayout>
  );
};

export default DoctorBloodRequestsPage;
