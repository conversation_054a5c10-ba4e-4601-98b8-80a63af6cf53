@use "../base/variables" as *;
@use "../base/mixin" as *;

.navbar {
  @include flex-align(space-between, center);
  padding: 0 $spacing-xl;
  background-color: $white;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 1000;
  height: 70px;
  position: relative;

  .navbar-logo {
    font-weight: bold;
    font-size: 1.2rem;
    color: $text-color;
    display: flex;
    align-items: center;
    height: 100%;
    z-index: 1001;

    .logo-img {
      max-height: 48px;
      max-width: 160px;
      object-fit: contain;
      display: block;
    }
  }

  // Mobile Menu Toggle Button
  .mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;

    .hamburger-line {
      width: 100%;
      height: 3px;
      background-color: $secondary-color;
      border-radius: 2px;
      transition: all 0.3s ease;
      transform-origin: center;

      &.active {
        &:nth-child(1) {
          transform: rotate(45deg) translate(7px, 7px);
        }

        &:nth-child(2) {
          opacity: 0;
        }

        &:nth-child(3) {
          transform: rotate(-45deg) translate(7px, -7px);
        }
      }
    }
  }

  .navbar-nav {
    @include flex-align(center, center);
    gap: 40px;
    height: 100%;

    a {
      text-decoration: none;
      color: $text-color;
      font-weight: 900;
      font-size: 1rem;
      padding: 24px 0;
      position: relative;
      transition: color 0.3s;

      &.active {
        color: $secondary-color;

        &::after {
          content: "";
          position: absolute;
          bottom: 15px;
          left: 0;
          right: 0;
          height: 3px;
          background-color: $secondary-color;
        }
      }

      &:hover:not(.active) {
        color: $secondary-color;
      }
    }
  }

  .navbar-actions {
    @include flex-align(flex-end, center);
    gap: $spacing-base;

    a {
      text-decoration: none;
      font-weight: 900;
      font-size: 1rem;
      transition: all 0.3s;

      &.btn-login {
        color: $secondary-color;

        &:hover {
          color: $secondary-hover;
        }
      }

      &.btn-register,
      &.btn-logout {
        background-color: $secondary-color;
        color: $white;
        padding: 12px 24px;
        border-radius: 25px;

        &:hover {
          background-color: $secondary-hover;
        }
      }
    }

    .user-info {
      @include flex-align(center, center);
      gap: $spacing-sm;

      .user-name {
        color: $text-color;
        font-weight: 500;
        font-size: 1rem;
      }

      .user-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: $secondary-color;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $white;
        font-size: 0.9rem;
      }
    }
  }

  // Mobile Navigation Overlay
  .mobile-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.active {
      opacity: 1;
      visibility: visible;
    }

    .mobile-nav {
      position: absolute;
      top: 0;
      right: 0;
      width: 280px;
      height: 100%;
      background: $white;
      box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
      transform: translateX(100%);
      transition: transform 0.3s ease;
      overflow-y: auto;

      .mobile-nav-items {
        padding: $spacing-lg 0;

        .mobile-nav-item {
          display: block;
          padding: $spacing-sm $spacing-lg;
          text-decoration: none;
          color: $text-color;
          font-weight: 500;
          border: none;
          background: none;
          width: 100%;
          text-align: left;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f8f9fa;
            color: $primary-color;
          }

          &.active {
            background-color: rgba($primary-color, 0.1);
            color: $primary-color;
            font-weight: 600;
          }

          &.btn-login {
            color: $secondary-color;
            font-weight: 600;
          }

          &.btn-register {
            background-color: $secondary-color;
            color: $white;
            margin: $spacing-sm $spacing-lg;
            padding: $spacing-sm $spacing-base;
            border-radius: 25px;
            text-align: center;

            &:hover {
              background-color: $secondary-hover;
            }
          }
        }

        .mobile-nav-divider {
          height: 1px;
          background: #e9ecef;
          margin: $spacing-sm 0;
        }
      }
    }

    &.active .mobile-nav {
      transform: translateX(0);
    }
  }

  // Responsive Design
  @include tablet {
    padding: 0 $spacing-lg;

    .desktop-nav {
      gap: 24px;
    }

    .desktop-actions {
      gap: $spacing-sm;
    }
  }

  @include mobile {
    .desktop-nav {
      display: none;
    }

    .desktop-actions {
      display: none;
    }

    .mobile-menu-toggle {
      display: flex;
    }
  }

  // Small mobile devices
  @media (max-width: 480px) {
    .mobile-nav-overlay .mobile-nav {
      width: 100%;
    }

    .navbar-logo {
      font-size: 1.1rem;
    }
  }
}