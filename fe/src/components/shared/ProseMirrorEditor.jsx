import React, {
  useEffect,
  useRef,
  useImperativeHandle,
  forwardRef,
} from "react";
import { EditorState } from "prosemirror-state";
import { EditorView } from "prosemirror-view";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>arser, DOMSerializer } from "prosemirror-model";
import { schema } from "prosemirror-schema-basic";
import { addListNodes } from "prosemirror-schema-list";
import { exampleSetup } from "prosemirror-example-setup";
import { MenuItem, Dropdown } from "prosemirror-menu";
import { toggleMark } from "prosemirror-commands";
import { uploadImage } from "../../services/uploadService";
import { message } from "antd";
import "prosemirror-view/style/prosemirror.css";
import "prosemirror-menu/style/menu.css";
import "./ProseMirrorEditor.scss";

// Create text color mark
const textColor = {
  attrs: { color: { default: null } },
  parseDOM: [
    {
      style: "color",
      getAttrs: (color) => ({ color }),
    },
  ],
  toDOM: (mark) => ["span", { style: `color: ${mark.attrs.color}` }, 0],
};

// Create background color mark
const backgroundColor = {
  attrs: { backgroundColor: { default: null } },
  parseDOM: [
    {
      style: "background-color",
      getAttrs: (backgroundColor) => ({ backgroundColor }),
    },
  ],
  toDOM: (mark) => [
    "span",
    { style: `background-color: ${mark.attrs.backgroundColor}` },
    0,
  ],
};

// Create schema with list support and color marks (simplified)
const mySchema = new Schema({
  nodes: addListNodes(schema.spec.nodes, "paragraph block*", "block"),
  marks: {
    ...schema.spec.marks,
    textColor,
    backgroundColor,
  },
});

// Color palette
const colors = [
  "#000000",
  "#1c1c1c",
  "#383838",
  "#5a5a5a",
  "#7a7a7a",
  "#9a9a9a",
  "#bababa",
  "#ffffff",
  "#ff0000",
  "#ff8000",
  "#ffff00",
  "#80ff00",
  "#00ff00",
  "#00ff80",
  "#00ffff",
  "#0080ff",
  "#0000ff",
  "#8000ff",
  "#ff00ff",
  "#ff0080",
  "#800000",
  "#804000",
  "#808000",
  "#408000",
  "#008000",
  "#008040",
  "#008080",
  "#004080",
  "#000080",
  "#400080",
  "#800080",
  "#800040",
];

// Helper function to apply color
const applyColor = (markType, color) => (state, dispatch) => {
  const { from, to } = state.selection;
  if (from === to) return false;

  const mark = markType.create({
    [markType === mySchema.marks.textColor ? "color" : "backgroundColor"]:
      color,
  });
  if (dispatch) {
    dispatch(state.tr.addMark(from, to, mark));
  }
  return true;
};

// Create color menu items
const createColorMenuItem = (markType, title, icon) => {
  return new Dropdown(
    colors.map(
      (color) =>
        new MenuItem({
          title: `${title}: ${color}`,
          run: applyColor(markType, color),
          render: () => {
            const span = document.createElement("span");
            span.style.display = "inline-block";
            span.style.width = "16px";
            span.style.height = "16px";
            span.style.backgroundColor = color;
            span.style.border = "1px solid #ccc";
            span.style.marginRight = "2px";
            span.title = color;
            return span;
          },
        })
    ),
    {
      label: icon,
      title: title,
    }
  );
};

const ProseMirrorEditor = forwardRef(
  (
    {
      value = "",
      onChange,
      placeholder = "Nhập nội dung...",
      height = 400,
      disabled = false,
      className = "",
    },
    ref
  ) => {
    const editorRef = useRef(null);
    const viewRef = useRef(null);
    const fileInputRef = useRef(null);

    useImperativeHandle(ref, () => ({
      getContent: () => {
        if (viewRef.current) {
          const content = viewRef.current.state.doc;
          const fragment = DOMSerializer.fromSchema(mySchema).serializeFragment(
            content.content
          );
          const tempDiv = document.createElement("div");
          tempDiv.appendChild(fragment);
          return tempDiv.innerHTML;
        }
        return "";
      },
      focus: () => {
        if (viewRef.current) {
          viewRef.current.focus();
        }
      },
    }));

    // Handle image upload
    const handleImageUpload = async (file) => {
      if (!file) return;

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        message.error("Kích thước ảnh không được vượt quá 5MB");
        return;
      }

      // Validate file type
      if (!file.type.startsWith("image/")) {
        message.error("Vui lòng chọn file ảnh (JPG, PNG, GIF)");
        return;
      }

      try {
        message.loading({ content: "Đang upload ảnh...", key: "uploading" });

        const uploadResult = await uploadImage(file);

        if (uploadResult.success && viewRef.current) {
          // Insert image as HTML at current cursor position
          const { state, dispatch } = viewRef.current;
          const imgHTML = `<img src="${uploadResult.url}" alt="${file.name}" style="max-width: 100%; height: auto;" />`;

          // Create a document fragment from HTML
          const parser = DOMParser.fromSchema(mySchema);
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = imgHTML;
          const parsedNodes = parser.parse(tempDiv).content;

          // Insert the parsed content
          const transaction = state.tr.replaceSelectionWith(
            parsedNodes.firstChild
          );
          dispatch(transaction);

          message.success({
            content: "Upload ảnh thành công!",
            key: "uploading",
          });
        } else {
          throw new Error(uploadResult.message || "Upload thất bại");
        }
      } catch (error) {
        console.error("Error uploading image:", error);

        // Fallback: Insert base64 image directly
        try {
          const reader = new FileReader();
          reader.onload = (e) => {
            if (viewRef.current) {
              const { state, dispatch } = viewRef.current;
              const base64 = e.target.result;
              const imgHTML = `<img src="${base64}" alt="${file.name}" style="max-width: 100%; height: auto;" />`;

              const parser = DOMParser.fromSchema(mySchema);
              const tempDiv = document.createElement("div");
              tempDiv.innerHTML = imgHTML;
              const parsedNodes = parser.parse(tempDiv).content;

              const transaction = state.tr.replaceSelectionWith(
                parsedNodes.firstChild
              );
              dispatch(transaction);

              message.success({
                content: "Ảnh đã được chèn!",
                key: "uploading",
              });
            }
          };
          reader.readAsDataURL(file);
        } catch (fallbackError) {
          message.error({
            content: "Không thể chèn ảnh. Vui lòng thử lại.",
            key: "uploading",
          });
        }
      }
    };

    // Handle file input change
    const handleFileInputChange = (e) => {
      const file = e.target.files[0];
      if (file) {
        handleImageUpload(file);
      }
      // Reset input value to allow uploading the same file again
      e.target.value = "";
    };

    // Trigger file input
    const triggerImageUpload = () => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    };

    // Apply color to selected text
    const applyTextColor = (color) => {
      if (!viewRef.current) return;

      const { state, dispatch } = viewRef.current;
      const { from, to } = state.selection;

      if (from === to) return; // No selection

      const mark = mySchema.marks.textColor.create({ color });
      dispatch(state.tr.addMark(from, to, mark));
    };

    const applyBackgroundColor = (color) => {
      if (!viewRef.current) return;

      const { state, dispatch } = viewRef.current;
      const { from, to } = state.selection;

      if (from === to) return; // No selection

      const mark = mySchema.marks.backgroundColor.create({
        backgroundColor: color,
      });
      dispatch(state.tr.addMark(from, to, mark));
    };

    useEffect(() => {
      if (!editorRef.current) return;

      let startDoc;
      if (value) {
        try {
          // Try to parse HTML content
          const parser = DOMParser.fromSchema(mySchema);
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = value;
          startDoc = parser.parse(tempDiv);
        } catch (error) {
          // If parsing fails, create a basic document with the text
          startDoc = mySchema.node("doc", null, [
            mySchema.node(
              "paragraph",
              null,
              value ? [mySchema.text(value)] : []
            ),
          ]);
        }
      } else {
        startDoc = mySchema.node("doc", null, [
          mySchema.node("paragraph", null, []),
        ]);
      }

      const state = EditorState.create({
        doc: startDoc,
        plugins: exampleSetup({ schema: mySchema, menuBar: true }),
      });

      const view = new EditorView(editorRef.current, {
        state,
        editable: () => !disabled,
        dispatchTransaction(transaction) {
          const newState = view.state.apply(transaction);
          view.updateState(newState);

          if (onChange && transaction.docChanged) {
            // Convert to HTML
            const content = DOMSerializer.fromSchema(
              mySchema
            ).serializeFragment(newState.doc.content);
            const tempDiv = document.createElement("div");
            tempDiv.appendChild(content);
            onChange(tempDiv.innerHTML);
          }
        },
        attributes: {
          class: `prosemirror-editor ${disabled ? "disabled" : ""}`,
          "data-placeholder": placeholder,
        },
        // Handle drag and drop for images
        handleDrop(view, event, slice, moved) {
          if (moved || !event.dataTransfer) return false;

          const files = Array.from(event.dataTransfer.files);
          const imageFiles = files.filter((file) =>
            file.type.startsWith("image/")
          );

          if (imageFiles.length > 0) {
            event.preventDefault();
            imageFiles.forEach((file) => handleImageUpload(file));
            return true;
          }

          return false;
        },
        // Handle paste for images
        handlePaste(view, event) {
          if (!event.clipboardData) return false;

          const files = Array.from(event.clipboardData.files);
          const imageFiles = files.filter((file) =>
            file.type.startsWith("image/")
          );

          if (imageFiles.length > 0) {
            event.preventDefault();
            imageFiles.forEach((file) => handleImageUpload(file));
            return true;
          }

          return false;
        },
      });

      viewRef.current = view;

      return () => {
        if (viewRef.current) {
          viewRef.current.destroy();
          viewRef.current = null;
        }
      };
    }, [disabled, placeholder]);

    // Update content when value prop changes
    useEffect(() => {
      if (viewRef.current && value !== undefined) {
        const currentContent = (() => {
          try {
            const content = DOMSerializer.fromSchema(
              mySchema
            ).serializeFragment(viewRef.current.state.doc.content);
            const tempDiv = document.createElement("div");
            tempDiv.appendChild(content);
            return tempDiv.innerHTML;
          } catch (error) {
            return "";
          }
        })();

        // Only update if content has actually changed
        if (currentContent !== value) {
          try {
            const parser = DOMParser.fromSchema(mySchema);
            const tempDiv = document.createElement("div");
            tempDiv.innerHTML = value || "<p></p>";
            const doc = parser.parse(tempDiv);

            const newState = EditorState.create({
              doc,
              plugins: viewRef.current.state.plugins,
            });

            viewRef.current.updateState(newState);
          } catch (error) {
            console.error("Error updating ProseMirror content:", error);
          }
        }
      }
    }, [value]);

    return (
      <div className={`prosemirror-wrapper ${className}`}>
        {/* Hidden file input for image upload */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileInputChange}
          style={{ display: "none" }}
          multiple={false}
        />

        {/* Toolbar */}
        <div className="prosemirror-toolbar">
          {/* Image upload button */}
          <div className="toolbar-group">
            <button
              type="button"
              className="toolbar-button"
              onClick={triggerImageUpload}
              disabled={disabled}
              title="Chèn ảnh"
            >
              🖼️ Ảnh
            </button>
          </div>

          {/* Color picker toolbar */}
          <div className="toolbar-group">
            <div className="color-group">
              <label>Màu chữ:</label>
              <div className="color-button">
                <input
                  type="color"
                  onChange={(e) => applyTextColor(e.target.value)}
                  title="Chọn màu chữ"
                  disabled={disabled}
                />
              </div>
            </div>
            <div className="color-group">
              <label>Màu nền:</label>
              <div className="color-button">
                <input
                  type="color"
                  onChange={(e) => applyBackgroundColor(e.target.value)}
                  title="Chọn màu nền"
                  disabled={disabled}
                />
              </div>
            </div>
          </div>
        </div>

        <div
          ref={editorRef}
          style={{ minHeight: height }}
          className="prosemirror-container"
        />

        {/* Upload instructions */}
        <div className="upload-instructions">
          <small>
            💡 Bạn có thể kéo thả ảnh vào editor hoặc dán ảnh từ clipboard
            (Ctrl+V)
          </small>
        </div>
      </div>
    );
  }
);

ProseMirrorEditor.displayName = "ProseMirrorEditor";

export default ProseMirrorEditor;
