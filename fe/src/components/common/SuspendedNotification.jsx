import React, { useEffect } from 'react';
import { notification } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';

const SuspendedNotification = ({ show, onClose, message }) => {
  useEffect(() => {
    if (show) {
      notification.error({
        message: 'Tà<PERSON> khoản bị đình chỉ',
        description: message || 'Tài khoản của bạn đã bị đình chỉ hoạt động. Bạn sẽ được đăng xuất khỏi hệ thống.',
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
        duration: 0, // Don't auto close
        placement: 'topRight',
        onClose: onClose,
        style: {
          width: 400,
        },
      });
    }
  }, [show, message, onClose]);

  return null; // This component doesn't render anything directly
};

export default SuspendedNotification;
