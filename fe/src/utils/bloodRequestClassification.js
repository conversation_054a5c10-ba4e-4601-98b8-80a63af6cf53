/**
 * Utility functions for classifying blood requests
 */

/**
 * Classify blood request as internal or external based on available data
 * @param {Object} request - Blood request data
 * @returns {Object} Classification result
 */
export const classifyBloodRequest = (request) => {
  // Check if userID is valid
  const hasValidUserID =
    request.userID &&
    request.userID !== 0 &&
    request.userID !== "0" &&
    request.userID !== null &&
    request.userID !== undefined;

  // Heuristics for internal requests (from doctors)
  const isInternalByRelationship = request.relationship === "Bác sĩ phụ trách";

  const isInternalByDoctorInfo =
    request.doctorName && request.doctorPhone && request.facilityName;

  const isInternalByStatus = request.status === 1; // Auto-approved requests are usually from doctors

  // Heuristics for external requests (from members)
  const isExternalByRelationship = [
    "Chính bản thân tôi",
    "bạn bè",
    "gia đình",
    "Other",
  ].includes(request.relationship);

  // Classification logic
  let classification = {
    isInternal: false,
    isExternal: false,
    confidence: "low",
    reason: "unknown",
    userRole: "Unknown",
    userRoleID: null,
  };

  if (hasValidUserID) {
    classification.confidence = "high";
    classification.reason = "valid_user_id";
    // Will be determined by API call
  } else if (isInternalByRelationship) {
    classification.isInternal = true;
    classification.confidence = "high";
    classification.reason = "relationship_doctor";
    classification.userRole = "Unknown-Doctor";
  } else if (isInternalByDoctorInfo) {
    classification.isInternal = true;
    classification.confidence = "medium";
    classification.reason = "doctor_info_present";
    classification.userRole = "Unknown-Doctor";
  } else if (isInternalByStatus) {
    classification.isInternal = true;
    classification.confidence = "low";
    classification.reason = "auto_approved_status";
    classification.userRole = "Unknown-Doctor";
  } else if (isExternalByRelationship) {
    classification.isExternal = true;
    classification.confidence = "medium";
    classification.reason = "relationship_member";
    classification.userRole = "Unknown-Member";
  } else {
    // Default to external for unknown cases
    classification.isExternal = true;
    classification.confidence = "low";
    classification.reason = "default_fallback";
    classification.userRole = "Unknown-Member";
  }

  return classification;
};

/**
 * Get user role display name
 * @param {string} userRole - User role
 * @param {number} userRoleID - User role ID
 * @returns {string} Display name
 */
export const getUserRoleDisplayName = (userRole, userRoleID) => {
  if (userRoleID === 1) return "Thành viên";
  if (userRoleID === 2) return "Bác sĩ";
  if (userRole === "Unknown-Doctor") return "Bác sĩ (Chưa xác định)";
  if (userRole === "Unknown-Member") return "Thành viên (Chưa xác định)";
  return userRole || "Không xác định";
};

/**
 * Validate userID
 * @param {any} userID - User ID to validate
 * @returns {boolean} Is valid
 */
export const isValidUserID = (userID) => {
  return (
    userID &&
    userID !== 0 &&
    userID !== "0" &&
    userID !== null &&
    userID !== undefined &&
    !isNaN(userID)
  );
};
