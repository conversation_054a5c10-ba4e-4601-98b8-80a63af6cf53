import React from 'react';
import { Modal, Button, Result } from 'antd';
import { ExclamationCircleOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons';

const SuspendedAccountModal = ({ visible, onClose, userEmail }) => {
  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
      centered
      closable={false}
      maskClosable={false}
    >
      <Result
        icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
        title="Tài khoản bị đình chỉ"
        subTitle={
          <div style={{ textAlign: 'left', marginTop: 20 }}>
            <p style={{ fontSize: '16px', marginBottom: 16 }}>
              Tài khoản <strong>{userEmail}</strong> hiện đang bị đình chỉ hoạt động.
            </p>
            <p style={{ marginBottom: 12 }}>
              <strong>Lý do có thể:</strong>
            </p>
            <ul style={{ paddingLeft: 20, marginBottom: 16 }}>
              <li>Vi phạm quy định sử dụng hệ thống</li>
              <li>Hoạt động bất thường được phát hiện</li>
              <li>Yêu cầu từ quản trị viên</li>
            </ul>
            <p style={{ marginBottom: 16 }}>
              <strong>Để được hỗ trợ, vui lòng liên hệ:</strong>
            </p>
            <div style={{ background: '#f5f5f5', padding: 16, borderRadius: 8 }}>
              <p style={{ margin: 0, marginBottom: 8 }}>
                <PhoneOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                <strong>Hotline:</strong> 1900-xxxx
              </p>
              <p style={{ margin: 0 }}>
                <MailOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                <strong>Email:</strong> <EMAIL>
              </p>
            </div>
          </div>
        }
        extra={
          <Button type="primary" size="large" onClick={onClose}>
            Đã hiểu
          </Button>
        }
      />
    </Modal>
  );
};

export default SuspendedAccountModal;
