import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation, useSearchParams } from "react-router-dom";
import { getPasswordValidationError, validatePasswordConfirmation } from "../../utils/validation";
import "../../styles/components/ResetPasswordForm.scss";
import authService from "../../services/authService";
import { FaEye, FaEyeSlash } from "react-icons/fa";

export default function ResetPasswordForm() {
  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    password: false,
    confirmPassword: false,
  });
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();

  // Get email and token from URL params (typical password reset flow)
  const email = searchParams.get('email') || location.state?.email;
  const token = searchParams.get('token');

  useEffect(() => {
    console.log("Reset password form - Email:", email);
    console.log("Reset password form - Token:", token);
    console.log("Reset password form - Location state:", location.state);
    console.log("Reset password form - Search params:", Object.fromEntries(searchParams));

    // If no token, redirect to forgot password (unless in test mode)
    const isTestMode = searchParams.get('test') === 'true';
    if (!token && !isTestMode) {
      console.log("No token found, redirecting to forgot password");
      navigate('/forgot-password');
    }
  }, [token, navigate, email, location.state, searchParams]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const togglePasswordVisibility = (fieldName) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate password
    const passwordError = getPasswordValidationError(formData.password);
    if (passwordError) {
      newErrors.password = passwordError;
    }

    // Validate password confirmation
    const confirmPasswordError = validatePasswordConfirmation(
      formData.password,
      formData.confirmPassword
    );
    if (confirmPasswordError) {
      newErrors.confirmPassword = confirmPasswordError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});
    setSuccessMessage("");

    try {
      const resetData = {
        email: email,
        token: token,
        newPassword: formData.password
      };

      const result = await authService.resetPassword(resetData);

      if (result.success) {
        setSuccessMessage("Mật khẩu đã được đặt lại thành công!");
        // Redirect to login page after 2 seconds
        setTimeout(() => {
          navigate("/login", {
            state: {
              message: "Mật khẩu đã được đặt lại thành công. Vui lòng đăng nhập với mật khẩu mới.",
              email: email
            }
          });
        }, 2000);
      } else {
        setErrors({
          submit: result.error || "Không thể đặt lại mật khẩu. Vui lòng thử lại."
        });
      }
    } catch (error) {
      console.error("Reset password failed:", error);
      setErrors({
        submit: "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau."
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="reset-password-form__container">
      <div className="reset-password-form__box">
        <div className="reset-password-form__logo">LOGO</div>
        <div className="reset-password-form__title">
          ĐẶT LẠI MẬT KHẨU
        </div>
        <div className="reset-password-form__description">
          Nhập mật khẩu mới cho tài khoản của bạn.
        </div>

        <form className="reset-password-form__form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="reset-password-form__label">MẬT KHẨU MỚI</label>
            <div className="password-input-container">
              <input
                className={`reset-password-form__input ${errors.password ? "error" : ""}`}
                type={showPasswords.password ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Tối thiểu 6 ký tự bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt"
                required
              />
              <button
                type="button"
                className="password-toggle-btn"
                onClick={() => togglePasswordVisibility('password')}
              >
                {showPasswords.password ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.password && (
              <div className="reset-password-form__error">{errors.password}</div>
            )}
          </div>

          <div className="form-group">
            <label className="reset-password-form__label">XÁC NHẬN MẬT KHẨU MỚI</label>
            <div className="password-input-container">
              <input
                className={`reset-password-form__input ${errors.confirmPassword ? "error" : ""}`}
                type={showPasswords.confirmPassword ? "text" : "password"}
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                placeholder="Nhập lại mật khẩu để xác nhận"
                required
              />
              <button
                type="button"
                className="password-toggle-btn"
                onClick={() => togglePasswordVisibility('confirmPassword')}
              >
                {showPasswords.confirmPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
            {errors.confirmPassword && (
              <div className="reset-password-form__error">{errors.confirmPassword}</div>
            )}
          </div>

          {successMessage && (
            <div className="reset-password-form__success">{successMessage}</div>
          )}
          {errors.submit && (
            <div className="reset-password-form__error">{errors.submit}</div>
          )}

          <button
            className="reset-password-form__submit"
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? "ĐANG ĐẶT LẠI..." : "ĐẶT LẠI MẬT KHẨU"}
          </button>
        </form>

        <div className="reset-password-form__back">
          <Link to="/login">
            ← Quay lại đăng nhập
          </Link>
        </div>
      </div>
    </div>
  );
}
