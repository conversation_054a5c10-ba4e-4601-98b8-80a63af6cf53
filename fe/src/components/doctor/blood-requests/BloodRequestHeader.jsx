import React from "react";

/**
 * Component for blood request page header with title and create button
 */
const BloodRequestHeader = ({
  isBloodDepartment,
  activeTab,
  loading,
  onCreateClick,
}) => {
  const getTitle = () => {
    if (isBloodDepartment) {
      return activeTab === "internal"
        ? "Yêu cầu máu nội bộ"
        : "<PERSON>êu cầu máu từ bên ngoài";
    }
    return "Danh sách yêu cầu máu";
  };

  const getDescription = () => {
    if (!isBloodDepartment) return null;
    
    return (
      <p style={{ margin: "4px 0 0 0", color: "#666", fontSize: "14px" }}>
        {activeTab === "internal"
          ? "<PERSON>ác yêu cầu máu từ bác sĩ khoa khác trong bệnh viện"
          : "Các yêu cầu máu từ thành viên bên ngoài"}
      </p>
    );
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 16,
      }}
    >
      <div>
        <h2 style={{ margin: 0 }}>{getTitle()}</h2>
        {getDescription()}
      </div>
      {!isBloodDepartment && (
        <button
          className="btn btn-primary"
          onClick={onCreateClick}
          disabled={loading}
          style={{
            backgroundColor: "#1890ff",
            borderColor: "#1890ff",
            color: "white",
            padding: "8px 16px",
            borderRadius: "6px",
            border: "none",
            cursor: loading ? "not-allowed" : "pointer",
            opacity: loading ? 0.6 : 1,
          }}
        >
          + Tạo yêu cầu máu
        </button>
      )}
    </div>
  );
};

export default BloodRequestHeader;
