import { useEffect } from "react";
import {
  <PERSON><PERSON>, <PERSON><PERSON>, Card, Avatar, Row, Col, Descriptions, Badge,
  Divider, Select, Input, Tag
} from "antd";
import {
  EditOutlined, UserOutlined, CheckCircleOutlined
} from "@ant-design/icons";


const { TextArea } = Input;

/**
 * Modal cập nhật trạng thái hiến máu
 */
const DonorStatusModal = ({
  visible,
  onCancel,
  onSave,
  selectedDonor,
  statusUpdateData,
  setStatusUpdateData
}) => {

  const getTimeSlotText = (slot) => {
    if (slot === "morning" || slot === "Sáng (7:00-12:00)") {
      return "7:00 - 12:00";
    } else if (slot === "afternoon" || slot === "Chiều (13:00-17:00)") {
      return "13:00 - 17:00";
    }
    return slot || "N/A";
  };

  const getProcessStepText = (process) => {
    const steps = {
      1: "<PERSON><PERSON><PERSON> ký",
      2: "<PERSON><PERSON><PERSON><PERSON> sức khỏe cơ bản",
      3: "<PERSON><PERSON><PERSON> máu",
      4: "<PERSON><PERSON><PERSON> nghiệm máu",
      5: "<PERSON>hập kho"
    };
    return steps[process] || "Không xác định";
  };

  if (!selectedDonor) return null;

  // Debug log to check statusUpdateData
  console.log("DonorStatusModal - statusUpdateData:", statusUpdateData);
  console.log("DonorStatusModal - selectedDonor:", selectedDonor);

  // Sync statusUpdateData when selectedDonor changes
  useEffect(() => {
    if (selectedDonor && visible) {
      console.log("Syncing statusUpdateData with selectedDonor");
      setStatusUpdateData(prev => ({
        ...prev,
        status: selectedDonor.status ? "2" : "1",
        process: selectedDonor.process?.toString() || "2",
        notes: selectedDonor.notes || "",
      }));
    }
  }, [selectedDonor?.id, visible, setStatusUpdateData]);

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <EditOutlined style={{ color: '#1890ff' }} />
          <span>Cập nhật trạng thái hiến máu</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={700}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={onSave}>
          <CheckCircleOutlined /> Lưu cập nhật
        </Button>
      ]}
    >
      {/* Donor Summary */}
      <Card
        size="small"
        style={{ marginBottom: 16, backgroundColor: '#f8f9fa' }}
      >
        <Row gutter={16}>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Avatar size={64} icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
              <div style={{ marginTop: 8, fontWeight: 'bold', color: '#1890ff' }}>
                {selectedDonor.name}
              </div>
            </div>
          </Col>
          <Col span={16}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Nhóm máu">
                <Tag color="red" style={{ fontSize: '14px' }}>
                  {selectedDonor.bloodType}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Trạng thái hiện tại">
                <Badge
                  color={selectedDonor.status ? "#52c41a" : "#ff4d4f"}
                  text={selectedDonor.status ? "Chấp nhận" : "Không chấp nhận"}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Quy trình hiện tại">
                {selectedDonor.process ? (
                  <Tag color="blue">
                    Bước {selectedDonor.process}: {getProcessStepText(selectedDonor.process)}
                  </Tag>
                ) : (
                  <Tag color="default">Chưa có quy trình</Tag>
                )}
              </Descriptions.Item>

              {selectedDonor.doctorId && (
                <Descriptions.Item label="Bác sĩ phụ trách">
                  ID: {selectedDonor.doctorId}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Col>
        </Row>
      </Card>

      {/* Status Update Section */}
      <Divider orientation="left">🔄 Cập nhật trạng thái</Divider>
      <div style={{ marginBottom: 16 }}>
        <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
          Chọn trạng thái:
        </label>
        <Select
          style={{ width: '100%' }}
          value={statusUpdateData.status}
          onChange={(value) =>
            setStatusUpdateData((prev) => ({
              ...prev,
              status: value,
            }))
          }
          placeholder="Chọn trạng thái"
        >
          <Select.Option value="1">
            <Badge color="#ff4d4f" text="Không chấp nhận" />
          </Select.Option>
          <Select.Option value="2">
            <Badge color="#52c41a" text="Chấp nhận" />
          </Select.Option>
        </Select>
      </div>

      {/* Process Selection - Always show regardless of status */}
      <div style={{ marginBottom: 16 }}>
        <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
          Chọn quy trình hiến máu:
        </label>
        <Select
          style={{ width: '100%' }}
          value={statusUpdateData.process}
          onChange={(value) =>
            setStatusUpdateData((prev) => ({
              ...prev,
              process: value,
            }))
          }
          placeholder="Chọn bước quy trình"
        >
          <Select.Option value="2">
            <Badge color="#1890ff" text="Khám sức khỏe cơ bản" />
          </Select.Option>
          <Select.Option value="3">
            <Badge color="#722ed1" text="Lấy máu" />
          </Select.Option>
          <Select.Option value="4">
            <Badge color="#fa8c16" text="Xét nghiệm máu" />
          </Select.Option>
          <Select.Option value="5">
            <Badge color="#52c41a" text="Nhập kho" />
          </Select.Option>
        </Select>
      </div>





      {/* Notes Section */}
      <Divider orientation="left">📝 Ghi chú của bác sĩ</Divider>
      <TextArea
        key={`notes-${selectedDonor?.id}`} // Force re-render when donor changes
        value={statusUpdateData.notes || ""}
        onChange={(e) => {
          console.log("Notes changed:", e.target.value); // Debug log
          setStatusUpdateData((prev) => ({
            ...prev,
            notes: e.target.value,
          }));
        }}
        placeholder="Nhập ghi chú về tình trạng sức khỏe hoặc quá trình hiến máu..."
        rows={4}
        style={{ marginBottom: 16 }}
      />
    </Modal>
  );
};

export default DonorStatusModal;
