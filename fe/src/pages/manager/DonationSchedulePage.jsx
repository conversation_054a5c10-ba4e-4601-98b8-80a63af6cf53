import { useState } from "react";
import { <PERSON><PERSON>, message } from "antd";
import { CalendarOutlined, ReloadOutlined } from "@ant-design/icons";
import ManagerLayout from "../../components/manager/ManagerLayout";
import PageHeader from "../../components/manager/PageHeader";
import DonationScheduleTabs from "../../components/manager/donation-schedule/DonationScheduleTabs";
import DonationDetailModal from "../../components/manager/donation-schedule/DonationDetailModal";
import ProcessWorkflowModal from "../../components/shared/ProcessWorkflowModal";
import ManagerBloodCheckInModal from "../../components/manager/blood-inventory/ManagerBloodCheckInModal";
import useDonationSchedule from "../../hooks/useDonationSchedule";
import {
  getScheduleDonations,
  getProcessDonations
} from "../../utils/donationScheduleHelpers";
import { checkInBloodInventory } from "../../services/bloodInventoryService";
import { getBloodComponentId } from "../../constants/bloodInventoryConstants";
import authService from "../../services/authService";
import "../../styles/pages/DonationSchedulePage.scss";
import "../../styles/components/PageHeader.scss";

const DonationSchedulePage = () => {
  const {
    // Data
    allDonations,
    loading,
    selectedDonation,
    processModalVisible,
    detailModalVisible,
    activeTab,
    scheduleSort,
    processSort,
    filters,
    isManager,

    // Actions
    setSelectedDonation,
    setProcessModalVisible,
    setDetailModalVisible,
    setActiveTab,
    setScheduleSort,
    setProcessSort,
    setFilters,
    refreshData,
    handleStoreBlood,
    handleSendReminder,
  } = useDonationSchedule();

  // State for blood check-in modal
  const [showCheckInModal, setShowCheckInModal] = useState(false);
  const [loadingCheckIn, setLoadingCheckIn] = useState(false);
  const [checkInForm, setCheckInForm] = useState({
    bloodGroup: "",
    rhType: "",
    componentType: "",
    bagType: "",
    quantity: 0,
    notes: "",
  });
  const [donationToStore, setDonationToStore] = useState(null);

  // Handle blood check-in
  const handleCheckIn = async () => {
    // Validation form trước khi gửi
    if (
      !checkInForm.bloodGroup ||
      !checkInForm.rhType ||
      !checkInForm.componentType ||
      !checkInForm.quantity ||
      checkInForm.quantity <= 0
    ) {
      message.error("Vui lòng điền đầy đủ thông tin hợp lệ!");
      return;
    }

    setLoadingCheckIn(true);
    try {
      const userId = authService.getCurrentUser()?.id;

      if (!userId) {
        throw new Error("Không tìm thấy thông tin người dùng");
      }

      // Tạo payload đúng format API
      const payload = {
        bloodGroup: checkInForm.bloodGroup,
        rhType: checkInForm.rhType,
        componentId: getBloodComponentId(checkInForm.componentType),
        quantity: parseInt(checkInForm.quantity),
        bagType: checkInForm.bagType || "450ml",
        notes: checkInForm.notes || "",
        performedBy: parseInt(userId),
      };

      await checkInBloodInventory(payload);

      message.success("Nhập kho thành công!");
      setShowCheckInModal(false);
      setCheckInForm({
        bloodGroup: "",
        rhType: "",
        componentType: "",
        bagType: "",
        quantity: 0,
        notes: "",
      });
      setDonationToStore(null);

      // Refresh data after successful check-in
      refreshData();
    } catch (error) {
      const errorMessage =
        error.response?.data?.message || error.message || "Lỗi không xác định";
      message.error(`Nhập kho thất bại: ${errorMessage}`);
    } finally {
      setLoadingCheckIn(false);
    }
  };

  // Override handleStoreBlood to show check-in modal
  const handleStoreBloodOverride = (donationId) => {
    const donation = allDonations.find(d => d.id === donationId);
    if (donation) {
      setDonationToStore(donation);
      // Pre-fill form with donation info
      setCheckInForm({
        bloodGroup: donation.bloodType?.charAt(0) || "",
        rhType: donation.bloodType?.includes('+') ? 'Rh+' : 'Rh-',
        componentType: "Máu toàn phần", // Default component type
        bagType: "450ml",
        quantity: 1,
        notes: `Nhập kho từ hiến máu - ID: ${donation.id}`,
      });
      setShowCheckInModal(true);
    }
  };

  // Handle modal actions
  const handleViewDetails = (donation) => {
    setSelectedDonation(donation);
    setDetailModalVisible(true);
  };

  const handleViewWorkflow = (donation) => {
    setSelectedDonation(donation);
    setProcessModalVisible(true);
  };

  const closeDetailModal = () => {
    setDetailModalVisible(false);
    setSelectedDonation(null);
  };

  const closeWorkflowModal = () => {
    setProcessModalVisible(false);
    setSelectedDonation(null);
  };

  // Get filtered data for tabs
  const scheduleData = {
    donations: getScheduleDonations(allDonations, filters, scheduleSort),
    filters,
    onFilterChange: setFilters,
    scheduleSort,
    onSortChange: setScheduleSort,
  };

  const processData = {
    donations: getProcessDonations(allDonations, filters, processSort),
    filters,
    onFilterChange: setFilters,
    processSort,
    onSortChange: setProcessSort,
  };

  return (
    <ManagerLayout pageTitle="Quản lý lịch hiến máu">
      <div className="main-content">
        <PageHeader
          title="Quản lý lịch hiến máu"
          icon={CalendarOutlined}
          extra={
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={refreshData}
              loading={loading}
            >
              Làm mới
            </Button>
          }
        />

        <DonationScheduleTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
          scheduleData={scheduleData}
          processData={processData}
          loading={loading}
          onViewDetails={handleViewDetails}
          onViewWorkflow={handleViewWorkflow}
          onSendReminder={handleSendReminder}
          onStoreBlood={handleStoreBloodOverride}
          isManager={isManager}
        />

        {/* Process Workflow Modal */}
        <ProcessWorkflowModal
          visible={processModalVisible}
          onCancel={closeWorkflowModal}
          selectedItem={selectedDonation}
          onStoreBlood={handleStoreBloodOverride}
          isManager={isManager}
          title="Quy trình hiến máu"
        />

        {/* Detail Modal */}
        <DonationDetailModal
          visible={detailModalVisible}
          onCancel={closeDetailModal}
          donation={selectedDonation}
          onSendReminder={handleSendReminder}
        />

        {/* Blood Check-In Modal */}
        <ManagerBloodCheckInModal
          open={showCheckInModal}
          onOk={handleCheckIn}
          onCancel={() => {
            setShowCheckInModal(false);
            setDonationToStore(null);
            setCheckInForm({
              bloodGroup: "",
              rhType: "",
              componentType: "",
              bagType: "",
              quantity: 0,
              notes: "",
            });
          }}
          confirmLoading={loadingCheckIn}
          form={checkInForm}
          setForm={setCheckInForm}
        />
      </div>
    </ManagerLayout>
  );
};

export default DonationSchedulePage;