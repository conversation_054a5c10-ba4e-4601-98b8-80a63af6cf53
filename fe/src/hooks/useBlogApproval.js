import { useState, useEffect, useRef } from "react";
import { message } from "antd";
import useRequest from "./useFetchData";
import {
  getBloodArticles,
  deleteArticle,
  updateArticle,
} from "../services/bloodArticleService";
import { fetchAllNews, updateNews, deleteNews } from "../services/newsService";
import { getActivityLogs } from "../services/activityLogService";
import { getAllTags, getNewsTags } from "../services/tagService";
import { getNewsId } from "../utils/newsUtils";
import { getArticleId } from "../utils/articleUtils";
import useSearchAndFilter from "./useSearchAndFilter";
import userInfoService from "../services/userInfoService";
import dayjs from "dayjs";

const CATEGORY_OPTIONS = [
  { value: "Tài liệu", label: "<PERSON><PERSON><PERSON> liệu" },
  { value: "Tin tức", label: "Tin tức" },
  { value: "Theo dõi hoạt động", label: "Theo dõi hoạt động" },
];

export const useBlogApproval = (currentUser) => {
  const {
    data: blogs = [],
    loading: blogsLoading,
    refetch: refetchBlogs,
  } = useRequest(getBloodArticles, []);

  const {
    data: news = [],
    loading: newsLoading,
    refetch: refetchNews,
  } = useRequest(fetchAllNews, []);

  const {
    data: activityLogs = [],
    loading: activityLogsLoading,
    refetch: refetchActivityLogs,
  } = useRequest(getActivityLogs, []);

  // State management
  const [activeTab, setActiveTab] = useState("Tài liệu");
  const [dateFilter, setDateFilter] = useState(null);
  const [dateRange, setDateRange] = useState(null);
  const [selectedBlog, setSelectedBlog] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editImage, setEditImage] = useState(null);
  const [userMap, setUserMap] = useState({});

  // Tags state
  const [tags, setTags] = useState([]);
  const [tagsLoading, setTagsLoading] = useState(false);

  // Polling ref for activity logs
  const pollingIntervalRef = useRef(null);

  // Polling effect for activity logs
  useEffect(() => {
    // Clear any existing interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }

    // Start polling only when on activity logs tab
    if (activeTab === "Theo dõi hoạt động") {
      // Set up polling every 3 seconds for more responsive updates
      pollingIntervalRef.current = setInterval(() => {
        refetchActivityLogs();
      }, 3000);
    }

    // Cleanup function
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [activeTab, refetchActivityLogs]);

  const customFilterFn = (item) => {
    // Handle single date filter (legacy)
    if (dateFilter) {
      let itemDate;

      if (activeTab === "Tài liệu") {
        itemDate = item.createdAt;
      } else if (activeTab === "Tin tức") {
        itemDate = item.postedAt;
      } else if (activeTab === "Theo dõi hoạt động") {
        itemDate = item.createdAt;
      }

      if (!itemDate) return false;

      const itemDay = dayjs(itemDate).startOf("day");
      const filterDay = dateFilter.startOf("day");

      if (!itemDay.isSame(filterDay)) {
        return false;
      }
    }

    // Handle date range filter
    if (dateRange && dateRange.length === 2) {
      let itemDate;

      if (activeTab === "Tài liệu") {
        itemDate = item.createdAt;
      } else if (activeTab === "Tin tức") {
        itemDate = item.postedAt;
      } else if (activeTab === "Theo dõi hoạt động") {
        itemDate = item.createdAt;
      }

      if (!itemDate) return false;

      const itemDay = dayjs(itemDate);
      const startDate = dayjs(dateRange[0]).startOf("day");
      const endDate = dayjs(dateRange[1]).endOf("day");

      if (!itemDay.isBetween(startDate, endDate, null, "[]")) {
        return false;
      }
    }

    return true;
  };

  const getCurrentData = () => {
    switch (activeTab) {
      case "Tài liệu":
        return Array.isArray(blogs) ? blogs : [];
      case "Tin tức":
        return Array.isArray(news) ? news : [];
      case "Theo dõi hoạt động":
        return Array.isArray(activityLogs) ? activityLogs : [];
      default:
        return Array.isArray(blogs) ? blogs : [];
    }
  };

  const getCurrentLoading = () => {
    switch (activeTab) {
      case "Tài liệu":
        return blogsLoading;
      case "Tin tức":
        return newsLoading;
      case "Theo dõi hoạt động":
        return activityLogsLoading;
      default:
        return blogsLoading;
    }
  };

  const currentData = getCurrentData();
  const currentLoading = getCurrentLoading();

  const {
    searchTerm,
    setSearchTerm,
    filter: activeTabFilter,
    setFilter: setActiveTabFilter,
    filteredData: filteredItems,
  } = useSearchAndFilter(currentData, {
    searchFields:
      activeTab === "Theo dõi hoạt động"
        ? ["description", "activityType", "entityType", "userName", "roleName"]
        : ["title", "content", "tags"],
    filterField: activeTab === "Theo dõi hoạt động" ? null : "category",
    filterFn: customFilterFn,
    searchFn: (item, term) => {
      if (!term) return true;
      const lowerTerm = term.toLowerCase();

      if (activeTab === "Theo dõi hoạt động") {
        const textMatch = [
          "description",
          "activityType",
          "entityType",
          "userName",
          "roleName",
        ].some((field) => {
          const value = item[field];
          if (!value) return false;
          return value.toLowerCase().includes(lowerTerm);
        });

        const dateMatch =
          item.createdAt &&
          new Date(item.createdAt)
            .toLocaleDateString("vi-VN")
            .includes(lowerTerm);

        return textMatch || dateMatch;
      } else {
        const textMatch = ["title", "content", "tags"].some((field) => {
          const value = item[field];
          if (!value) return false;

          if (Array.isArray(value)) {
            return value.some((v) => {
              const tagText =
                typeof v === "object" && v.tagName ? v.tagName : v;
              return tagText.toLowerCase().includes(lowerTerm);
            });
          }

          return value.toLowerCase().includes(lowerTerm);
        });

        const dateMatch =
          (activeTab === "Tài liệu" ? item.createdAt : item.postedAt) &&
          new Date(activeTab === "Tài liệu" ? item.createdAt : item.postedAt)
            .toLocaleDateString("vi-VN")
            .includes(lowerTerm);

        return textMatch || dateMatch;
      }
    },
    debounceMs: 300,
  });

  useEffect(() => {
    if (!activeTabFilter) {
      setActiveTabFilter("Tài liệu");
    }
  }, [activeTabFilter, setActiveTabFilter]);

  useEffect(() => {
    setActiveTabFilter(activeTab);
  }, [activeTab, setActiveTabFilter]);

  // Load tags based on active tab
  useEffect(() => {
    const loadTags = async () => {
      console.log("useBlogApproval - Loading tags for activeTab:", activeTab);
      setTagsLoading(true);
      try {
        let tagsData = [];
        if (activeTab === "Tài liệu") {
          tagsData = await getAllTags();
          console.log("useBlogApproval - Loaded article tags:", tagsData);
        } else if (activeTab === "Tin tức") {
          tagsData = await getNewsTags();
          console.log("useBlogApproval - Loaded news tags:", tagsData);
        }
        setTags(Array.isArray(tagsData) ? tagsData : []);
      } catch (error) {
        console.error("Error loading tags:", error);
        setTags([]);
      } finally {
        setTagsLoading(false);
      }
    };

    loadTags();
  }, [activeTab]);

  // Refetch data when tab changes
  useEffect(() => {
    if (activeTab === "Tài liệu") {
      refetchBlogs();
    } else if (activeTab === "Tin tức") {
      refetchNews();
    } else if (activeTab === "Theo dõi hoạt động") {
      refetchActivityLogs();
    }
  }, [activeTab, refetchBlogs, refetchNews, refetchActivityLogs]);

  useEffect(() => {
    userInfoService
      .getAllUsers()
      .then((users) => {
        const map = {};
        users.forEach((u) => {
          map[u.userId || u.userID || u.id] =
            u.name || u.fullName || u.username || u.email;
        });
        setUserMap(map);
      })
      .catch(() => {
        setUserMap({});
      });
  }, []);

  // Handlers
  const handleEditBlog = (blog) => {
    console.log("useBlogApproval - handleEditBlog called with:", blog);
    console.log("useBlogApproval - activeTab:", activeTab);
    console.log("useBlogApproval - current tags:", tags);

    setSelectedBlog(blog);
    setEditMode(true);
    setShowModal(true);
    setEditImage(blog.imgUrl);
  };

  const handleDeleteBlog = async (blogId) => {
    console.log("handleDeleteBlog called with:", { blogId, activeTab });

    try {
      if (activeTab === "Tài liệu") {
        console.log("Deleting article with ID:", blogId);
        await deleteArticle(blogId);
        refetchBlogs();
      } else if (activeTab === "Tin tức") {
        console.log("Deleting news with ID:", blogId);
        await deleteNews(blogId);
        refetchNews();
      }
      message.success("Đã xóa bài viết!");
      setShowModal(false);
    } catch (error) {
      console.error("Delete error:", error);
      message.error("Xóa bài viết thất bại!");
    }
  };

  const handleEditSubmit = (form) => {
    form.validateFields().then(async (values) => {
      if (!values.title || !values.content) {
        message.error("Tiêu đề và nội dung không được để trống!");
        return;
      }

      const userId =
        currentUser?.id || currentUser?.userId || currentUser?.userID;

      try {
        // Process tags to separate existing tag IDs and new tag names
        const tagsToProcess = values.tags || [];
        const tagIds = [];
        const newTags = [];

        tagsToProcess.forEach((tag) => {
          if (typeof tag === "number" || !isNaN(tag)) {
            // Nếu là number, đó là tagId
            tagIds.push(typeof tag === "number" ? tag : parseInt(tag));
          } else if (typeof tag === "string") {
            // Nếu là string, kiểm tra xem có phải existing tag không
            const existingTag = tags.find(
              (t) =>
                (typeof t === "object" && t.tagName === tag) ||
                (typeof t === "string" && t === tag)
            );

            if (
              existingTag &&
              typeof existingTag === "object" &&
              existingTag.tagId
            ) {
              // Đây là existing tag, thêm tagId
              tagIds.push(existingTag.tagId);
            } else {
              // Đây là new tag
              newTags.push(tag);
            }
          }
        });

        if (activeTab === "Tài liệu") {
          const updateData = {
            ...values,
            imgUrl: editImage,
            userId,
            tags: tagIds.concat(newTags), // Combine both tagIds and newTags into tags array
            tagIds: tagIds,
            newTags: newTags,
          };

          // Get article ID using utility function
          const articleId = getArticleId(selectedBlog);

          if (!articleId) {
            throw new Error("Không tìm thấy ID của bài viết");
          }

          // Use updateArticle directly instead of updateBlog to avoid create logic
          await updateArticle(articleId, updateData);
          refetchBlogs();
        } else if (activeTab === "Tin tức") {
          const updateData = {
            ...values,
            imgUrl: editImage,
            userId,
            tags: tagIds.concat(newTags), // Combine both tagIds and newTags into tags array
            tagIds: tagIds,
            newTags: newTags,
          };

          // Determine the news ID using utility function
          const newsId = getNewsId(selectedBlog);

          if (!newsId) {
            throw new Error("Không tìm thấy ID của bài tin tức");
          }

          await updateNews(newsId, updateData);
          refetchNews();
        }
        setShowModal(false);
        message.success("Cập nhật bài viết thành công!");
      } catch (error) {
        console.error("Error updating blog:", error);
        message.error("Cập nhật bài viết thất bại!");
        // Đảm bảo modal đóng ngay cả khi có lỗi
        setShowModal(false);
      }
    });
  };

  const handleViewBlog = (blog) => {
    setSelectedBlog(blog);
    setEditMode(false);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedBlog(null);
    setEditMode(false);
    setEditImage(null);
  };

  return {
    activeTab,
    dateFilter,
    dateRange,
    selectedBlog,
    showModal,
    editMode,
    editImage,
    userMap,
    searchTerm,
    filteredItems,
    currentLoading,
    CATEGORY_OPTIONS,

    // Tags
    tags,
    tagsLoading,

    setActiveTab,
    setDateFilter,
    setDateRange,
    setEditImage,

    handleEditBlog,
    handleDeleteBlog,
    handleEditSubmit,
    handleViewBlog,
    handleCloseModal,
    setSearchTerm,

    refetchActivityLogs,
  };
};
