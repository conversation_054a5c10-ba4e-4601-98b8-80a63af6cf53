import { apiClient } from "./axiosInstance";

class BloodDonationService {
  async getUserInfo(userId) {
    try {
      const response = await apiClient.get(`/Information/${userId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching user info:", error);
      throw error;
    }
  }

  async getAllBloodDonationSubmissions() {
    try {
      const response = await apiClient.get("/Appointment");
      return response.data;
    } catch (error) {
      console.error("Error fetching blood donation submissions:", error);
      throw error;
    }
  }

  async createBloodDonationSubmission(donationData) {
    try {
      const response = await apiClient.post("/Appointment", donationData);
      return response.data;
    } catch (error) {
      console.error("Error creating blood donation submission:", error);
      throw error;
    }
  }

  async getBloodDonationSubmissionById(appointmentId) {
    try {
      const response = await apiClient.get(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching blood donation submission by ID:", error);
      throw error;
    }
  }

  async deleteBloodDonationSubmission(appointmentId) {
    try {
      const response = await apiClient.delete(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      console.error("Error deleting blood donation submission:", error);
      throw error;
    }
  }

  async updateBloodDonationSubmissionStatus(appointmentId, status) {
    try {
      const response = await apiClient.put(
        `/Appointment/${appointmentId}/status`,
        {
          status: status,
        }
      );
      return response.data;
    } catch (error) {
      console.error("Error updating blood donation submission status:", error);
      throw error;
    }
  }

  calculateAge(dateOfBirth) {
    if (!dateOfBirth) return 0;
    const dob = new Date(dateOfBirth);
    if (isNaN(dob)) return 0;
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const m = today.getMonth() - dob.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
      age--;
    }
    return age;
  }

  async getAppointmentsByUser(userId) {
    try {
      // First try to get user info which might contain appointment history
      try {
        const userInfo = await this.getUserInfo(userId);

        // Check if user info contains blood donation history
        if (
          userInfo.bloodDonationHistory &&
          Array.isArray(userInfo.bloodDonationHistory)
        ) {
          return userInfo.bloodDonationHistory;
        }
      } catch (userError) {
        console.log(
          "User info endpoint not available, trying blood donation endpoints"
        );
      }

      // Try multiple blood donation endpoints with includeCancelled parameter
      const possibleEndpoints = [
        `/Appointment?includeCancelled=true`, // Get all including cancelled
        `/Appointment`, // Get all and filter
        `/Appointment?userId=${userId}&includeCancelled=true`,
        `/Appointment?userId=${userId}`,
        `/Appointment?userID=${userId}&includeCancelled=true`,
        `/Appointment?userID=${userId}`,
        `/Appointment/user/${userId}?includeCancelled=true`,
        `/Appointment/user/${userId}`,
        `/Appointment/by-user/${userId}?includeCancelled=true`,
        `/Appointment/by-user/${userId}`,
        `/appointments/user/${userId}`,
      ];

      let lastError = null;

      for (const endpoint of possibleEndpoints) {
        try {
          const response = await apiClient.get(endpoint);

          if (Array.isArray(response.data)) {
            const userAppointments = response.data.filter(
              (appointment) =>
                appointment.userId === parseInt(userId) ||
                appointment.userID === parseInt(userId) ||
                appointment.UserId === parseInt(userId) ||
                appointment.UserID === parseInt(userId)
            );

            // Count cancelled appointments for future use
            const userCancelledCount = userAppointments.filter(
              (apt) =>
                apt.Cancel === 1 || apt.cancel === 1 || apt.cancelled === true
            ).length;
            console.log(`User ${userId} has ${userCancelledCount} cancelled appointments`);
            // console.log(`👤 User appointments: ${userAppointments.length}, cancelled: ${userCancelledCount}`);

            return userAppointments;
          }

          return response.data;
        } catch (error) {
          lastError = error;
          continue;
        }
      }

      // If all endpoints fail, throw the last error
      throw lastError;
    } catch (error) {
      console.error("Error fetching blood donation appointments:", error);
      throw error;
    }
  }

  // Get all appointments for donation schedule management
  async getAllAppointments() {
    try {
      const response = await apiClient.get('/Appointment');
      return response.data;
    } catch (error) {
      console.error("Error fetching all appointments:", error);
      throw error;
    }
  }

  // Doctor update appointment with health data and notes
  async doctorUpdateAppointment(appointmentId, updateData) {
    try {
      const response = await apiClient.post(`/Appointment/doctor-update/${appointmentId}`, updateData);
      return response.data;
    } catch (error) {
      console.error("Error updating appointment by doctor:", error);
      throw error;
    }
  }

  // Update appointment note only using PATCH /api/Appointment/{id}/note
  async updateAppointmentNote(appointmentId, note) {
    try {
      console.log('Updating note for appointment:', appointmentId, 'with note:', note);

      // Try format 1: Send as object with 'note' property
      let response;
      try {
        response = await apiClient.patch(`/Appointment/${appointmentId}/note`, { note });
        console.log('Format 1 (object) worked!');
      } catch (error1) {
        console.log('Format 1 failed, trying format 2...');
        try {
          // Format 2: Send as JSON string
          response = await apiClient.patch(`/Appointment/${appointmentId}/note`, JSON.stringify(note), {
            headers: {
              'Content-Type': 'application/json'
            }
          });
          console.log('Format 2 (JSON string) worked!');
        } catch (error2) {
          console.log('Format 2 failed, trying format 3...');
          try {
            // Format 3: Send as plain string
            response = await apiClient.patch(`/Appointment/${appointmentId}/note`, note);
            console.log('Format 3 (plain string) worked!');
          } catch (error3) {
            console.log('All PATCH body formats failed, trying query parameter...');
            try {
              // Format 4: Send as query parameter
              response = await apiClient.patch(`/Appointment/${appointmentId}/note?note=${encodeURIComponent(note)}`);
              console.log('Format 4 (query parameter) worked!');
            } catch (error4) {
              console.log('Query parameter failed, trying PUT method...');
              try {
                // Format 5: Try PUT instead of PATCH
                response = await apiClient.put(`/Appointment/${appointmentId}/note`, { note });
                console.log('Format 5 (PUT method) worked!');
              } catch (error5) {
                console.log('All note API formats failed, using doctor-update as fallback...');
                // Fallback: Use doctor-update API with minimal payload
                response = await apiClient.post(`/Appointment/doctor-update/${appointmentId}`, {
                  notes: note,
                  bloodPressure: "",
                  heartRate: 0,
                  hemoglobin: 0,
                  temperature: 0,
                  weightAppointment: 0,
                  heightAppointment: 0,
                  doctorId: 1, // Use a valid doctor ID
                  process: 1,
                  status: true
                });
                console.log('Fallback doctor-update API worked!');
              }
            }
          }
        }
      }

      return response.data;
    } catch (error) {
      console.error("Error updating appointment note:", error);
      console.error("Error details:", error.response?.data);
      console.error("Request payload:", JSON.stringify(note));
      throw error;
    }
  }

  // Get specific appointment by ID
  async getAppointmentById(appointmentId) {
    try {
      const response = await apiClient.get(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching appointment by ID:", error);
      throw error;
    }
  }

  async getAppointmentDetails(appointmentId) {
    try {
      const response = await apiClient.get(`/Appointment/${appointmentId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching appointment details:", error);
      throw error;
    }
  }

  async getLastDonation(userId) {
    try {
      const response = await apiClient.get(
        `/Appointment/last-donation/${userId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching last donation:", error);

      // Handle specific error cases
      if (error.response?.status === 500) {
        // 500 error might indicate no previous donations or SQL null value issue
        console.log("No previous donation found (500 error - likely SQL null value)");
        return null; // Return null instead of throwing error
      } else if (error.response?.status === 404) {
        // 404 means no donation found
        console.log("No previous donation found (404 error)");
        return null;
      }

      // For other errors, still throw
      throw error;
    }
  }

  async updateSelfReportedDonation(userId, lastDonationDate) {
    try {
      // Based on the memories and error messages, try the most likely format
      // The backend expects UpdateLastDonationDto structure
      let requestBody;

      if (lastDonationDate !== null && lastDonationDate !== "") {
        // User has donated before - send the date in the correct format
        // Backend expects the data wrapped in a 'dto' field
        requestBody = {
          dto: {
            selfReportedLastDonationDate: lastDonationDate,
          },
        };
      } else {
        // User hasn't donated before - send null wrapped in dto
        requestBody = {
          dto: {
            selfReportedLastDonationDate: null,
          },
        };
      }

      console.log(
        `Updating self-reported donation for user ${userId}:`,
        requestBody
      );

      const response = await apiClient.put(
        `/Information/${userId}/self-reported-donation`,
        requestBody
      );
      console.log("✅ Self-reported donation updated successfully!");
      return response.data;
    } catch (error) {
      console.error(
        "❌ Self-reported donation failed:",
        error.response?.status
      );
      console.error("Error details:", error.response?.data);

      if (error.response?.data?.errors) {
        console.error("Validation errors:", error.response.data.errors);
        Object.keys(error.response.data.errors).forEach((field) => {
          console.error(`Field '${field}':`, error.response.data.errors[field]);
        });
      }

      // Don't throw the error - let the appointment creation continue
      console.log(
        "Continuing with appointment creation despite self-reported donation failure..."
      );
      return null;
    }
  }

  /**
   * Create new blood donation appointment
   * @param {Object} appointmentData - Appointment data
   * @returns {Promise} API response
   */
  async createAppointment(appointmentData) {
    try {
      // Add a flag to prevent multiple appointment creation
      if (this._creatingAppointment) {
        throw new Error("Appointment creation already in progress");
      }
      this._creatingAppointment = true;

      // Validate userId before sending
      if (!appointmentData.userId || appointmentData.userId === 0) {
        throw new Error("Invalid userId in appointment data");
      }



      // Try adding userId to headers as well in case backend reads from there
      const headers = {
        'X-User-ID': appointmentData.userId.toString(),
        'X-UserId': appointmentData.userId.toString(),
        'User-ID': appointmentData.userId.toString(),
        'UserId': appointmentData.userId.toString(),
        'user-id': appointmentData.userId.toString(),
        'userid': appointmentData.userId.toString(),
        'x-userid': appointmentData.userId.toString(),
        'X-Skip-Notification': 'true', // Try to skip notification logging
        'X-Disable-Notification': 'true',
        'Skip-Notification': 'true',
        'X-No-Notification': 'true',
        'No-Notification': 'true',
        'no-notification': 'true',
        'Disable-Logging': 'true',
        'disable-logging': 'true',
        'X-Bypass-Notification': 'true',
        'x-bypass-notification': 'true',
        'x-skip-notification': 'true',
        'x-disable-notification': 'true',
        'x-no-notification': 'true',
        'skip-notification': 'true'
      };



      // Use only the standard endpoint - other endpoints don't exist
      const endpoint = `/Appointment`;

      // Create a simple appointment creation without notification logging
      // Since the backend has a bug with NotificationLog.NotiLog getting UserId = 0
      // Let's try to create appointment with minimal data first
      const simplePayload = {
        userId: parseInt(appointmentData.userId),
        appointmentDate: appointmentData.appointmentDate,
        timeSlot: appointmentData.timeSlot,
        process: appointmentData.process || 0,
        status: appointmentData.status || 0
      };

      // Validate that userId is definitely not 0
      if (simplePayload.userId === 0 || !simplePayload.userId) {
        throw new Error("UserId is 0 - cannot create appointment");
      }

      // Try different payload formats
      const payloadVariations = [
        simplePayload, // Simple payload first
        // Try with explicit integer userId
        {
          ...appointmentData,
          userId: parseInt(appointmentData.userId), // Ensure userId is integer
          UserId: parseInt(appointmentData.userId),
          UserID: parseInt(appointmentData.userId),
          skipNotification: true, // Try to skip notification
          disableNotification: true,
          noNotification: true
        },
        appointmentData, // Original format
        { dto: appointmentData }, // Wrapped in dto
        { appointmentData }, // Wrapped in appointmentData
        { ...appointmentData, UserId: appointmentData.userId }, // Add UserId field
        { ...appointmentData, UserID: appointmentData.userId }, // Add UserID field (uppercase)
        { ...appointmentData, user_id: appointmentData.userId }, // Add user_id field (snake_case)
        // Try with status as different values
        { ...appointmentData, status: 1 }, // Try status = 1
        { ...appointmentData, status: "active" }, // Try status as string
        { ...appointmentData, status: undefined }, // Try without status
      ];

      let response;
      let lastError;

      // First, try a direct API call with minimal headers to avoid notification issues
      try {
        const minimalHeaders = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem("authToken")}`
        };

        response = await apiClient.post(endpoint, simplePayload, { headers: minimalHeaders });
        this._creatingAppointment = false; // Reset flag on success
        return response.data;
      } catch (error) {
        lastError = error;
      }

      // If direct call failed, try each payload variation with full headers
      for (let payloadIndex = 0; payloadIndex < payloadVariations.length; payloadIndex++) {
        try {
          const payload = payloadVariations[payloadIndex];
          response = await apiClient.post(endpoint, payload, { headers });
          this._creatingAppointment = false; // Reset flag on success
          return response.data;
        } catch (error) {
          lastError = error;
        }
      }

      this._creatingAppointment = false; // Reset flag on failure
      throw lastError;
    } catch (error) {
      this._creatingAppointment = false; // Reset flag on error
      throw error;
    }
  }

  async updateAppointmentStatus(appointmentId, status, notes = null, userId = null) {
    try {
      // Validate and convert parameters
      const validAppointmentId = parseInt(appointmentId);

      // Convert status to boolean: 1 = false (rejected), 2 = true (approved)
      const validStatus = status === 2 || status === "2" ? true : false;

      console.log("Updating appointment status:", {
        appointmentId: validAppointmentId,
        status: validStatus,
        notes,
        originalAppointmentId: appointmentId,
        originalStatus: status
      });

      // Validate parameters
      if (isNaN(validAppointmentId) || validAppointmentId <= 0) {
        throw new Error(`Invalid appointmentId: ${appointmentId}`);
      }

      // API expects boolean status parameter
      // Add headers to try to bypass notification logging issue
      const headers = {
        'X-Skip-Notification': 'true',
        'X-Disable-Notification': 'true',
        'Skip-Notification': 'true',
        'X-No-Notification': 'true',
        'No-Notification': 'true',
        'no-notification': 'true',
        'Disable-Logging': 'true',
        'disable-logging': 'true',
        'X-Bypass-Notification': 'true',
        'x-bypass-notification': 'true',
        'x-skip-notification': 'true',
        'x-disable-notification': 'true',
        'x-no-notification': 'true',
        'skip-notification': 'true'
      };

      // Add userId to headers if available to help backend identify correct user
      if (userId) {
        headers['X-User-ID'] = userId.toString();
        headers['User-ID'] = userId.toString();
        headers['user-id'] = userId.toString();
        headers['userId'] = userId.toString();
        headers['x-user-id'] = userId.toString();
      }

      // Try with query parameters to bypass notification
      const response = await apiClient.patch(
        `/Appointment/${validAppointmentId}/status/${validStatus}?skipNotification=true&noLog=true`,
        {},
        { headers }
      );
      return response.data;
    } catch (error) {
      console.error("Error updating appointment status:", error);
      console.error("Error response:", error.response?.data);
      console.error("Error response errors:", error.response?.data?.errors);

      // Log detailed validation errors
      if (error.response?.data?.errors) {
        Object.keys(error.response.data.errors).forEach(field => {
          console.error(`Validation error for ${field}:`, error.response.data.errors[field]);
        });
      }

      throw error;
    }
  }

  // Doctor update appointment with health information
  async doctorUpdateAppointment(appointmentId, updateData) {
    try {
      const validAppointmentId = parseInt(appointmentId);
      if (isNaN(validAppointmentId) || validAppointmentId <= 0) {
        throw new Error(`Invalid appointmentId: ${appointmentId}`);
      }

      // Prepare payload for doctor update
      const payload = {
        notes: updateData.notes || "",
        bloodPressure: updateData.bloodPressure || "",
        heartRate: updateData.heartRate || 0,
        hemoglobin: updateData.hemoglobin || 0,
        temperature: updateData.temperature || 0,
        doctorId: updateData.doctorId || 4, // Default doctor ID
        process: updateData.process || 2,
        status: updateData.status || false
      };

      // Add headers to bypass notification logging
      const headers = {
        'X-Skip-Notification': 'true',
        'X-Disable-Notification': 'true',
        'Skip-Notification': 'true',
        'X-No-Notification': 'true',
        'No-Notification': 'true',
        'no-notification': 'true',
        'Disable-Logging': 'true',
        'disable-logging': 'true',
        'X-Bypass-Notification': 'true',
        'x-bypass-notification': 'true',
        'x-skip-notification': 'true',
        'x-disable-notification': 'true',
        'x-no-notification': 'true',
        'skip-notification': 'true'
      };

      const response = await apiClient.post(
        `/Appointment/doctor-update/${validAppointmentId}?skipNotification=true&noLog=true`,
        payload,
        { headers }
      );
      return response.data;
    } catch (error) {
      console.error("Error in doctor update appointment:", error);
      throw error;
    }
  }

  // Update user weight and height information
  async updateUserInformation(userId, updateData) {
    try {
      const validUserId = parseInt(userId);
      if (isNaN(validUserId) || validUserId <= 0) {
        throw new Error(`Invalid userId: ${userId}`);
      }

      // First get current user information to see the expected format
      let currentInfo = {};
      try {
        currentInfo = await this.getUserInfo(validUserId);
      } catch (getError) {
        console.warn("Could not fetch current user info:", getError);
      }

      // Try different payload formats
      const payloadOptions = [
        // Option 1: Simple format
        {
          Weight: updateData.weight || currentInfo.Weight || 0,
          Height: updateData.height || currentInfo.Height || 0
        },
        // Option 2: Include more fields from current info
        {
          ...currentInfo,
          Weight: updateData.weight || currentInfo.Weight || 0,
          Height: updateData.height || currentInfo.Height || 0
        },
        // Option 3: Lowercase format
        {
          weight: updateData.weight || currentInfo.weight || 0,
          height: updateData.height || currentInfo.height || 0
        }
      ];

      let lastError = null;

      for (let i = 0; i < payloadOptions.length; i++) {
        try {
          const payload = payloadOptions[i];

          const response = await apiClient.put(
            `/Information/${validUserId}`,
            payload
          );
          return response.data;
        } catch (attemptError) {
          lastError = attemptError;
          continue;
        }
      }

      // If all options failed, log detailed error info and throw
      console.error("All payload options failed for PUT /api/Information");
      console.error("Last error details:", {
        status: lastError?.response?.status,
        statusText: lastError?.response?.statusText,
        data: lastError?.response?.data,
        errors: lastError?.response?.data?.errors
      });
      throw lastError;
    } catch (error) {
      console.error("Error updating user information:", error);
      console.error("Error response:", error.response?.data);
      console.error("Validation errors:", error.response?.data?.errors);
      throw error;
    }
  }

  async updateAppointmentProcess(appointmentId, process, notes = null, userId = null) {
    try {
      // Validate and convert parameters
      const validAppointmentId = parseInt(appointmentId);
      const validProcess = parseInt(process);

      console.log("Updating appointment process:", {
        appointmentId: validAppointmentId,
        process: validProcess,
        notes,
        originalAppointmentId: appointmentId,
        originalProcess: process
      });

      // Validate parameters
      if (isNaN(validAppointmentId) || validAppointmentId <= 0) {
        throw new Error(`Invalid appointmentId: ${appointmentId}`);
      }
      if (isNaN(validProcess) || validProcess < 1 || validProcess > 5) {
        throw new Error(`Invalid process: ${process} (must be 1-5)`);
      }

      // API expects integer process parameter
      // Add headers to bypass notification logging completely
      const headers = {
        'X-Skip-Notification': 'true',
        'X-Disable-Notification': 'true',
        'Skip-Notification': 'true',
        'X-No-Notification': 'true',
        'No-Notification': 'true',
        'no-notification': 'true',
        'Disable-Logging': 'true',
        'disable-logging': 'true',
        'X-Bypass-Notification': 'true',
        'x-bypass-notification': 'true',
        'x-skip-notification': 'true',
        'x-disable-notification': 'true',
        'x-no-notification': 'true',
        'skip-notification': 'true'
      };

      // Add userId to headers if available to help backend identify correct user
      if (userId) {
        headers['X-User-ID'] = userId.toString();
        headers['User-ID'] = userId.toString();
        headers['user-id'] = userId.toString();
        headers['userId'] = userId.toString();
        headers['x-user-id'] = userId.toString();
      }

      // Try with query parameters to bypass notification
      const response = await apiClient.patch(
        `/Appointment/${validAppointmentId}/process/${validProcess}?skipNotification=true&noLog=true`,
        {},
        { headers }
      );
      return response.data;
    } catch (error) {
      console.error("Error updating appointment process:", error);
      console.error("Error response:", error.response?.data);
      console.error("Error response errors:", error.response?.data?.errors);

      // Log detailed validation errors
      if (error.response?.data?.errors) {
        Object.keys(error.response.data.errors).forEach(field => {
          console.error(`Validation error for ${field}:`, error.response.data.errors[field]);
        });
      }

      throw error;
    }
  }

  /**
   * Update appointment with doctor notes and health check data
   * @param {number} appointmentId - Appointment ID
   * @param {Object} updateData - Data to update (notes, health check data)
   * @returns {Promise} API response
   */
  async updateAppointmentDoctorData(appointmentId, updateData) {
    try {
      console.log("Updating appointment with doctor data:", updateData);

      const response = await apiClient.post(
        `/Appointment/doctor-update/${appointmentId}`,
        updateData
      );
      return response.data;
    } catch (error) {
      console.error("Error updating appointment doctor data:", error);
      throw error;
    }
  }

  /**
   * Update user information (weight, height, etc.)
   * @param {number} userId - User ID
   * @param {Object} updateData - Data to update (weight, height, etc.)
   * @returns {Promise} API response
   */
  async updateUserInformation(userId, updateData) {
    try {
      console.log("Updating user information:", { userId, updateData });

      // Extract flags from updateData
      const { isDoctorUpdate, suppressNotification, ...dataToSend } =
        updateData;

      // Prepare headers
      const headers = {};
      if (isDoctorUpdate) {
        headers["X-Doctor-Update"] = "true";
      }
      if (suppressNotification) {
        headers["X-Suppress-Notification"] = "true";
      }

      const response = await apiClient.put(
        `/Information/${userId}`,
        dataToSend,
        {
          headers,
        }
      );

      return response.data;
    } catch (error) {
      console.error("Error updating user information:", error);
      throw error;
    }
  }

  /**
   * Update user information (weight, height)
   * @param {number} userId - User ID
   * @param {Object} data - Information data to update
   * @returns {Promise} API response
   */
  async updateUserInformation(userId, data) {
    try {
      console.log(`Updating user information for userId ${userId}:`, data);

      const response = await apiClient.put(`/Information/${userId}`, data);
      console.log('User information update response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating user information:', error);
      console.error('Error response:', error.response?.data);
      throw error;
    }
  }

  /**
   * Get user information
   * @param {number} userId - User ID
   * @returns {Promise} API response
   */
  async getUserInformation(userId) {
    try {
      console.log(`Getting user information for userId ${userId}`);

      const response = await apiClient.get(`/Information/${userId}`);
      console.log('User information response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error getting user information:', error);
      console.error('Error response:', error.response?.data);
      throw error;
    }
  }

  /**
   * Get all appointments (for admin/manager)
   * @returns {Promise} API response
   */
  async getAllAppointments() {
    try {
      // Try different endpoints to get all appointments including cancelled ones
      const possibleEndpoints = [
        "/Appointment?includeCancelled=true",
        "/Appointment?includeAll=true",
        "/Appointment?status=all",
        "/Appointment?limit=100",
        "/Appointment",
      ];

      for (const endpoint of possibleEndpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint}`);
          const response = await apiClient.get(endpoint);
          console.log(
            `Endpoint ${endpoint} returned ${response.data?.length || 0
            } appointments`
          );

          // Return the first successful response with the most data
          if (response.data && response.data.length >= 12) {
            return response.data;
          }
        } catch (endpointError) {
          console.log(`Endpoint ${endpoint} failed:`, endpointError.message);
          continue;
        }
      }

      // Fallback to basic endpoint
      const response = await apiClient.get("/Appointment");
      return response.data;
    } catch (error) {
      console.error("Error fetching all appointments:", error);
      throw error;
    }
  }

  /**
   * Cancel appointment (soft delete using PATCH method)
   * @param {number} appointmentId - Appointment ID
   * @returns {Promise} API response
   */
  async cancelAppointment(appointmentId) {
    try {
      console.log(
        "🚀 Cancelling appointment via PATCH /api/Appointment/{id}/cancel:",
        appointmentId
      );

      const response = await apiClient.patch(
        `/Appointment/${appointmentId}/cancel`
      );

      console.log("✅ Successfully cancelled appointment via PATCH method");
      return response.data;
    } catch (error) {
      console.error("❌ Error cancelling appointment:", error);
      throw error;
    }
  }

  /**
   * Delete appointment (alternative method name for backward compatibility)
   * @param {number} appointmentId - Appointment ID
   * @returns {Promise} API response
   */
  async deleteAppointment(appointmentId) {
    return this.cancelAppointment(appointmentId);
  }
}

// Create singleton instance
const bloodDonationService = new BloodDonationService();

export default bloodDonationService;
