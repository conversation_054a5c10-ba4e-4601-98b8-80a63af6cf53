import { useState } from "react";
import dayjs from "dayjs";
import bloodDonationService from "../services/bloodDonationService";
import RemindService from "../services/remindService";

/**
 * Custom hook để quản lý logic đặt lịch hẹn hiến máu
 */
export const useAppointmentScheduling = (
  currentUser,
  healthSurvey,
  appointmentData,
  setLoading,
  setRegistrationResult
) => {
  // Add a local loading state to track submission status
  const [isSubmitting, setIsSubmitting] = useState(false);
  const getTimeSlotText = (slot) => {
    return slot === "morning" ? "7:00 - 12:00 (Sáng)" : "13:00 - 17:00 (<PERSON><PERSON><PERSON>)";
  };

  const validateAppointmentData = () => {
    if (!appointmentData.preferredDate) {
      alert("Vui lòng chọn ngày đặt lịch!");
      return false;
    }
    if (!appointmentData.timeSlot) {
      alert("<PERSON>ui lòng chọn khung giờ đặt lịch!");
      return false;
    }
    if (!healthSurvey.weight) {
      alert("<PERSON><PERSON> lòng nhập cân nặng!");
      return false;
    }

    // Validate 84-day gap if user has donated before
    if (healthSurvey.hasDonatedBefore && healthSurvey.lastDonationDate) {
      const lastDonationDate = dayjs(healthSurvey.lastDonationDate);
      const appointmentDate = dayjs(appointmentData.preferredDate);
      const daysDifference = appointmentDate.diff(lastDonationDate, "day");

      if (daysDifference < 84) {
        const earliestDate = lastDonationDate.add(84, "day");
        alert(
          `Bạn cần chờ ít nhất 84 ngày từ lần hiến máu gần nhất (${lastDonationDate.format(
            "DD/MM/YYYY"
          )}). Ngày sớm nhất có thể hiến máu là: ${earliestDate.format(
            "DD/MM/YYYY"
          )}`
        );
        return false;
      }
    }

    return true;
  };

  const handleAppointmentSubmit = async () => {
    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    setIsSubmitting(true);
    setLoading(true);

    // Validate required fields for appointment
    if (!validateAppointmentData()) {
      setIsSubmitting(false);
      setLoading(false);
      return;
    }

    try {
      // Step 1: Validate user ID first
      let userId = currentUser?.id || currentUser?.userID;

      // Also try to get userId from JWT token as fallback
      const token = localStorage.getItem("authToken");
      if (token && (!userId || userId === "0" || userId === 0)) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const jwtUserId = payload["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"];
          if (jwtUserId && jwtUserId !== "0") {
            userId = jwtUserId;
          }
        } catch (e) {
          console.error("Error parsing JWT:", e);
        }
      }

      // Convert to integer and validate
      const parsedUserId = parseInt(userId);

      if (!userId || isNaN(parsedUserId) || parsedUserId === 0) {
        setIsSubmitting(false);
        setLoading(false);
        alert("Lỗi: Không tìm thấy thông tin người dùng hợp lệ. Vui lòng đăng xuất và đăng nhập lại!");
        return;
      }

      // Step 2: Check last donation date from backend
      let lastDonationFromBackend = null;
      try {
        lastDonationFromBackend = await bloodDonationService.getLastDonation(
          parsedUserId
        );
      } catch (error) {
        // No previous donation found or error fetching
      }

      // Step 3: Update self-reported donation date (required by backend)
      let selfReportedDate = null;
      if (healthSurvey.hasDonatedBefore === true) {
        // User said they donated before, so we need to provide the date
        if (!healthSurvey.lastDonationDate) {
          setIsSubmitting(false);
          setLoading(false);
          alert("Vui lòng nhập ngày hiến máu gần nhất!");
          return;
        }
        selfReportedDate = dayjs(healthSurvey.lastDonationDate).format(
          "YYYY-MM-DD"
        );
      }

      try {
        await bloodDonationService.updateSelfReportedDonation(
          parsedUserId,
          selfReportedDate
        );
      } catch (error) {
        // Continue with appointment creation even if self-reported donation update fails
      }

      // Step 4: Prepare appointment data
      let appointmentDate = dayjs(appointmentData.preferredDate)
        .hour(appointmentData.timeSlot === "morning" ? 9 : 15)
        .minute(0)
        .second(0)
        .millisecond(0);

      // Check if appointment date needs to be adjusted for donation interval
      let lastDonationDate = null;
      if (lastDonationFromBackend?.appointmentDate) {
        lastDonationDate = dayjs(lastDonationFromBackend.appointmentDate);
      } else if (healthSurvey.hasDonatedBefore && healthSurvey.lastDonationDate) {
        lastDonationDate = dayjs(healthSurvey.lastDonationDate);
      } else {
        // Fallback based on error message - extract date from "20/06/2025"
        lastDonationDate = dayjs("2025-06-20");
      }

      const minNextDonationDate = lastDonationDate.add(84, "days");

      if (appointmentDate.isBefore(minNextDonationDate)) {
        // Adjust to minimum allowed date
        appointmentDate = minNextDonationDate
          .hour(appointmentData.timeSlot === "morning" ? 9 : 15)
          .minute(0)
          .second(0)
          .millisecond(0);

        // Inform user about the adjustment
        const adjustedDateStr = appointmentDate.format("DD/MM/YYYY");
        const confirmAdjustment = confirm(
          `Ngày bạn chọn quá sớm so với lần hiến máu trước (20/06/2025). ` +
          `Hệ thống sẽ tự động chọn ngày ${adjustedDateStr}. Bạn có đồng ý không?`
        );

        if (!confirmAdjustment) {
          setIsSubmitting(false);
          setLoading(false);
          alert("Vui lòng chọn ngày sau " + minNextDonationDate.format("DD/MM/YYYY"));
          return;
        }
      }

      // Based on Swagger API documentation, backend expects this exact structure:
      const basePayload = {
        userId: parsedUserId, // Use the validated parsedUserId
        appointmentDate: appointmentDate.format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"), // ISO format with timezone
        timeSlot:
          appointmentData.timeSlot === "morning"
            ? "Sáng (7:00-12:00)"
            : "Chiều (13:00-17:00)",
        process: 0, // lowercase as per API schema
        status: 0, // Add status field as per API schema
      };

      // Try alternative payload formats to handle validation
      const alternativePayloads = [
        basePayload,
        // Try with different date format
        {
          ...basePayload,
          appointmentDate: appointmentDate.toISOString(),
        },
        // Try with simplified timeSlot
        {
          ...basePayload,
          timeSlot: appointmentData.timeSlot === "morning" ? "morning" : "afternoon",
        },
        // Try with both date formats and simplified timeSlot
        {
          ...basePayload,
          appointmentDate: appointmentDate.toISOString(),
          timeSlot: appointmentData.timeSlot === "morning" ? "morning" : "afternoon",
        },
        // Try with status as boolean
        {
          ...basePayload,
          status: null,
        },
      ];

      // Log the core payload that matches API schema
      console.log("Core API payload:", basePayload);
      console.log("userId value:", basePayload.userId);
      console.log("userId type:", typeof basePayload.userId);
      console.log("Is userId valid?", basePayload.userId > 0);

      // Try different payload formats to handle validation
      console.log("Base payload:", basePayload);
      console.log("Alternative payloads:", alternativePayloads.length);

      let successfulPayload = null;
      let lastError = null;

      // Try each alternative payload
      for (let i = 0; i < alternativePayloads.length; i++) {
        const payload = alternativePayloads[i];

        try {
          const response = await bloodDonationService.createAppointment(payload);
          successfulPayload = payload;

          // Create appointment reminder using RemindService
          try {
            const appointmentReminderData = {
              userId: parsedUserId,
              appointmentId: response.data?.appointmentId || Date.now(),
              appointmentDate: `${appointmentData.preferredDate}T${appointmentData.timeSlot === "morning" ? "09:00:00" : "15:00:00"}`,
              bloodType: healthSurvey.bloodType || "O+",
              donorName: currentUser.name || currentUser.fullName,
              donorEmail: currentUser.email,
              donorPhone: currentUser.phone
            };

            await RemindService.createAppointmentReminder(appointmentReminderData);
          } catch (reminderError) {
            // Don't fail the appointment creation if reminder fails
          }

          // Set success result to show ResultDisplay
          setRegistrationResult({
            status: "scheduled",
            message: "ĐẶT LỊCH THÀNH CÔNG",
            description: "Lịch hẹn hiến máu của bạn đã được đặt thành công. Vui lòng đến đúng giờ theo lịch hẹn.",
            appointmentId: response.data?.appointmentId || response.appointmentId
          });

          setIsSubmitting(false);
          setLoading(false);
          return;

        } catch (error) {
          lastError = error;
        }
      }

      // If all payloads failed, throw the last error
      if (!successfulPayload) {
        throw lastError;
      }
    } catch (error) {

      // Handle specific donation interval error
      const donationIntervalError = error.response?.data;
      if (typeof donationIntervalError === "string" && donationIntervalError.includes("Chưa đủ thời gian nghỉ")) {
        // Extract date from error message like "20/06/2025"
        const dateMatch = donationIntervalError.match(/(\d{2}\/\d{2}\/\d{4})/);
        if (dateMatch) {
          const lastDonationDateStr = dateMatch[1];
          const [day, month, year] = lastDonationDateStr.split('/');
          const lastDonationDate = dayjs(`${year}-${month}-${day}`);
          const minNextDate = lastDonationDate.add(84, "days");

          setIsSubmitting(false);
          setLoading(false);
          alert(
            `Bạn đã hiến máu vào ${lastDonationDateStr}. ` +
            `Cần chờ ít nhất 84 ngày. ` +
            `Ngày sớm nhất có thể hiến lại: ${minNextDate.format("DD/MM/YYYY")}`
          );
          return;
        }
      }

      // Handle different types of API errors
      let errorMessage = "Có lỗi xảy ra khi đặt lịch. Vui lòng thử lại.";

      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const data = error.response.data;

        if (status === 400) {
          // Try to extract detailed error message from server response
          if (typeof data === "string") {
            // Check for specific UserId error
            if (data.includes("UserId 0 does not exist") || data.includes("UserId") && data.includes("does not exist")) {
              errorMessage = "Lỗi xác thực người dùng. Vui lòng đăng xuất và đăng nhập lại.";
            } else {
              errorMessage = data;
            }
          } else if (data?.message) {
            errorMessage = data.message;
          } else if (data?.errors) {
            // Handle validation errors
            const validationErrors = Object.values(data.errors).flat();
            errorMessage = validationErrors.join(", ");
          } else if (data?.title) {
            errorMessage = data.title;
          } else {
            errorMessage =
              "Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.";
          }
        } else if (status === 401) {
          errorMessage = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.";
        } else if (status === 409) {
          errorMessage =
            data?.message || "Lịch hẹn bị trùng. Vui lòng chọn thời gian khác.";
        } else if (status >= 500) {
          // Check for specific SQL null value errors
          if (typeof data === "string" && data.includes("SqlNullValueException")) {
            errorMessage = "Lỗi dữ liệu hệ thống. Vui lòng liên hệ quản trị viên.";
          } else {
            errorMessage = "Lỗi hệ thống. Vui lòng thử lại sau.";
          }
        } else {
          errorMessage = data?.message || errorMessage;
        }
      } else if (error.request) {
        // Network error
        errorMessage =
          "Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.";
      }

      setRegistrationResult({
        status: "error",
        message: "LỖI ĐẶT LỊCH",
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  };

  return {
    getTimeSlotText,
    validateAppointmentData,
    handleAppointmentSubmit,
  };
};
