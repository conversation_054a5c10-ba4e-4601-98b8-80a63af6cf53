import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import MemberNavbar from "../../components/member/MemberNavbar";
import authService from "../../services/authService";
import userInfoService from "../../services/userInfoService";
import vietnamAddressService from "../../services/vietnamAddressService";
import "../../styles/pages/MemberInfoPage.scss";

const documentTypes = [
  { value: "cccd", label: "Căn cước công dân" },
  { value: "passport", label: "Hộ chiếu" },
];

const genders = [
  { value: "male", label: "Nam" },
  { value: "female", label: "Nữ" },
  { value: "other", label: "Khác" },
];

const bloodTypes = [
  { value: "A", label: "A" },
  { value: "B", label: "B" },
  { value: "AB", label: "AB" },
  { value: "O", label: "O" },
];

const rhTypes = [
  { value: "Rh+", label: "Rh+" },
  { value: "Rh-", label: "Rh-" },
];

const MemberInfoPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const currentUser = authService.getCurrentUser();
  const { isFirstTime, message } = location.state || {};



  // Lấy thông tin từ user profile hoặc localStorage
  const storedInfo = JSON.parse(localStorage.getItem("memberInfo") || "{}");
  const userProfile = currentUser?.profile || {};

  const [form, setForm] = useState({
    documentType: userProfile.documentType || storedInfo.documentType || "cccd",
    documentNumber:
      userProfile.documentNumber || storedInfo.documentNumber || "",
    fullName: userProfile.fullName || storedInfo.fullName || "",
    dob: userProfile.dateOfBirth || storedInfo.dob || "",
    gender: userProfile.gender || storedInfo.gender || "male",
    province: userProfile.province || storedInfo.province || "",
    ward: userProfile.ward || storedInfo.ward || "",
    provinceName: userProfile.provinceName || storedInfo.provinceName || "",
    wardName: userProfile.wardName || storedInfo.wardName || "",
    address: userProfile.address || storedInfo.address || "",
    email: userProfile.email || storedInfo.email || "",
    phone: userProfile.phone || storedInfo.phone || "",
    bloodType: userProfile.bloodGroup || storedInfo.bloodType || "",
    rhType: userProfile.rhType || storedInfo.rhType || "Rh+",
  });
  const [errors, setErrors] = useState({});
  // Updated for local JSON data
  const [provinceList, setProvinceList] = useState([]);
  const [wardList, setWardList] = useState([]);
  const [loadingProvinces, setLoadingProvinces] = useState(false);
  const [loadingWards, setLoadingWards] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [notification, setNotification] = useState({ message: "", type: "" });

  // Thêm hàm fetchUserInfo để lấy thông tin người dùng từ API
  const fetchUserInfo = async () => {
    try {
      const userData = await userInfoService.getUserInfo(currentUser.id);

      // Format date from "2003-02-16T00:00:00" to "2003-02-16"
      const formattedDate = userInfoService.formatDate(userData.dateOfBirth);

      // Cập nhật form với dữ liệu mới (updated for local JSON data)
      setForm({
        documentType: userData.idCardType || "cccd",
        documentNumber: userData.idCard || "",
        fullName: userData.name || "",
        dob: formattedDate,
        gender: userData.gender || "male",
        // Xử lý dữ liệu tỉnh/thành phố và phường/xã từ database
        province: userData.province || userData.city || "", // Ưu tiên province, fallback city
        provinceName: userData.city || userData.provinceName || "", // Tên tỉnh/thành phố
        ward: userData.wardId || userData.ward || "", // ID phường/xã
        wardName: userData.ward || userData.wardName || "", // Tên phường/xã
        // Tách địa chỉ đầy đủ để lấy số nhà, tên đường
        address: userInfoService.extractHouseNumberAndStreet(userData.address, userData.ward, userData.district, userData.city),
        email: userData.email || "",
        phone: userData.phone || "",
        bloodType: userData.bloodGroup || "",
        rhType: userData.rhType || "Rh+",
        password: userData.password || "",
      });

      // Cập nhật localStorage
      localStorage.setItem("memberInfo", JSON.stringify(userData));
    } catch (error) {
      console.error("Lỗi khi lấy thông tin người dùng:", error);
    }
  };

  // Load provinces from local JSON file
  useEffect(() => {
    const loadProvinces = async () => {
      setLoadingProvinces(true);
      try {
        const provinces = await vietnamAddressService.getProvinces();
        setProvinceList(provinces);
      } catch (error) {
        console.error("Failed to load province data:", error);
        setNotification({
          message: error.message || "Không thể tải dữ liệu tỉnh thành",
          type: "error"
        });
      } finally {
        setLoadingProvinces(false);
      }
    };

    loadProvinces();
  }, []);

  // Load wards when province changes (from local JSON file)
  useEffect(() => {
    const loadWards = async () => {
      if (!form.province) {
        setWardList([]);
        return;
      }

      setLoadingWards(true);
      try {
        const wards = await vietnamAddressService.getWardsByProvince(form.province);
        setWardList(wards);
      } catch (error) {
        console.error("Failed to load ward data:", error);
        setNotification({
          message: error.message || "Không thể tải dữ liệu phường/xã",
          type: "error"
        });
        setWardList([]);
      } finally {
        setLoadingWards(false);
      }
    };

    loadWards();
  }, [form.province]);

  // Validation + enable/disable button
  useEffect(() => {
    setIsValid(validate());
    // eslint-disable-next-line
  }, [form]);
  // Validation
  const validate = () => {
    const newErrors = {};
    // Ràng buộc số giấy tờ
    if (!form.documentNumber) {
      newErrors.documentNumber = "Vui lòng nhập số giấy tờ.";
    } else {
      if (form.documentType === "cccd") {
        if (!/^\d{12}$/.test(form.documentNumber)) {
          newErrors.documentNumber = "Căn cước công dân phải gồm đúng 12 số.";
        }
      } else if (form.documentType === "passport") {
        if (!/^[A-Z]\d{7}$/.test(form.documentNumber)) {
          newErrors.documentNumber =
            "Hộ chiếu phải gồm đúng một chữ cái in hoa (đại diện cho loại hộ chiếu) theo sau là 7 chữ số. .";
        }
      }
    }
    if (!form.fullName) newErrors.fullName = "Vui lòng nhập họ và tên.";

    // Validate ngày sinh - không được chọn ngày trong tương lai
    if (!form.dob) {
      newErrors.dob = "Vui lòng chọn ngày sinh.";
    } else {
      const today = new Date();
      const selectedDate = new Date(form.dob);
      if (selectedDate > today) {
        newErrors.dob = "Vui lòng chọn đúng ngày sinh";
      }
    }

    if (!form.province) newErrors.province = "Vui lòng chọn tỉnh/thành phố.";
    // if (!form.district) newErrors.district = "Vui lòng chọn quận/huyện."; // Removed - using only province and ward
    if (!form.ward) newErrors.ward = "Vui lòng chọn phường/xã.";
    if (!form.address) newErrors.address = "Vui lòng nhập số nhà, tên đường.";

    // Email/phone: chỉ bắt buộc 1 trong 2
    if (!form.email && !form.phone) {
      newErrors.email = "Cần nhập email hoặc số điện thoại.";
      newErrors.phone = "Cần nhập email hoặc số điện thoại.";
    } else {
      if (form.email && !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(form.email)) {
        newErrors.email = "Email không hợp lệ.";
      }
      if (form.phone && !/^0\d{9}$/.test(form.phone)) {
        newErrors.phone = "Số điện thoại không hợp lệ.";
      }
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Updated for local JSON data - only province and ward
    if (name === "province") {
      const selectedProvince = provinceList.find((p) => p === value);
      setForm((prev) => ({
        ...prev,
        [name]: value,
        provinceName: selectedProvince || "",
        // Reset ward when province changes
        ward: "",
        wardName: "",
      }));
    } else if (name === "ward") {
      const selectedWard = wardList.find((w) => w === value);
      setForm((prev) => ({
        ...prev,
        [name]: value,
        wardName: selectedWard || "",
      }));
    } else {
      setForm((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validate()) {
      try {
        // Prepare data using service
        const dataToSave = userInfoService.prepareUserDataForSubmission(form, currentUser);

        console.log("Data being sent:", dataToSave);

        // Lưu vào localStorage
        localStorage.setItem("memberInfo", JSON.stringify(dataToSave));

        // Gửi thông tin xuống database
        await userInfoService.updateUserInfo(currentUser.id, dataToSave);

        // Cập nhật profile của user hiện tại với thông tin từ database
        if (currentUser) {
          // Cập nhật profile với thông tin mới, đảm bảo có trường name
          const updatedProfile = {
            ...currentUser.profile,
            ...dataToSave,
            name: dataToSave.name, // Đảm bảo trường name được cập nhật
          };

          authService.updateProfile(updatedProfile);

          // Nếu là first-time setup, đánh dấu không còn là first login
          if (isFirstTime) {
            const updatedUser = { ...currentUser, isFirstLogin: false };
            // Note: setCurrentUser method might not exist, handle this appropriately
            if (typeof authService.setCurrentUser === 'function') {
              authService.setCurrentUser(updatedUser);
            }
          }
        }

        // Lấy thông tin mới nhất từ database sau khi lưu thành công
        await fetchUserInfo();
        setNotification({
          message: "Lưu thông tin thành công!",
          type: "success",
        });
        setTimeout(() => setNotification({ message: "", type: "" }), 3500);
        console.log("Thông tin đã lưu:", dataToSave);
        if (isFirstTime) {
          navigate("/member", {
            state: { message: "Chào mừng bạn đến với hệ thống hiến máu!" },
          });
        }
      } catch (error) {
        console.error("Lỗi khi lưu thông tin:", error);
        if (error.response) {
          console.error("Response status:", error.response.status);
          console.error("Response data:", error.response.data);
          console.error("Validation errors:", error.response.data?.errors);
          console.error("Response headers:", error.response.headers);
        }
        setNotification({
          message: `Có lỗi xảy ra khi lưu thông tin: ${error.response?.data?.message || error.message}`,
          type: "error",
        });
        setTimeout(() => setNotification({ message: "", type: "" }), 3500);
      }
    }
  };

  // Thêm useEffect để fetch thông tin người dùng khi component mount
  useEffect(() => {
    if (currentUser?.id) {
      fetchUserInfo();
    }
  }, [currentUser?.id]);



  return (
    <>
      <MemberNavbar />
      <div className="member-info-bg">
        <div className="member-info-header">
          <div className="avatar">
            <i className="bi bi-person-circle"></i>
          </div>
          <div>
            <h2>THÔNG TIN TÀI KHOẢN</h2>
            <p style={{ fontSize: "1.15rem" }}>
              Các thông tin điền theo thông tin căn cước công dân để đảm bảo
              chính xác, nếu thông tin không chính xác, khi cần đến làm thủ tục
              trực tiếp sẽ phải làm lại để xác thực một lần nữa
            </p>
          </div>
        </div>
        <div className="member-info-form-box">
          <form
            className="member-info-form"
            onSubmit={handleSubmit}
            id="member-info-form"
          >
            <div className="form-col">
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>Chọn loại giấy tờ</label>
                <select
                  className="form-select form-select-lg"
                  name="documentType"
                  value={form.documentType}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                >
                  {documentTypes.map((d) => (
                    <option key={d.value} value={d.value}>
                      {d.label}
                    </option>
                  ))}
                </select>
              </div>{" "}
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>
                  Số
                  {form.documentType === "passport"
                    ? " hộ chiếu"
                    : " căn cước công dân"}
                  <span className="text-danger">*</span>
                </label>
                <input
                  type="text"
                  className={`form-control form-control-lg${errors.documentNumber ? " is-invalid" : ""
                    }`}
                  name="documentNumber"
                  value={form.documentNumber}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                  placeholder={`Nhập ${form.documentType === "passport"
                    ? "số hộ chiếu"
                    : "số CCCD (12 số)"
                    }`}
                />
                {errors.documentNumber && (
                  <div className="invalid-feedback">
                    {errors.documentNumber}
                  </div>
                )}
              </div>{" "}
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>
                  Họ và tên <span className="text-danger">*</span>
                </label>
                <input
                  type="text"
                  className={`form-control form-control-lg${errors.fullName ? " is-invalid" : ""
                    }`}
                  name="fullName"
                  value={form.fullName}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                  placeholder="Nhập họ và tên đầy đủ"
                />
                {errors.fullName && (
                  <div className="invalid-feedback">{errors.fullName}</div>
                )}
              </div>
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>
                  Ngày sinh <span className="text-danger">*</span>
                </label>
                <input
                  type="date"
                  className={`form-control form-control-lg${errors.dob ? " is-invalid" : ""
                    }`}
                  name="dob"
                  value={form.dob}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                  max={new Date().toISOString().split("T")[0]}
                />
                {errors.dob && (
                  <div className="invalid-feedback">{errors.dob}</div>
                )}
              </div>
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>Giới tính</label>
                <select
                  className="form-select form-select-lg"
                  name="gender"
                  value={form.gender}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                >
                  {genders.map((g) => (
                    <option key={g.value} value={g.value}>
                      {g.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>Nhóm máu</label>
                <select
                  className="form-select form-select-lg"
                  name="bloodType"
                  value={form.bloodType}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                >
                  <option value="">Chọn nhóm máu</option>
                  {bloodTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

            </div>
            <div className="form-col">
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>
                  Tỉnh/Thành Phố <span className="text-danger">*</span>
                </label>
                <select
                  className="form-select form-select-lg"
                  name="province"
                  value={form.province}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                  disabled={loadingProvinces}
                >
                  <option value="">
                    {loadingProvinces ? "Đang tải..." : "Chọn tỉnh thành"}
                  </option>
                  {provinceList.map((province) => (
                    <option key={province} value={province}>
                      {province}
                    </option>
                  ))}
                </select>
                {errors.province && (
                  <div className="invalid-feedback">{errors.province}</div>
                )}
              </div>

              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>
                  Phường/Xã <span className="text-danger">*</span>
                </label>
                <select
                  className="form-select form-select-lg"
                  name="ward"
                  value={form.ward}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                  disabled={!form.province || loadingWards}
                >
                  <option value="">
                    {loadingWards ? "Đang tải..." : "Chọn phường/xã"}
                  </option>
                  {wardList.map((ward) => (
                    <option key={ward} value={ward}>
                      {ward}
                    </option>
                  ))}
                </select>
                {errors.ward && (
                  <div className="invalid-feedback">{errors.ward}</div>
                )}
              </div>{" "}
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>
                  Số nhà, tên đường <span className="text-danger">*</span>
                </label>
                <input
                  type="text"
                  className={`form-control form-control-lg${errors.address ? " is-invalid" : ""
                    }`}
                  name="address"
                  value={form.address}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                  placeholder="Nhập số nhà, tên đường"
                />
                {errors.address && (
                  <div className="invalid-feedback">{errors.address}</div>
                )}
              </div>
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>Email</label>
                <input
                  type="email"
                  className={`form-control form-control-lg${errors.email ? " is-invalid" : ""
                    }`}
                  name="email"
                  value={form.email}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                  placeholder="Nhập địa chỉ email"
                />
                {errors.email && (
                  <div className="invalid-feedback">{errors.email}</div>
                )}
              </div>
              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>Số điện thoại</label>
                <input
                  type="text"
                  className={`form-control form-control-lg${errors.phone ? " is-invalid" : ""
                    }`}
                  name="phone"
                  value={form.phone}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                  placeholder="Nhập số điện thoại (10 số, bắt đầu bằng 0)"
                />
                {errors.phone && (
                  <div className="invalid-feedback">{errors.phone}</div>
                )}{" "}
              </div>

              <div className="form-group input-box">
                <label style={{ fontSize: "1.1rem" }}>Rh</label>
                <select
                  className="form-select form-select-lg"
                  name="rhType"
                  value={form.rhType}
                  onChange={handleChange}
                  style={{ fontSize: "1.1rem" }}
                >
                  {rhTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </form>
          {notification.message && (
            <div
              style={{
                marginBottom: 16,
                padding: 12,
                borderRadius: 6,
                color: notification.type === "success" ? "#155724" : "#721c24",
                background:
                  notification.type === "success" ? "#d4edda" : "#f8d7da",
                border: `1px solid ${notification.type === "success" ? "#c3e6cb" : "#f5c6cb"
                  }`,
                fontWeight: 500,
                fontSize: "1.08rem",
                textAlign: "center",
                transition: "all 0.3s",
              }}
            >
              {notification.message}
            </div>
          )}
          <div className="member-info-actions">

            <button
              type="submit"
              className="btn btn-primary"
              form="member-info-form"
              disabled={!isValid}
            >
              Xác Nhận
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default MemberInfoPage;
