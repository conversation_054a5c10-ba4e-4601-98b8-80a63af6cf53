/**
 * Nominatim Geocoding Service
 * Free alternative to Google Maps API using OpenStreetMap data
 */

import axios from "axios";

class NominatimService {
  // Nominatim API endpoint
  static BASE_URL = "https://nominatim.openstreetmap.org";

  // Hospital coordinates (Bệnh viện <PERSON>)
  static HOSPITAL_COORDINATES = {
    lat: 10.7751237,
    lng: 106.6862143,
    name: "Bệnh viện Đa khoa Ánh Dương",
    address: "Đường Cách Mạng Tháng 8, Quận 3, TP.HCM, Vietnam",
  };

  /**
   * Geocode an address to get coordinates
   * @param {string} address - The address to geocode
   * @returns {Promise<Object>} - Object containing lat, lng, and formatted_address
   */
  static async geocodeAddress(address) {
    if (!address || address.trim().length < 5) {
      throw new Error("Địa chỉ qu<PERSON>");
    }
    const searchParams = new URLSearchParams({
      q: address,
      format: "json",
      countrycodes: "vn",
      limit: 1,
      addressdetails: 1,
      "accept-language": "vi",
    });
    const url = `${this.BASE_URL}/search?${searchParams.toString()}`;
    const response = await axios.get(url, {
      headers: {
         "Accept": "application/json",
        "Accept-Language": "vi,en;q=0.9",
      },
    });
    const data = response.data;
    if (!data || data.length === 0) {
      throw new Error(
        "Không tìm thấy địa chỉ này. Vui lòng kiểm tra lại địa chỉ."
      );
    }
    const result = data[0];
    return {
      lat: parseFloat(result.lat),
      lng: parseFloat(result.lon),
      address: result.display_name,
      place_id: result.place_id,
      importance: result.importance,
      boundingbox: result.boundingbox,
      addressDetails: result.address,
    };
  }

  /**
   * Reverse geocode coordinates to get address
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @returns {Promise<Object>} - Object containing address information
   */
  static async reverseGeocode(lat, lng) {
    const searchParams = new URLSearchParams({
      lat: lat.toString(),
      lon: lng.toString(),
      format: "json",
      addressdetails: 1,
      "accept-language": "vi",
    });
    const url = `${this.BASE_URL}/reverse?${searchParams.toString()}`;
    const response = await axios.get(url, {
      headers: {
         "Accept": "application/json",
        "Accept-Language": "vi,en;q=0.9",
      },
    });
    const data = response.data;
    if (data.error) {
      throw new Error(data.error);
    }
    return {
      lat: parseFloat(data.lat),
      lng: parseFloat(data.lon),
      address: data.display_name,
      addressDetails: data.address,
    };
  }

  /**
   * Search for places with autocomplete suggestions
   * @param {string} query - Search query
   * @returns {Promise<Array>} - Array of place suggestions
   */
  static async searchPlaces(query) {
    if (!query || query.trim().length < 3) {
      return [];
    }
    const searchParams = new URLSearchParams({
      q: query,
      format: "json",
      countrycodes: "vn",
      limit: 5,
      addressdetails: 1,
      "accept-language": "vi",
    });
    const url = `${this.BASE_URL}/search?${searchParams.toString()}`;
    const response = await axios.get(url, {
      headers: {
         "Accept": "application/json",
        "Accept-Language": "vi,en;q=0.9",
      },
    });
    const data = response.data;
    return data.map((item) => ({
      place_id: item.place_id,
      display_name: item.display_name,
      lat: parseFloat(item.lat),
      lng: parseFloat(item.lon),
      importance: item.importance,
      type: item.type,
      addressDetails: item.address,
    }));
  }

  /**
   * Get detailed information about a specific place
   * @param {string} placeId - Nominatim place ID
   * @returns {Promise<Object>} - Detailed place information
   */
  static async getPlaceDetails(placeId) {
    const searchParams = new URLSearchParams({
      place_id: placeId,
      format: "json",
      addressdetails: 1,
      "accept-language": "vi",
    });
    const url = `${this.BASE_URL}/details?${searchParams.toString()}`;
    const response = await axios.get(url, {
      headers: {
         "Accept": "application/json",
        "Accept-Language": "vi,en;q=0.9",
      },
    });
    return response.data;
  }

  /**
   * Check if Nominatim service is available
   * @returns {Promise<boolean>} - True if service is available
   */
  static async isServiceAvailable() {
    const url = `${this.BASE_URL}/status`;
    const response = await axios.get(url, {
      headers: {
         "Accept": "application/json",
        "Accept-Language": "vi,en;q=0.9",
      },
    });
    return response.data;
  }

  /**
   * Get hospital coordinates
   * @returns {Object} - Hospital coordinates and info
   */
  static getHospitalCoordinates() {
    return this.HOSPITAL_COORDINATES;
  }

  /**
   * Format address from Nominatim result for display
   * @param {Object} addressDetails - Address details from Nominatim
   * @returns {string} - Formatted address string
   */
  static formatAddress(addressDetails) {
    if (!addressDetails) return "";

    const parts = [];

    // House number and street
    if (addressDetails.house_number) parts.push(addressDetails.house_number);
    if (addressDetails.road) parts.push(addressDetails.road);

    // Ward/Suburb
    if (addressDetails.suburb) parts.push(addressDetails.suburb);
    else if (addressDetails.quarter) parts.push(addressDetails.quarter);

    // District
    if (addressDetails.city_district) parts.push(addressDetails.city_district);
    else if (addressDetails.county) parts.push(addressDetails.county);

    // City
    if (addressDetails.city) parts.push(addressDetails.city);
    else if (addressDetails.town) parts.push(addressDetails.town);

    // Country
    if (addressDetails.country) parts.push(addressDetails.country);

    return parts.join(", ");
  }
}

export default NominatimService;
