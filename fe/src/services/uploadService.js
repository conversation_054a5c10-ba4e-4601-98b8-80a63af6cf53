import { apiClient } from "./axiosInstance";
import config from "../config/environment";

const API_BASE = config.api.baseUrl;

// Convert file to base64
export const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

export const uploadImage = async (file) => {
  try {
    // Convert to base64 for database storage
    const base64String = await convertToBase64(file);

    return {
      success: true,
      url: base64String, // Return base64 string directly
      message: "Upload thành công",
    };
  } catch (error) {
    console.error("Upload error:", error);
    return {
      success: false,
      message: "Không thể convert ảnh. Vui lòng thử lại.",
    };
  }
};

// Alternative: Upload to server if needed
export const uploadImageToServer = async (file) => {
  try {
    const formData = new FormData();
    formData.append("image", file);

    const response = await apiClient.upload(`/api/upload`, formData);

    return {
      success: true,
      url: response.data.url || response.data.imgUrl || response.data,
      message: "Upload thành công",
    };
  } catch (error) {
    console.error("Upload error:", error);

    // Fallback to base64 if server upload fails
    try {
      const base64String = await convertToBase64(file);
      return {
        success: true,
        url: base64String,
        message: "Upload thành công (base64)",
        isLocal: true,
      };
    } catch (localError) {
      return {
        success: false,
        message:
          error.response?.data?.message ||
          "Không thể upload ảnh. Vui lòng thử lại.",
      };
    }
  }
};

export const uploadMultipleImages = async (files) => {
  try {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file);
    });

    const response = await apiClient.upload(
      `${API_BASE}/upload/images`,
      formData
    );

    return response.data;
  } catch (error) {
    console.error("Upload error:", error);
    throw new Error("Không thể upload ảnh. Vui lòng thử lại.");
  }
};
