@use "../base/variables" as vars;

.doctor-dashboard {
  display: flex;
  min-height: 100vh;
  background: #f6f6f6;

  .doctor-dashboard-content {
    flex: 1;
    margin-left: 0px;
    padding: 0 4px;
    transition: margin-left 0.3s ease;
    font-family: "Inter", sans-serif;

    .page-header {
      margin-bottom: 24px;
      background: #fff;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8e8e8;
      display: flex;
      align-items: flex-start;
      gap: 16px;
      h1 {
        color: #20374e;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
        background: none;
        -webkit-text-fill-color: unset;
      }
      p {
        color: #666;
        font-size: 1.1rem;
        margin: 0 0 8px 0;
        font-weight: 500;
      }
      .doctor-type-badge {
        background: #deccaa;
        color: #20374e;
        padding: 6px 16px;
        border-radius: 16px;
        font-size: 0.95rem;
        font-weight: 600;
        border: none;
        box-shadow: none;
      }
    }

    .quick-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
      .stat-card {
        background: #fff;
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        .stat-icon {
          font-size: 2.2rem;
          width: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background: #f8f9fa;
        }
        .stat-info {
          flex: 1;
          h3 {
            margin: 0 0 4px 0;
            color: #666;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
          }
          .stat-number {
            font-size: 2rem;
            font-weight: 800;
            margin: 0;
            color: #d93e4c;
            background: none;
            -webkit-text-fill-color: unset;
          }
          .stat-number.warning {
            color: #faad14;
          }
          .stat-number.success {
            color: #52c41a;
          }
          .stat-number.info {
            color: #1890ff;
          }
        }
      }
    }

    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
      .dashboard-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #e8e8e8;
        transition: all 0.3s ease;
        overflow: hidden;
        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }
        .card-header {
          padding: 16px 24px;
          background: #f8f9fa;
          border-bottom: 1px solid #e8e8e8;
          display: flex;
          justify-content: space-between;
          align-items: center;
          h2 {
            margin: 0;
            color: #20374e;
            font-size: 1.2rem;
            font-weight: 700;
            background: none;
            -webkit-text-fill-color: unset;
          }
          .view-all-link {
            color: #d93e4c;
            text-decoration: none;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            &:hover {
              text-decoration: underline;
              color: #20374e;
            }
          }
        }
        .card-body {
          padding: 16px 24px;
        }
      }
      .activity-list,
      .notifications-list {
        .activity-item,
        .notification-item {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          &:last-child {
            border-bottom: none;
          }
          .activity-icon,
          .notification-icon {
            font-size: 1.3rem;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            background: #f8f9fa;
            color: #d93e4c;
          }
          .activity-content,
          .notification-content {
            flex: 1;
            .activity-message,
            .notification-title {
              font-weight: 700;
              color: #20374e;
              margin-bottom: 2px;
              font-size: 1rem;
            }
            .notification-message {
              color: #666;
              font-size: 0.95rem;
              margin-bottom: 2px;
            }
            .activity-time,
            .notification-time {
              color: #999;
              font-size: 0.85rem;
            }
          }
          &.unread {
            background: #fff4f4;
            border-radius: 8px;
            border-left: 3px solid #d93e4c;
          }
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .doctor-dashboard {
    .doctor-dashboard-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        padding: 1.5rem;

        div h1 {
          font-size: 2rem;
        }
      }

      .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1.5rem;
          gap: 1rem;

          .stat-icon {
            font-size: 2rem;
            width: 60px;
            height: 60px;
          }

          .stat-info .stat-number {
            font-size: 2rem;
          }
        }
      }

      .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .dashboard-card .card-body {
          .inventory-grid {
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
          }

          .requests-list .request-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;

            .request-status {
              text-align: left;
            }
          }
        }
      }
    }
  }
}
