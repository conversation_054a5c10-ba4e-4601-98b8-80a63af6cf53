import { useState } from "react";

/**
 * Custom hook để quản lý logic khảo sát sức khỏe
 */
export const useHealthSurvey = (
  healthSurvey,
  setHealthSurvey,
  personalInfo
) => {
  const [weightError, setWeightError] = useState("");
  const [heightError, setHeightError] = useState("");

  // Hàm validate cân nặng
  const validateWeight = (weight) => {
    if (!weight || weight === "" || weight <= 0) {
      return "Vui lòng nhập cân nặng";
    }

    const minWeight = personalInfo.gender === "female" ? 42 : 45;
    const genderText = personalInfo.gender === "female" ? "nữ" : "nam";

    if (parseFloat(weight) < minWeight) {
      return `Cân nặng phải ≥ ${minWeight}kg cho ${genderText}`;
    }

    return "";
  };

  // Hàm validate chiều cao
  const validateHeight = (height) => {
    if (!height || height === "" || height <= 0) {
      return "Vui lòng nhập chiều cao";
    }

    if (parseFloat(height) < 100 || parseFloat(height) > 250) {
      return "Chiều cao phải từ 100-250cm";
    }

    return "";
  };

  const handleHealthSurveyChange = (field, value) => {
    setHealthSurvey((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Validate cân nặng real-time
    if (field === "weight") {
      const error = validateWeight(value);
      setWeightError(error);
    }

    // Validate chiều cao real-time
    if (field === "height") {
      const error = validateHeight(value);
      setHeightError(error);
    }
  };

  // Helper function để xử lý logic checkbox cho các câu hỏi
  const handleCheckboxChange = (field, value, noneField, otherFields = []) => {
    if (field === noneField) {
      // Nếu chọn "Không", bỏ chọn tất cả các checkbox khác
      if (value) {
        const updates = { [field]: value };
        otherFields.forEach((otherField) => {
          updates[otherField] = false;
        });
        setHealthSurvey((prev) => ({ ...prev, ...updates }));
      } else {
        handleHealthSurveyChange(field, value);
      }
    } else {
      // Nếu chọn checkbox khác, bỏ chọn "Không"
      if (value) {
        setHealthSurvey((prev) => ({
          ...prev,
          [field]: value,
          [noneField]: false,
        }));
      } else {
        handleHealthSurveyChange(field, value);
      }
    }
  };

  // Hàm cuộn đến phần tử
  const scrollToElement = (elementId) => {
    // Thêm delay nhỏ để đảm bảo alert được hiển thị trước
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
        // Thêm hiệu ứng highlight
        element.style.border = "2px solid #ff4d4f";
        element.style.borderRadius = "8px";
        element.style.padding = "8px";
        element.style.backgroundColor = "#fff2f0";

        // Nếu là phần cân nặng, focus vào input
        if (elementId === "weight-section") {
          const weightInput = element.querySelector(".ant-input-number-input");
          if (weightInput) {
            setTimeout(() => weightInput.focus(), 300);
          }
        }

        setTimeout(() => {
          element.style.border = "";
          element.style.borderRadius = "";
          element.style.padding = "";
          element.style.backgroundColor = "";
        }, 3000);
      }
    }, 50);
  };

  // Validate form trước khi submit
  const validateHealthSurveyForm = () => {
    // Kiểm tra cân nặng
    if (
      !healthSurvey.weight ||
      healthSurvey.weight === "" ||
      healthSurvey.weight <= 0
    ) {
      // Thử nhiều cách để cuộn đến phần cân nặng
      const weightSection = document.getElementById("weight-section");
      const weightInput = document.querySelector(".ant-input-number-input");

      if (weightSection) {
        weightSection.scrollIntoView({ behavior: "smooth", block: "center" });
        weightSection.style.border = "2px solid #ff4d4f";
        weightSection.style.borderRadius = "8px";
        weightSection.style.backgroundColor = "#fff2f0";
        setTimeout(() => {
          weightSection.style.border = "";
          weightSection.style.borderRadius = "";
          weightSection.style.backgroundColor = "";
        }, 3000);
      }

      if (weightInput) {
        setTimeout(() => weightInput.focus(), 300);
      }

      setTimeout(() => {
        alert(
          "⚠️ Vui lòng nhập cân nặng của bạn để tiếp tục khảo sát sức khỏe!"
        );
      }, 200);
      return false;
    }

    // Kiểm tra cân nặng có đạt yêu cầu tối thiểu không
    const minWeight = personalInfo.gender === "female" ? 42 : 45;
    const genderText = personalInfo.gender === "female" ? "nữ" : "nam";
    if (parseFloat(healthSurvey.weight) < minWeight) {
      scrollToElement("weight-section");
      setTimeout(() => {
        alert(
          `⚠️ Cân nặng không đạt yêu cầu tối thiểu!\nYêu cầu: ≥ ${minWeight}kg cho ${genderText}\nCân nặng hiện tại: ${healthSurvey.weight}kg`
        );
      }, 200);
      return false;
    }

    // Kiểm tra chiều cao (bắt buộc nếu chưa có thông tin từ database)
    if (
      !healthSurvey.height ||
      healthSurvey.height === "" ||
      healthSurvey.height <= 0
    ) {
      setTimeout(() => {
        alert(
          "⚠️ Vui lòng nhập chiều cao của bạn để tiếp tục khảo sát sức khỏe!"
        );
      }, 200);

      const heightInput = document.querySelector(
        'input[placeholder="Nhập chiều cao"]'
      );
      if (heightInput) {
        heightInput.scrollIntoView({ behavior: "smooth", block: "center" });
        setTimeout(() => heightInput.focus(), 300);
      }
      return false;
    }

    // Kiểm tra chiều cao có hợp lệ không
    if (
      parseFloat(healthSurvey.height) < 100 ||
      parseFloat(healthSurvey.height) > 250
    ) {
      setTimeout(() => {
        alert(
          "⚠️ Chiều cao không hợp lệ! Vui lòng nhập chiều cao từ 100-250cm."
        );
      }, 200);

      const heightInput = document.querySelector(
        'input[placeholder="Nhập chiều cao"]'
      );
      if (heightInput) {
        heightInput.scrollIntoView({ behavior: "smooth", block: "center" });
        setTimeout(() => heightInput.focus(), 300);
      }
      return false;
    }

    // Kiểm tra các câu hỏi radio/checkbox
    // Câu 1
    if (
      healthSurvey.hasDonatedBefore === null ||
      healthSurvey.hasDonatedBefore === undefined
    ) {
      alert("Vui lòng trả lời câu hỏi 1: Anh/chị từng hiến máu chưa?");
      scrollToElement("question-1");
      return false;
    }
    // Nếu chọn Có thì phải chọn ngày
    if (
      healthSurvey.hasDonatedBefore === true &&
      !healthSurvey.lastDonationDate
    ) {
      alert("Vui lòng chọn ngày hiến máu gần nhất!");
      scrollToElement("question-1");
      return false;
    }

    // Câu 2
    if (
      healthSurvey.hasCurrentMedicalConditions === null ||
      healthSurvey.hasCurrentMedicalConditions === undefined
    ) {
      alert(
        "Vui lòng trả lời câu hỏi 2: Hiện tại, anh/chị có mắc bệnh lý nào không?"
      );
      scrollToElement("question-2");
      return false;
    }
    // Nếu chọn Có thì phải nhập chi tiết
    if (
      healthSurvey.hasCurrentMedicalConditions === true &&
      !healthSurvey.currentMedicalConditionsDetail
    ) {
      alert("Vui lòng ghi rõ bệnh lý hiện tại!");
      scrollToElement("question-2");
      return false;
    }

    // Câu 3
    if (
      healthSurvey.hasPreviousSeriousConditions === null ||
      healthSurvey.hasPreviousSeriousConditions === undefined
    ) {
      alert("Vui lòng trả lời câu hỏi 3: Tiền sử bệnh nghiêm trọng?");
      scrollToElement("question-3");
      return false;
    }
    if (
      healthSurvey.hasPreviousSeriousConditions === "other" &&
      !healthSurvey.otherPreviousConditions
    ) {
      alert("Vui lòng mô tả bệnh nghiêm trọng khác!");
      scrollToElement("question-3");
      return false;
    }

    // Câu 4-8: Ít nhất phải chọn 1 checkbox mỗi nhóm
    // Câu 4
    if (
      !(
        healthSurvey.hadMalariaSyphilisTuberculosis ||
        healthSurvey.hadBloodTransfusion ||
        healthSurvey.hadVaccination ||
        healthSurvey.last12MonthsNone
      )
    ) {
      alert("Vui lòng chọn ít nhất 1 đáp án cho câu hỏi 4!");
      scrollToElement("question-4");
      return false;
    }

    // Câu 5
    if (
      !(
        healthSurvey.hadTyphoidSepsis ||
        healthSurvey.unexplainedWeightLoss ||
        healthSurvey.persistentLymphNodes ||
        healthSurvey.invasiveMedicalProcedures ||
        healthSurvey.tattoosPiercings ||
        healthSurvey.drugUse ||
        healthSurvey.bloodExposure ||
        healthSurvey.livedWithHepatitisB ||
        healthSurvey.sexualContactWithInfected ||
        healthSurvey.sameSexContact ||
        healthSurvey.last6MonthsNone
      )
    ) {
      alert("Vui lòng chọn ít nhất 1 đáp án cho câu hỏi 5!");
      scrollToElement("question-5");
      return false;
    }

    // Câu 6
    if (
      !(
        healthSurvey.hadUrinaryInfection ||
        healthSurvey.visitedEpidemicArea ||
        healthSurvey.last1MonthNone
      )
    ) {
      alert("Vui lòng chọn ít nhất 1 đáp án cho câu hỏi 6!");
      scrollToElement("question-6");
      return false;
    }

    // Câu 7
    if (
      !(
        healthSurvey.hadFluSymptoms ||
        healthSurvey.last14DaysNone ||
        (typeof healthSurvey.otherSymptoms === "string" &&
          healthSurvey.otherSymptoms)
      )
    ) {
      alert("Vui lòng chọn ít nhất 1 đáp án cho câu hỏi 7!");
      scrollToElement("question-7");
      return false;
    }
    // Nếu chọn Khác (cụ thể) thì phải nhập text
    if (
      !!healthSurvey.otherSymptoms &&
      typeof healthSurvey.otherSymptoms === "string" &&
      healthSurvey.otherSymptoms.trim() === ""
    ) {
      alert("Vui lòng mô tả triệu chứng ở câu hỏi 7!");
      scrollToElement("question-7");
      return false;
    }

    // Câu 8
    if (
      !(
        healthSurvey.tookAntibiotics ||
        healthSurvey.last7DaysNone ||
        (typeof healthSurvey.otherMedications === "string" &&
          healthSurvey.otherMedications)
      )
    ) {
      alert("Vui lòng chọn ít nhất 1 đáp án cho câu hỏi 8!");
      scrollToElement("question-8");
      return false;
    }
    if (
      !!healthSurvey.otherMedications &&
      typeof healthSurvey.otherMedications === "string" &&
      healthSurvey.otherMedications.trim() === ""
    ) {
      alert("Vui lòng mô tả thuốc ở câu hỏi 8!");
      scrollToElement("question-8");
      return false;
    }

    // Câu 9 (nữ)
    if (personalInfo.gender === "female") {
      if (
        !(
          healthSurvey.isPregnantOrNursing ||
          healthSurvey.hadPregnancyTermination ||
          healthSurvey.womenQuestionsNone
        )
      ) {
        alert("Vui lòng chọn ít nhất 1 đáp án cho câu hỏi 9!");
        scrollToElement("question-9");
        return false;
      }
    }

    return true;
  };

  return {
    weightError,
    heightError,
    validateWeight,
    validateHeight,
    handleHealthSurveyChange,
    handleCheckboxChange,
    scrollToElement,
    validateHealthSurveyForm,
  };
};
