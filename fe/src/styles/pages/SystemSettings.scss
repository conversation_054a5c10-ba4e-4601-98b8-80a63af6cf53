@use "../base/variables" as vars;
@use "../base/mixin" as mixins;

.system-settings {
  padding: 0px;
  max-width: 1400px;
  margin: 0 auto;

  .page-header {
    @include mixins.flex-between;
    @include mixins.card-base;
    @include mixins.card-padding(vars.$spacing-8);
    margin-bottom: vars.$spacing-8;
    @include mixins.shadow-elevation(2);

    .header-content {
      h1 {
        @include mixins.heading(
          vars.$font-size-3xl,
          vars.$font-weight-bold,
          vars.$text-primary
        );
        @include mixins.text-gradient(vars.$accent-color, vars.$primary-color);
        margin-bottom: vars.$spacing-2;
      }

      p {
        @include mixins.text(vars.$font-size-lg, vars.$text-secondary);
        margin: 0;
      }
    }

    .btn-primary {
      @include mixins.button-primary;
      @include mixins.flex-align(center, center);
      gap: vars.$spacing-2;
      white-space: nowrap;

      i {
        font-size: vars.$font-size-sm;
      }
    }
  }

  .settings-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: vars.$spacing-8;

    .settings-nav {
      @include mixins.card-base;
      @include mixins.shadow-elevation(2);
      padding: vars.$spacing-4;
      height: fit-content;

      .nav-btn {
        @include mixins.flex-align(flex-start, center);
        gap: vars.$spacing-3;
        width: 100%;
        padding: vars.$spacing-3 vars.$spacing-4;
        border-radius: vars.$border-radius-base;
        background: transparent;
        border: none;
        color: vars.$text-secondary;
        font-weight: vars.$font-weight-medium;
        transition: all 0.3s ease;
        cursor: pointer;
        margin-bottom: vars.$spacing-2;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          background: vars.$background-section;
          color: vars.$text-primary;
        }

        &.active {
          background: vars.$primary-color;
          color: vars.$white;
          @include mixins.shadow-elevation(2);
        }

        i {
          font-size: vars.$font-size-base;
          width: 20px;
          text-align: center;
        }
      }
    }

    .settings-content {
      @include mixins.card-base;
      @include mixins.shadow-elevation(2);
      overflow: hidden;

      .settings-section {
        padding: vars.$spacing-8;

        h3 {
          @include mixins.heading(
            vars.$font-size-xl,
            vars.$font-weight-semibold,
            vars.$text-primary
          );
          margin-bottom: vars.$spacing-6;
          padding-bottom: vars.$spacing-3;
          border-bottom: 1px solid vars.$border-light;
        }

        .form-group {
          margin-bottom: vars.$spacing-6;

          &:last-child {
            margin-bottom: 0;
          }

          label {
            @include mixins.form-label;
            color: vars.$text-primary;
            font-weight: vars.$font-weight-semibold;
            margin-bottom: vars.$spacing-2;
          }

          input,
          textarea,
          select {
            @include mixins.form-input;
            font-size: vars.$font-size-base;
          }

          textarea {
            resize: vertical;
            min-height: 80px;
          }
        }

        .form-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: vars.$spacing-4;
        }

        .checkbox-group {
          .checkbox-label {
            @include mixins.flex-align(flex-start, center);
            gap: vars.$spacing-3;
            cursor: pointer;
            @include mixins.text(
              vars.$font-size-base,
              vars.$text-primary,
              vars.$font-weight-medium
            );

            input[type="checkbox"] {
              position: absolute;
              opacity: 0;
              cursor: pointer;

              &:checked + .checkmark {
                background: vars.$primary-color;
                border-color: vars.$primary-color;

                &::after {
                  display: block;
                }
              }
            }

            .checkmark {
              position: relative;
              width: 20px;
              height: 20px;
              background: vars.$white;
              border: 2px solid vars.$border-medium;
              border-radius: vars.$border-radius-sm;
              transition: all 0.3s ease;

              &::after {
                content: "";
                position: absolute;
                display: none;
                left: 6px;
                top: 2px;
                width: 6px;
                height: 10px;
                border: solid vars.$white;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
              }
            }
          }
        }

        .notification-options {
          .form-group {
            margin-bottom: vars.$spacing-4;
          }
        }

        .test-section {
          margin-top: vars.$spacing-6;
          padding-top: vars.$spacing-4;
          border-top: 1px solid vars.$border-light;

          .btn-outline {
            @include mixins.button-ghost(vars.$text-secondary);
            border: 1px solid vars.$border-medium;
            @include mixins.flex-align(center, center);
            gap: vars.$spacing-2;

            &:hover {
              background: vars.$background-section;
              border-color: vars.$primary-color;
              color: vars.$primary-color;
            }
          }
        }

        .backup-info {
          margin: vars.$spacing-6 0;
          padding: vars.$spacing-4;
          background: vars.$background-light;
          border-radius: vars.$border-radius-base;

          .info-item {
            @include mixins.flex-between;

            .label {
              @include mixins.text(
                vars.$font-size-sm,
                vars.$text-secondary,
                vars.$font-weight-medium
              );
            }

            .value {
              @include mixins.text(
                vars.$font-size-sm,
                vars.$text-primary,
                vars.$font-weight-semibold
              );
            }
          }
        }

        .backup-actions {
          margin-top: vars.$spacing-6;
          padding-top: vars.$spacing-4;
          border-top: 1px solid vars.$border-light;

          .btn-primary {
            @include mixins.button-primary;
            @include mixins.flex-align(center, center);
            gap: vars.$spacing-2;
          }
        }

        .maintenance-warning {
          @include mixins.flex-align(flex-start, center);
          gap: vars.$spacing-3;
          padding: vars.$spacing-4;
          background: rgba(vars.$warning-color, 0.1);
          border: 1px solid vars.$warning-color;
          border-radius: vars.$border-radius-base;
          margin-top: vars.$spacing-4;

          i {
            color: vars.$warning-color;
            font-size: vars.$font-size-lg;
          }

          span {
            @include mixins.text(
              vars.$font-size-sm,
              vars.$warning-color,
              vars.$font-weight-medium
            );
          }
        }
      }
    }
  }

  .loading-spinner {
    @include mixins.flex-center;
    flex-direction: column;
    padding: vars.$spacing-16;
    gap: vars.$spacing-4;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid vars.$border-light;
      border-top: 4px solid vars.$primary-color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    p {
      @include mixins.text(vars.$font-size-base, vars.$text-secondary);
      margin: 0;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // === RESPONSIVE DESIGN ===
  @include mixins.tablet {
    padding: vars.$spacing-4;

    .page-header {
      flex-direction: column;
      gap: vars.$spacing-4;
      text-align: center;

      .btn-primary {
        align-self: center;
      }
    }

    .settings-container {
      grid-template-columns: 1fr;
      gap: vars.$spacing-6;

      .settings-nav {
        display: flex;
        flex-wrap: wrap;
        gap: vars.$spacing-2;
        padding: vars.$spacing-3;

        .nav-btn {
          flex: 1;
          min-width: 120px;
          margin-bottom: 0;
          justify-content: center;
        }
      }
    }
  }

  @include mixins.mobile {
    padding: vars.$spacing-3;

    .page-header {
      padding: vars.$spacing-4;

      .header-content h1 {
        font-size: vars.$font-size-2xl;
      }

      .btn-primary {
        padding: vars.$spacing-2 vars.$spacing-3;
        font-size: vars.$font-size-sm;
      }
    }

    .settings-container {
      gap: vars.$spacing-4;

      .settings-nav {
        .nav-btn {
          padding: vars.$spacing-2 vars.$spacing-3;
          font-size: vars.$font-size-sm;

          i {
            font-size: vars.$font-size-sm;
          }
        }
      }

      .settings-content {
        .settings-section {
          padding: vars.$spacing-4;

          h3 {
            font-size: vars.$font-size-lg;
          }

          .form-row {
            grid-template-columns: 1fr;
            gap: vars.$spacing-3;
          }

          .checkbox-group {
            .checkbox-label {
              font-size: vars.$font-size-sm;

              .checkmark {
                width: 18px;
                height: 18px;

                &::after {
                  left: 5px;
                  top: 1px;
                  width: 5px;
                  height: 8px;
                }
              }
            }
          }
        }
      }
    }
  }
}
