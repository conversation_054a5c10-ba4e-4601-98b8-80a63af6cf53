@use '../base/variables' as vars;
@use '../base/mixin' as mix;

.register-form__container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: unset;
    background: transparent;
    width: 100%;
    padding: 0 10px;
}

.register-form__box {
    background: transparent;
    border-radius: 24px;
    padding: 40px 32px 32px 32px;
    width: 100%;
    max-width: 420px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.register-form__logo {
    font-family: vars.$font-primary;
    font-size: 2.2rem;
    font-weight: 900;
    margin-bottom: 20px;
    letter-spacing: 2px;
    text-align: center;
    background: linear-gradient(135deg, vars.$primary-color, vars.$secondary-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.register-form__welcome {
    font-family: vars.$font-primary;
    font-size: 1.4rem;
    font-weight: 900;
    margin-bottom: 36px;
    text-align: center;
    line-height: 1.3;
    letter-spacing: 0.5px;
    color: vars.$text-color;
}

// Google login button styles
.register-form__google-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 370px;
    padding: 14px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    gap: 12px;
    margin-bottom: 28px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-color: #d0d0d0;
        transform: translateY(-1px);
    }

    &:active {
        transform: translateY(0);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
}

.register-form__google-icon {
    display: flex;
    align-items: center;
    margin-right: 8px;
}

.register-form__divider {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 320px;
    margin: 16px 0 24px 0;
    color: #bbb;
    font-size: 13px;
    gap: 8px;

    span:first-child,
    span:last-child {
        flex: 1;
        height: 1px;
        background: #eee;
    }
}

.register-form__divider-text {
    padding: 0 8px;
    white-space: nowrap;
    color: #bbb;
    font-weight: 400;
    font-size: 14px;
    letter-spacing: 0.5px;
}

.register-form__form {
    width: 100%;
    max-width: 370px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;

    // When input is focused, make the eye icon more visible
    &:focus-within .password-toggle-btn {
        opacity: 1;
        color: vars.$primary-color;
    }

    // Add a subtle border glow when focused
    &:focus-within {
        .register-form__input {
            box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
        }
    }
}

.password-toggle-btn {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    cursor: pointer;
    color: #888;
    font-size: 18px;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    opacity: 0.7;

    &:hover {
        color: vars.$primary-color;
        background-color: rgba(vars.$primary-color, 0.08);
        transform: scale(1.15);
        opacity: 1;
        box-shadow: 0 2px 8px rgba(vars.$primary-color, 0.15);
    }

    &:focus {
        outline: none;
        color: vars.$primary-color;
        background-color: rgba(vars.$primary-color, 0.12);
        box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.2);
        opacity: 1;
    }

    &:active {
        transform: scale(0.9);
        transition: transform 0.1s ease;
    }

    // Add a subtle animation when toggling
    svg {
        transition: all 0.2s ease;
    }

    // Different styles for show/hide states
    &.showing {
        color: vars.$primary-color;
        opacity: 1;
    }

    // Mobile responsive
    @media (max-width: 480px) {
        width: 32px;
        height: 32px;
        font-size: 16px;
        right: 10px;
        padding: 6px;
    }
}

.register-form__label {
    font-family: vars.$font-secondary;
    font-weight: 700;
    font-size: 15px;
    margin-bottom: 6px;
    letter-spacing: 0.5px;
    color: vars.$text-color;
}

.register-form__input {
    width: 100%;
    padding: 14px 50px 14px 16px; // Add right padding for the eye icon
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 16px;
    outline: none;
    margin-bottom: 4px;
    transition: all 0.3s ease;
    background: #fafafa;
    font-family: vars.$font-secondary;
    color: vars.$text-color;

    // For password fields, ensure proper spacing
    .password-input-container & {
        padding-right: 55px;
    }

    &:focus {
        border: 2px solid vars.$primary-color;
        background: #fff;
        box-shadow: 0 0 0 3px rgba(vars.$primary-color, 0.1);
        transform: translateY(-1px);
    }

    &.error {
        border-color: #dc3545;
        background: #fff5f5;
    }

    &::placeholder {
        color: #999;
        font-size: 15px;
    }
}

.register-form__submit {
    width: 100%;
    max-width: 220px;
    padding: 14px 0;
    background: linear-gradient(135deg, vars.$secondary-color, vars.$secondary-hover);
    color: #fff;
    border: none;
    border-radius: 12px;
    font-family: vars.$font-secondary;
    font-weight: 700;
    font-size: 16px;
    margin-top: 16px;
    cursor: pointer;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    align-self: center;
    position: relative;
    overflow: hidden;

    &:hover {
        background: linear-gradient(135deg, vars.$secondary-hover, vars.$secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(vars.$secondary-color, 0.3);
    }

    &:active {
        transform: translateY(0);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
}

.register-form__login {
    text-align: center;
    margin-top: 30px;
    font-size: 15px;
    font-family: vars.$font-secondary;
    color: vars.$text-color;

    a {
        color: vars.$text-color;
        text-decoration: none;

        span {
            font-weight: 700;
            text-decoration: underline;
            color: vars.$secondary-color;
            margin-left: 4px;
        }
    }
}

.register-form__error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 4px;
    margin-bottom: 8px;
    padding: 6px 8px;
    background: #fff5f5;
    border-radius: 6px;
    border-left: 3px solid #dc3545;
}

// Responsive Design
@media (max-width: 768px) {
    .register-form__container {
        padding: 0 5px;
    }

    .register-form__box {
        max-width: 100%;
        padding: 30px 20px 25px 20px;
    }

    .register-form__logo {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }

    .register-form__welcome {
        font-size: 1.2rem;
        margin-bottom: 30px;
    }

    .register-form__form,
    .register-form__google-btn,
    .register-form__divider {
        max-width: 100%;
    }

    .register-form__google-btn {
        padding: 12px 16px;
        font-size: 15px;
    }

    .register-form__input {
        padding: 12px 14px;
        font-size: 15px;
    }

    .register-form__submit {
        padding: 12px 0;
        font-size: 15px;
        max-width: 200px;
    }
}

@media (max-width: 576px) {
    .register-form__box {
        padding: 20px 15px;
    }

    .register-form__logo {
        font-size: 1.6rem;
        margin-bottom: 12px;
    }

    .register-form__welcome {
        font-size: 1.1rem;
        margin-bottom: 25px;
        line-height: 1.4;
    }

    .register-form__google-btn {
        padding: 11px 14px;
        font-size: 14px;
        margin-bottom: 20px;
    }

    .register-form__input {
        padding: 11px 12px;
        font-size: 14px;
    }

    .register-form__label {
        font-size: 14px;
    }

    .register-form__submit {
        padding: 11px 0;
        font-size: 14px;
        margin-top: 12px;
        max-width: 180px;
    }

    .register-form__login {
        font-size: 13px;
        margin-top: 25px;
    }
}

@media (max-width: 480px) {
    .register-form__container {
        padding: 0;
    }

    .register-form__box {
        padding: 15px 10px;
        max-width: 100%;
    }

    .register-form__logo {
        font-size: 1.4rem;
        margin-bottom: 10px;
    }

    .register-form__welcome {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .register-form__google-btn {
        padding: 10px 12px;
        font-size: 13px;
        margin-bottom: 18px;
        border-radius: 8px;
    }

    .register-form__input {
        padding: 10px;
        font-size: 14px;
        border-radius: 8px;
    }

    .register-form__submit {
        padding: 10px 0;
        font-size: 13px;
        border-radius: 8px;
        max-width: 100%;
        margin-top: 10px;
    }

    .register-form__divider {
        margin: 12px 0 18px 0;
        font-size: 12px;
    }

    .register-form__login {
        font-size: 12px;
        margin-top: 20px;
    }

    .form-group {
        margin-bottom: 6px;
    }
}