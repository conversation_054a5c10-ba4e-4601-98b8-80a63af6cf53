import React from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Tag, Button, Space, Typography } from "antd";
import { HeartFilled, FileTextOutlined } from "@ant-design/icons";
import DetailedStatusTimeline from "./DetailedStatusTimeline";
import "../../styles/components/ActivityDetailModal.scss";

const { Text } = Typography;

const ActivityDetailModal = ({
  visible,
  onCancel,
  activity,
  getStatusInfo,
  formatDate,
  loadingDetails = false,
}) => {
  if (!activity) return null;

  return (
    <Modal
      title={
        <Space>
          <span className={`detail-modal-icon ${activity.type}`}>
            {activity.type === "donation" ? (
              <HeartFilled />
            ) : (
              <FileTextOutlined />
            )}
          </span>
          <Text strong className="detail-modal-title">
            Chi tiết{" "}
            {activity.type === "donation" ? "lịch hẹn hiến máu" : "yêu cầu máu"}
          </Text>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button
          key="close"
          onClick={onCancel}
          className="detail-modal-close-button"
        >
          Đóng
        </Button>,
      ]}
      width={800}
      className="detail-modal"
    >
      <div className="detail-modal-content">
        {/* Header Info */}
        <Card
          size="small"
          className="detail-header-card"
          style={{
            marginBottom: 16,
            borderRadius: "12px",
            background: "linear-gradient(135deg, #f6fbfd, #e3f2fd)",
            border: "1px solid #e8ecef",
          }}
        >
          <Row gutter={16} align="middle">
            <Col>
              <div
                style={{
                  width: 50,
                  height: 50,
                  borderRadius: "12px",
                  background:
                    activity.type === "donation"
                      ? "linear-gradient(135deg, #d32f2f, #f44336)"
                      : "linear-gradient(135deg, #1976d2, #2196f3)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "white",
                  fontSize: "20px",
                }}
              >
                {activity.type === "donation" ? (
                  <HeartFilled />
                ) : (
                  <FileTextOutlined />
                )}
              </div>
            </Col>
            <Col flex={1}>
              <div>
                <Text strong style={{ fontSize: "16px", color: "#222" }}>
                  {activity.title}
                </Text>
                <br />
                <Text style={{ fontSize: "13px", color: "#6c757d" }}>
                  ID: {activity.id} • Tạo lúc: {formatDate(activity.createdAt)}
                </Text>
              </div>
            </Col>
            <Col>
              <Tag
                color={
                  activity.isCancelled
                    ? "error"
                    : getStatusInfo(
                        activity.displayStatus || activity.status,
                        activity.type
                      ).color
                }
                style={{ fontSize: "12px", fontWeight: "600" }}
              >
                {activity.isCancelled
                  ? "❌ ĐÃ HỦY"
                  : getStatusInfo(
                      activity.status,
                      activity.type
                    ).text.toUpperCase()}
              </Tag>
            </Col>
          </Row>
        </Card>

        {/* Detail Sections */}
        <div className="detail-sections">
          {/* Basic Info */}
          <Card
            size="small"
            title="Thông tin cơ bản"
            style={{ marginBottom: 16 }}
          >
            <Row gutter={[16, 8]}>
              <Col span={12}>
                <div className="detail-item">
                  <Text strong>Loại hoạt động:</Text>
                  <br />
                  <Text>
                    {activity.type === "donation" ? "Hiến máu" : "Yêu cầu máu"}
                  </Text>
                </div>
              </Col>
              <Col span={12}>
                <div className="detail-item">
                  <Text strong>Trạng thái:</Text>
                  <br />
                  <Tag
                    color={
                      getStatusInfo(
                        activity.displayStatus || activity.status,
                        activity.type
                      ).color
                    }
                  >
                    {
                      getStatusInfo(
                        activity.displayStatus || activity.status,
                        activity.type
                      ).text
                    }
                  </Tag>
                </div>
              </Col>
              <Col span={12}>
                <div className="detail-item">
                  <Text strong>Nhóm máu:</Text>
                  <br />
                  <Text
                    style={{
                      fontSize: "16px",
                      fontWeight: "600",
                      color: "#d32f2f",
                    }}
                  >
                    {activity.bloodType}
                  </Text>
                </div>
              </Col>
              <Col span={12}>
                <div className="detail-item">
                  <Text strong>Số lượng:</Text>
                  <br />
                  <Text>{activity.quantity}</Text>
                </div>
              </Col>
            </Row>
          </Card>

          {/* Appointment Info for Donations */}
          {activity.type === "donation" && (
            <Card
              size="small"
              title="Thông tin lịch hẹn"
              style={{ marginBottom: 16 }}
            >
              <Row gutter={[16, 8]}>
                <Col span={8}>
                  <div className="detail-item">
                    <Text strong>Ngày hẹn:</Text>
                    <br />
                    <Text style={{ fontSize: "15px", color: "#1976d2" }}>
                      {formatDate(activity.appointmentDate)}
                    </Text>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="detail-item">
                    <Text strong>Khung giờ:</Text>
                    <br />
                    <Text>{activity.timeSlot || "Chưa xác định"}</Text>
                  </div>
                </Col>
                <Col span={8}>
                  <div className="detail-item">
                    <Text strong>Ngày tạo:</Text>
                    <br />
                    <Text>{formatDate(activity.createdAt)}</Text>
                  </div>
                </Col>
                <Col span={24}>
                  <div className="detail-item">
                    <Text strong>Địa điểm:</Text>
                    <br />
                    <Text>{activity.location}</Text>
                  </div>
                </Col>
              </Row>
            </Card>
          )}

          {/* Health Check Info for Donations */}
          {activity.type === "donation" &&
            (activity.weight ||
              activity.height ||
              activity.doctorNotes ||
              (activity.healthCheck &&
                (activity.healthCheck.heartRate ||
                  activity.healthCheck.bloodPressure ||
                  activity.healthCheck.hemoglobin ||
                  activity.healthCheck.temperature))) && (
              <Card
                size="small"
                title="🏥 Thông tin khám sức khỏe"
                style={{ marginBottom: 16 }}
              >
                <Row gutter={[16, 12]}>
                  {/* Chỉ số cơ bản */}
                  {(activity.weight > 0 ||
                    (activity.healthCheck &&
                      activity.healthCheck.weight > 0)) && (
                    <Col span={6}>
                      <div className="detail-item">
                        <Text strong style={{ color: "#1976d2" }}>
                          ⚖️ Cân nặng:
                        </Text>
                        <br />
                        <Text
                          style={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "#d32f2f",
                          }}
                        >
                          {activity.healthCheck?.weight || activity.weight} kg
                        </Text>
                      </div>
                    </Col>
                  )}

                  {(activity.height > 0 ||
                    (activity.healthCheck &&
                      activity.healthCheck.height > 0)) && (
                    <Col span={6}>
                      <div className="detail-item">
                        <Text strong style={{ color: "#1976d2" }}>
                          📏 Chiều cao:
                        </Text>
                        <br />
                        <Text
                          style={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "#d32f2f",
                          }}
                        >
                          {activity.healthCheck?.height || activity.height} cm
                        </Text>
                      </div>
                    </Col>
                  )}

                  {/* Nhịp tim */}
                  {activity.healthCheck?.heartRate && (
                    <Col span={6}>
                      <div className="detail-item">
                        <Text strong style={{ color: "#1976d2" }}>
                          💓 Nhịp tim:
                        </Text>
                        <br />
                        <Text
                          style={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "#d32f2f",
                          }}
                        >
                          {activity.healthCheck.heartRate} bpm
                        </Text>
                      </div>
                    </Col>
                  )}

                  {/* Huyết áp */}
                  {activity.healthCheck?.bloodPressure && (
                    <Col span={6}>
                      <div className="detail-item">
                        <Text strong style={{ color: "#1976d2" }}>
                          🩸 Huyết áp:
                        </Text>
                        <br />
                        <Text
                          style={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "#d32f2f",
                          }}
                        >
                          {activity.healthCheck.bloodPressure} mmHg
                        </Text>
                      </div>
                    </Col>
                  )}

                  {/* Huyết sắc tố */}
                  {activity.healthCheck?.hemoglobin && (
                    <Col span={6}>
                      <div className="detail-item">
                        <Text strong style={{ color: "#1976d2" }}>
                          🔬 Huyết sắc tố:
                        </Text>
                        <br />
                        <Text
                          style={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "#d32f2f",
                          }}
                        >
                          {activity.healthCheck.hemoglobin} g/dL
                        </Text>
                      </div>
                    </Col>
                  )}

                  {/* Nhiệt độ */}
                  {activity.healthCheck?.temperature && (
                    <Col span={6}>
                      <div className="detail-item">
                        <Text strong style={{ color: "#1976d2" }}>
                          🌡️ Nhiệt độ:
                        </Text>
                        <br />
                        <Text
                          style={{
                            fontSize: "16px",
                            fontWeight: "600",
                            color: "#d32f2f",
                          }}
                        >
                          {activity.healthCheck.temperature}°C
                        </Text>
                      </div>
                    </Col>
                  )}

                  {/* Lịch sử hiến máu */}
                  {activity.hasDonated !== undefined && (
                    <Col span={6}>
                      <div className="detail-item">
                        <Text strong style={{ color: "#1976d2" }}>
                          🩸 Đã hiến máu:
                        </Text>
                        <br />
                        <Tag
                          color={activity.hasDonated ? "success" : "default"}
                          style={{ fontSize: "14px" }}
                        >
                          {activity.hasDonated ? "✅ Có" : "❌ Không"}
                        </Tag>
                      </div>
                    </Col>
                  )}

                  {activity.lastDonationDate && (
                    <Col span={12}>
                      <div className="detail-item">
                        <Text strong style={{ color: "#1976d2" }}>
                          📅 Lần hiến máu gần nhất:
                        </Text>
                        <br />
                        <Text style={{ fontSize: "15px", color: "#666" }}>
                          {formatDate(activity.lastDonationDate)}
                        </Text>
                      </div>
                    </Col>
                  )}

                  {/* Ghi chú của bác sĩ */}
                  {activity.doctorNotes && (
                    <Col span={24}>
                      <div className="detail-item">
                        <Text strong style={{ color: "#1976d2" }}>
                          👨‍⚕️ Ghi chú của bác sĩ:
                        </Text>
                        <br />
                        <div
                          style={{
                            background:
                              "linear-gradient(135deg, #f0f8ff, #e6f3ff)",
                            padding: "16px",
                            borderRadius: "12px",
                            border: "2px solid #d1ecf1",
                            marginTop: "8px",
                            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                          }}
                        >
                          <Text style={{ fontSize: "15px", lineHeight: "1.6" }}>
                            {activity.doctorNotes}
                          </Text>
                        </div>
                      </div>
                    </Col>
                  )}
                </Row>
              </Card>
            )}

          {/* Patient Info for Requests */}
          {activity.type === "request" && (
            <Card
              size="small"
              title="Thông tin bệnh nhân"
              style={{ marginBottom: 16 }}
            >
              <Row gutter={[16, 8]}>
                <Col span={12}>
                  <div className="detail-item">
                    <Text strong>Tên bệnh nhân:</Text>
                    <br />
                    <Text>{activity.patientName || "Không có thông tin"}</Text>
                  </div>
                </Col>
                <Col span={6}>
                  <div className="detail-item">
                    <Text strong>Tuổi:</Text>
                    <br />
                    <Text>{activity.patientAge || "N/A"}</Text>
                  </div>
                </Col>
                <Col span={6}>
                  <div className="detail-item">
                    <Text strong>Giới tính:</Text>
                    <br />
                    <Text>
                      {activity.patientGender === "male"
                        ? "Nam"
                        : activity.patientGender === "female"
                        ? "Nữ"
                        : activity.patientGender || "N/A"}
                    </Text>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="detail-item">
                    <Text strong>Mối quan hệ:</Text>
                    <br />
                    <Text>{activity.relationship || "Không có thông tin"}</Text>
                  </div>
                </Col>
                {activity.hospitalName && (
                  <Col span={12}>
                    <div className="detail-item">
                      <Text strong>Cơ sở y tế đang khám chữa bệnh:</Text>
                      <br />
                      <Text>{activity.hospitalName}</Text>
                    </div>
                  </Col>
                )}
              </Row>
            </Card>
          )}

          {/* Medical Info for Requests */}
          {activity.type === "request" &&
            (activity.doctorName || activity.medicalCondition) && (
              <Card
                size="small"
                title="Thông tin y tế"
                style={{ marginBottom: 16 }}
              >
                <Row gutter={[16, 8]}>
                  {activity.doctorName && (
                    <Col span={12}>
                      <div className="detail-item">
                        <Text strong>Bác sĩ điều trị:</Text>
                        <br />
                        <Text>{activity.doctorName}</Text>
                      </div>
                    </Col>
                  )}
                  {activity.doctorPhone && (
                    <Col span={12}>
                      <div className="detail-item">
                        <Text strong>Số điện thoại bác sĩ:</Text>
                        <br />
                        <Text>{activity.doctorPhone}</Text>
                      </div>
                    </Col>
                  )}
                  {activity.medicalCondition && (
                    <Col span={24}>
                      <div className="detail-item">
                        <Text strong>Tình trạng bệnh lý:</Text>
                        <br />
                        <div
                          style={{
                            background: "#f8f9fa",
                            padding: "12px",
                            borderRadius: "8px",
                            border: "1px solid #e9ecef",
                            marginTop: "8px",
                          }}
                        >
                          <Text>{activity.medicalCondition}</Text>
                        </div>
                      </div>
                    </Col>
                  )}
                </Row>
              </Card>
            )}

          {/* Notes */}
          {activity.notes && (
            <Card size="small" title="Ghi chú" style={{ marginBottom: 16 }}>
              <div
                style={{
                  background: "#f8f9fa",
                  padding: "12px",
                  borderRadius: "8px",
                  border: "1px solid #e9ecef",
                }}
              >
                <Text>{activity.notes}</Text>
              </div>
            </Card>
          )}

          {/* Detailed Status Timeline */}
          <DetailedStatusTimeline
            activity={activity}
            workflowType={activity.type === "donation" ? "donation" : "request"}
          />

          {/* Additional Info */}
          <Card size="small" title="Thông tin bổ sung">
            <Row gutter={[16, 8]}>
              <Col span={12}>
                <div className="detail-item">
                  <Text strong>Ngày tạo:</Text>
                  <br />
                  <Text>{formatDate(activity.createdAt)}</Text>
                </div>
              </Col>
              {activity.completedAt && (
                <Col span={12}>
                  <div className="detail-item">
                    <Text strong>Ngày hoàn thành:</Text>
                    <br />
                    <Text>{formatDate(activity.completedAt)}</Text>
                  </div>
                </Col>
              )}
              {activity.isCancelled && activity.cancelledAt && (
                <Col span={12}>
                  <div className="detail-item">
                    <Text strong>Ngày hủy:</Text>
                    <br />
                    <Text type="danger">
                      {formatDate(activity.cancelledAt)}
                    </Text>
                  </div>
                </Col>
              )}
            </Row>
          </Card>
        </div>
      </div>
    </Modal>
  );
};

export default ActivityDetailModal;
