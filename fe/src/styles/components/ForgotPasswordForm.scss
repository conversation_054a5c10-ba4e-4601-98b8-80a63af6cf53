@use '../base/variables' as vars;
@use '../base/mixin' as mix;

.forgot-password-form__container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: unset;
    background: transparent;
    width: 100%;
}

.forgot-password-form__box {
    background: transparent;
    border-radius: 24px;
    padding: 40px 32px 32px 32px;
    width: 100%;
    max-width: 370px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.forgot-password-form__logo {
    font-family: vars.$font-primary;
    font-size: 2rem;
    font-weight: 900;
    margin-bottom: 18px;
    letter-spacing: 2px;
    text-align: center;
}

.forgot-password-form__title {
    font-family: vars.$font-primary;
    font-size: 1.4rem;
    font-weight: 900;
    margin-bottom: 16px;
    text-align: center;
    line-height: 1.3;
    letter-spacing: 0.5px;
}

.forgot-password-form__description {
    font-family: vars.$font-secondary;
    font-size: 0.9rem;
    color: #666;
    text-align: center;
    margin-bottom: 32px;
    line-height: 1.4;
}

.forgot-password-form__form {
    width: 100%;
    max-width: 320px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.forgot-password-form__label {
    font-family: vars.$font-secondary;
    font-weight: 700;
    font-size: 15px;
    margin-bottom: 4px;
    letter-spacing: 0.5px;
}

.forgot-password-form__input {
    width: 100%;
    padding: 10px 12px;
    border: 1.5px solid #ddd;
    border-radius: 8px;
    font-size: 15px;
    outline: none;
    margin-bottom: 8px;
    transition: border 0.2s;
    background: #fafafa;
    font-family: vars.$secondary-color;

    &:focus {
        border: 1.5px solid vars.$primary-color;
    }

    &.error {
        border-color: #dc3545;
    }
}

.forgot-password-form__success {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    margin-bottom: 0.5rem;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 8px 12px;
    text-align: center;
}

.forgot-password-form__error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    margin-bottom: 0.5rem;
}

.forgot-password-form__submit {
    width: 100%;
    padding: 12px 0;
    background: vars.$secondary-color;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-family: vars.$font-secondary;
    font-weight: 700;
    font-size: 16px;
    margin-top: 8px;
    cursor: pointer;
    letter-spacing: 1px;
    transition: background 0.2s;

    &:hover {
        background: vars.$secondary-hover;
    }

    &:disabled {
        background: #ccc;
        cursor: not-allowed;
    }
}

.forgot-password-form__back {
    text-align: center;
    margin-top: 24px;
    font-size: 15px;
    font-family: vars.$font-secondary;

    a {
        color: vars.$secondary-color;
        text-decoration: none;
        font-weight: 600;

        &:hover {
            text-decoration: underline;
        }
    }
}

// Responsive
@media (max-width: 700px) {
    .forgot-password-form__box {
        max-width: 100vw;
        padding: 0 8vw;
    }

    .forgot-password-form__form {
        max-width: 100vw;
    }
}

@media (max-width: 500px) {
    .forgot-password-form__box {
        padding: 0 2vw;
    }

    .forgot-password-form__form {
        max-width: 100vw;
    }

    .forgot-password-form__back {
        font-size: 12px;
    }
}
