import React from "react";
import { Mo<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, List, Typography, Space, Tag } from "antd";
import {
  ExclamationCircleOutlined,
  UserOutlined,
  EditOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";

const { Title, Text } = Typography;

/**
 * Unified modal component for profile incomplete and blood request status
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Modal visibility
 * @param {Function} props.onClose - Close modal handler
 * @param {string} props.type - Modal type: 'profile-incomplete' or 'blood-request-status'
 * @param {string} props.context - Context: 'donation', 'request', or 'general'
 * @param {Object} props.profileCheckResult - Profile check result (for profile-incomplete type)
 * @param {Object} props.pendingRequest - Pending request data (for blood-request-status type)
 * @param {Function} props.onGoToProfile - Navigate to profile page handler
 * @param {Function} props.onViewHistory - View history handler
 */
const UnifiedModal = ({
  visible,
  onClose,
  type = 'profile-incomplete', // 'profile-incomplete' or 'blood-request-status'
  context = 'general', // 'donation', 'request', or 'general'
  profileCheckResult = null,
  pendingRequest = null,
  onGoToProfile = null,
  onViewHistory = null,
}) => {
  const getProfileIncompleteConfig = () => {
    switch (context) {
      case 'donation':
        return {
          title: 'Cần cập nhật hồ sơ để hiến máu',
          description: 'Để đảm bảo quy trình đăng ký hiến máu diễn ra thuận lợi, bạn cần cập nhật đầy đủ thông tin cá nhân trước khi tiếp tục.',
          actionText: 'đăng ký hiến máu'
        };
      case 'request':
        return {
          title: 'Cần cập nhật hồ sơ để nhận máu',
          description: 'Để đảm bảo quy trình đăng ký nhận máu diễn ra thuận lợi, bạn cần cập nhật đầy đủ thông tin cá nhân trước khi tiếp tục.',
          actionText: 'đăng ký nhận máu'
        };
      default:
        return {
          title: 'Cần cập nhật hồ sơ cá nhân',
          description: 'Để đảm bảo các quy trình diễn ra thuận lợi, bạn cần cập nhật đầy đủ thông tin cá nhân trước khi tiếp tục.',
          actionText: 'sử dụng dịch vụ'
        };
    }
  };

  const getBloodRequestStatusInfo = (status) => {
    const statusMap = {
      0: {
        text: "Đang chờ xử lý",
        color: "orange",
        icon: <ClockCircleOutlined />,
        description: "Đơn đăng ký của bạn đang được xem xét bởi bộ phận y tế",
      },
      1: {
        text: "Đã chấp nhận",
        color: "green",
        icon: <CheckCircleOutlined />,
        description: "Đơn đăng ký đã được chấp nhận và đang được xử lý",
      },
      2: {
        text: "Hoàn thành",
        color: "blue",
        icon: <CheckCircleOutlined />,
        description: "Đơn đăng ký đã được hoàn thành thành công",
      },
      3: {
        text: "Từ chối",
        color: "red",
        icon: <CloseCircleOutlined />,
        description: "Đơn đăng ký đã bị từ chối",
      },
    };
    return statusMap[status] || statusMap[0];
  };

  const renderProfileIncompleteContent = () => {
    const config = getProfileIncompleteConfig();
    
    return (
      <>
        <Alert
          message="Hồ sơ cá nhân chưa đầy đủ"
          description={config.description}
          type="warning"
          showIcon
          style={{ marginBottom: "20px" }}
        />

        {profileCheckResult?.missingFields?.length > 0 && (
          <div>
            <Title level={5} style={{ marginBottom: "12px" }}>
              <UserOutlined style={{ marginRight: "8px" }} />
              Thông tin cần bổ sung:
            </Title>
            <List
              size="small"
              bordered
              dataSource={profileCheckResult.missingFields}
              renderItem={(field) => (
                <List.Item>
                  <Text type="danger">• {field}</Text>
                </List.Item>
              )}
              style={{ backgroundColor: "#fafafa" }}
            />
          </div>
        )}

        <Alert
          message="Lưu ý quan trọng"
          description={
            <ul style={{ margin: "8px 0", paddingLeft: "20px" }}>
              <li>Thông tin cá nhân phải chính xác theo giấy tờ tùy thân</li>
              <li>Thông tin này sẽ được sử dụng để xác minh trong quá trình {config.actionText}</li>
              <li>Sau khi cập nhật xong, bạn có thể quay lại {config.actionText}</li>
            </ul>
          }
          type="info"
          showIcon
          style={{ marginTop: "16px" }}
        />
      </>
    );
  };

  const renderBloodRequestStatusContent = () => {
    if (!pendingRequest) {
      return (
        <Alert
          message="Không tìm thấy thông tin đơn đăng ký"
          type="error"
          showIcon
        />
      );
    }

    const statusInfo = getBloodRequestStatusInfo(pendingRequest.status);

    return (
      <>
        <div style={{ marginBottom: "20px" }}>
          <Space direction="vertical" size="middle" style={{ width: "100%" }}>
            <div>
              <Text strong>Trạng thái đơn đăng ký:</Text>
              <div style={{ marginTop: "8px" }}>
                <Tag
                  color={statusInfo.color}
                  icon={statusInfo.icon}
                  style={{ fontSize: "14px", padding: "4px 12px" }}
                >
                  {statusInfo.text}
                </Tag>
              </div>
              <Text type="secondary" style={{ display: "block", marginTop: "8px" }}>
                {statusInfo.description}
              </Text>
            </div>

            <div>
              <Text strong>Thông tin đơn đăng ký:</Text>
              <div style={{ marginTop: "8px", padding: "12px", backgroundColor: "#f5f5f5", borderRadius: "6px" }}>
                <Space direction="vertical" size="small">
                  <Text>Bệnh nhân: <strong>{pendingRequest.patientName}</strong></Text>
                  <Text>Nhóm máu: <strong>{pendingRequest.bloodGroup}{pendingRequest.rhType}</strong></Text>
                  <Text>Số lượng: <strong>{pendingRequest.quantity} ml</strong></Text>
                  <Text>Ngày tạo: <strong>{new Date(pendingRequest.createdTime).toLocaleDateString('vi-VN')}</strong></Text>
                </Space>
              </div>
            </div>
          </Space>
        </div>

        <Alert
          message="Hướng dẫn tiếp theo"
          description={
            <ul style={{ margin: "8px 0", paddingLeft: "20px" }}>
              <li>Theo dõi trạng thái đơn trong mục "Lịch sử hoạt động"</li>
              <li>Liên hệ hotline: <strong>1900-xxxx</strong> nếu cần hỗ trợ</li>
              <li>Đơn mới có thể được tạo sau khi đơn hiện tại được xử lý</li>
            </ul>
          }
          type="info"
          showIcon
        />
      </>
    );
  };

  const getModalConfig = () => {
    if (type === 'profile-incomplete') {
      const config = getProfileIncompleteConfig();
      return {
        title: (
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <ExclamationCircleOutlined style={{ color: "#faad14" }} />
            <span>{config.title}</span>
          </div>
        ),
        footer: [
          <Button key="cancel" onClick={onClose}>
            Để sau
          </Button>,
          <Button
            key="update"
            type="primary"
            icon={<EditOutlined />}
            onClick={onGoToProfile}
          >
            Cập nhật hồ sơ
          </Button>,
        ],
        content: renderProfileIncompleteContent()
      };
    } else {
      return {
        title: (
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <InfoCircleOutlined style={{ color: "#1890ff" }} />
            <span>Trạng thái đơn đăng ký nhận máu</span>
          </div>
        ),
        footer: [
          <Button key="close" onClick={onClose}>
            Đóng
          </Button>,
          <Button key="history" type="primary" onClick={onViewHistory}>
            Xem lịch sử hoạt động
          </Button>,
        ],
        content: renderBloodRequestStatusContent()
      };
    }
  };

  const modalConfig = getModalConfig();

  return (
    <Modal
      title={modalConfig.title}
      open={visible}
      onCancel={onClose}
      footer={modalConfig.footer}
      width={500}
      centered
    >
      <div style={{ padding: "16px 0" }}>
        {modalConfig.content}
      </div>
    </Modal>
  );
};

export default UnifiedModal;
