@use "../base/variables" as vars;

.blood-inventory-view {
  .blood-inventory-view-content {
    &.no-margin-padding {
      margin: 0 !important;
      padding: 0 !important;
    }

    .page-header {
      margin-bottom: 2rem;
      background: white;
      padding: 2rem;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(23, 162, 184, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 1rem;

      div {
        h1 {
          background: linear-gradient(45deg, #17a2b8, #20c997);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 0.5rem;
          font-size: 2.5rem;
          font-weight: 700;
        }

        p {
          color: #6c757d;
          font-size: 1.2rem;
          margin: 0 0 1rem 0;
          font-weight: 500;
        }

        .access-level {
          .full-access {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            padding: 0.75rem 1.5rem;
            border-radius: 15px;
            font-size: 1rem;
            font-weight: 600;
            border: 2px solid #c3e6cb;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(21, 87, 36, 0.1);
          }

          .limited-access {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            padding: 0.75rem 1.5rem;
            border-radius: 15px;
            font-size: 1rem;
            font-weight: 600;
            border: 2px solid #ffeaa7;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(133, 100, 4, 0.1);
          }
        }
      }

      .view-controls {
        display: flex;
        gap: 1rem;

        .btn {
          padding: 0.75rem 1.5rem;
          border: none;
          border-radius: 12px;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.btn-primary {
            background: linear-gradient(45deg, #17a2b8, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
            }
          }

          &.btn-secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
            }
          }
        }
      }
    }

    .quick-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;

      .stat-card {
        background: white;
        padding: 2rem;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 1.5rem;
        transition: all 0.3s ease;
        border: 1px solid rgba(23, 162, 184, 0.1);
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
        }

        &.total::before {
          background: linear-gradient(45deg, #17a2b8, #20c997);
        }

        &.critical::before {
          background: linear-gradient(45deg, #dc3545, #c82333);
        }

        &.low::before {
          background: linear-gradient(45deg, #ffc107, #fd7e14);
        }

        &.rare::before {
          background: linear-gradient(45deg, #6f42c1, #e83e8c);
        }

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
          font-size: 3rem;
          width: 80px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background: linear-gradient(
            135deg,
            rgba(23, 162, 184, 0.1),
            rgba(32, 201, 151, 0.1)
          );
        }

        .stat-info {
          flex: 1;

          h3 {
            margin: 0 0 0.5rem 0;
            color: #6c757d;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
          }

          .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 0;
            background: linear-gradient(45deg, #17a2b8, #20c997);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;

            &.danger {
              background: linear-gradient(45deg, #dc3545, #c82333);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }

            &.warning {
              background: linear-gradient(45deg, #ffc107, #fd7e14);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }

            &.rare {
              background: linear-gradient(45deg, #6f42c1, #e83e8c);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }
    }

    .filters-section {
      background: white;
      padding: 2rem;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;
      border: 1px solid rgba(23, 162, 184, 0.1);

      .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        label {
          font-weight: 700;
          color: #495057;
          font-size: 1rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        select {
          padding: 0.75rem 1rem;
          border: 2px solid #e9ecef;
          border-radius: 12px;
          font-size: 1rem;
          min-width: 180px;
          background: white;
          transition: all 0.3s ease;
          font-weight: 500;

          &:focus {
            outline: none;
            border-color: #17a2b8;
            box-shadow: 0 0 0 4px rgba(23, 162, 184, 0.1);
            transform: translateY(-2px);
          }

          &:hover {
            border-color: #17a2b8;
            transform: translateY(-1px);
          }
        }
      }
    }

    .inventory-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: 1.5rem;
      margin-bottom: 3rem;

      .inventory-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
        }

        &.success::before {
          background: linear-gradient(45deg, #28a745, #20c997);
        }

        &.warning::before {
          background: linear-gradient(45deg, #ffc107, #fd7e14);
        }

        &.danger::before {
          background: linear-gradient(45deg, #dc3545, #c82333);
        }

        &:hover {
          transform: translateY(-5px) scale(1.02);
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
          padding: 1.5rem;
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
          display: flex;
          justify-content: space-between;
          align-items: center;

          .blood-type-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;

            .blood-type {
              background: linear-gradient(45deg, #17a2b8, #20c997);
              color: white;
              padding: 0.75rem 1rem;
              border-radius: 12px;
              font-weight: 700;
              font-size: 1.2rem;
              box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
            }

            .rare-badge {
              background: linear-gradient(45deg, #6f42c1, #e83e8c);
              color: white;
              padding: 0.5rem 0.75rem;
              border-radius: 10px;
              font-size: 0.8rem;
              font-weight: 700;
              box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
            }
          }

          .status-icon {
            font-size: 2rem;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(23, 162, 184, 0.1);

            &.status-success {
              background: rgba(40, 167, 69, 0.1);
            }

            &.status-warning {
              background: rgba(255, 193, 7, 0.1);
            }

            &.status-danger {
              background: rgba(220, 53, 69, 0.1);
            }
          }
        }

        .card-body {
          padding: 2rem 1.5rem;
          text-align: center;

          .component-type {
            color: #6c757d;
            font-size: 1rem;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
          }

          .quantity {
            margin-bottom: 1.5rem;

            .quantity-number {
              font-size: 3rem;
              font-weight: 800;
              background: linear-gradient(45deg, #17a2b8, #20c997);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              display: block;
            }

            .quantity-unit {
              color: #6c757d;
              font-size: 1rem;
              margin-top: 0.5rem;
              font-weight: 600;
            }
          }

          .status-text {
            padding: 0.75rem 1.5rem;
            border-radius: 15px;
            font-weight: 700;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.status-success {
              background: linear-gradient(135deg, #d4edda, #c3e6cb);
              color: #155724;
              border: 1px solid #c3e6cb;
            }

            &.status-warning {
              background: linear-gradient(135deg, #fff3cd, #ffeaa7);
              color: #856404;
              border: 1px solid #ffeaa7;
            }

            &.status-danger {
              background: linear-gradient(135deg, #f8d7da, #f5c6cb);
              color: #721c24;
              border: 1px solid #f5c6cb;
            }
          }
        }

        .card-footer {
          padding: 1.5rem;
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
          display: flex;
          justify-content: space-between;
          align-items: center;

          .location {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 600;
          }

          .expiry {
            padding: 0.5rem 0.75rem;
            border-radius: 10px;
            font-size: 0.8rem;
            font-weight: 700;

            &.expiry-normal {
              background: linear-gradient(135deg, #d4edda, #c3e6cb);
              color: #155724;
            }

            &.expiry-warning {
              background: linear-gradient(135deg, #fff3cd, #ffeaa7);
              color: #856404;
            }

            &.expiry-critical {
              background: linear-gradient(135deg, #f8d7da, #f5c6cb);
              color: #721c24;
            }
          }
        }
      }
    }

    .inventory-table-container {
      background: white;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      border: 1px solid rgba(23, 162, 184, 0.1);
      margin-bottom: 3rem;

      h2 {
        padding: 2rem;
        margin: 0;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        background: linear-gradient(45deg, #17a2b8, #20c997);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 1.8rem;
        font-weight: 700;
        border-bottom: 1px solid #e9ecef;
        text-align: center;
      }

      .inventory-table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
          padding: 1.5rem 1rem;
          text-align: left;
          border-bottom: 1px solid #f8f9fa;
        }

        th {
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
          font-weight: 700;
          color: #495057;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 1px;
          position: sticky;
          top: 0;
          z-index: 10;
        }

        td {
          font-size: 0.95rem;
          font-weight: 500;
        }

        tr {
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            transform: scale(1.01);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }
        }

        .blood-type-badge {
          display: inline-block;
          padding: 6px 25px;
          border-radius: 20px;
          font-weight: 700;
          font-size: 20px;
          text-align: center;
          min-height: 40px;
          min-width: 50px;
          // Default style (fallback)
          background: linear-gradient(45deg, #17a2b8, #20c997);
          color: white;
          box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        }

        .blood-type-badge.positive {
          background-color: vars.$background-box;
          color: vars.$primary-color;
          border: 1px solid #bbdefb;
        }

        .blood-type-badge.negative {
          background-color: vars.$background-box-secondary;
          color: vars.$secondary-color;
          border: 1px solid #ffcdd2;
        }

        .rare-indicator {
          background: linear-gradient(45deg, #6f42c1, #e83e8c);
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: 8px;
          font-size: 0.7rem;
          font-weight: 700;
          margin-left: 0.5rem;
        }

        .quantity-display {
          background: linear-gradient(45deg, #28a745, #20c997);
          color: white;
          padding: 0.5rem 0.75rem;
          border-radius: 10px;
          font-weight: 700;
          box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .status-badge {
          padding: 0.5rem 0.75rem;
          border-radius: 10px;
          font-weight: 700;
          font-size: 0.85rem;
          text-transform: uppercase;

          &.status-success {
            background: linear-gradient(45deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
          }

          &.status-warning {
            background: linear-gradient(45deg, #fff3cd, #ffeaa7);
            color: #856404;
            border: 1px solid #ffeaa7;
          }

          &.status-danger {
            background: linear-gradient(45deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
          }
        }

        .expiry-info {
          .expiry-normal {
            color: #28a745;
            font-weight: 600;
          }

          .expiry-warning {
            color: #ffc107;
            font-weight: 600;
          }

          .expiry-critical {
            color: #dc3545;
            font-weight: 600;
          }

          small {
            color: #6c757d;
            font-style: italic;
          }
        }
      }
    }

    .blood-type-summary {
      h2 {
        background: linear-gradient(45deg, #17a2b8, #20c997);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 2rem;
        font-size: 1.8rem;
        font-weight: 700;
        text-align: center;
      }

      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;

        .summary-card {
          background: white;
          padding: 2rem;
          border-radius: 20px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          text-align: center;
          transition: all 0.3s ease;
          border: 1px solid rgba(23, 162, 184, 0.1);
          position: relative;
          overflow: hidden;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(45deg, #17a2b8, #20c997);
          }

          &.low-stock::before {
            background: linear-gradient(45deg, #dc3545, #c82333);
          }

          &:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
          }

          .summary-header {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;

            .blood-type-large {
              background: linear-gradient(45deg, #17a2b8, #20c997);
              color: white;
              padding: 1rem 1.5rem;
              border-radius: 15px;
              font-weight: 700;
              font-size: 1.5rem;
              box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
            }

            .rare-badge {
              background: linear-gradient(45deg, #6f42c1, #e83e8c);
              color: white;
              padding: 0.5rem 0.75rem;
              border-radius: 10px;
              font-size: 0.8rem;
              font-weight: 700;
            }
          }

          .summary-body {
            .total-quantity {
              font-size: 2.5rem;
              font-weight: 800;
              background: linear-gradient(45deg, #17a2b8, #20c997);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              margin-bottom: 0.5rem;
            }

            .item-count {
              color: #6c757d;
              font-size: 1rem;
              font-weight: 600;
              margin-bottom: 1rem;
            }

            .warning-text {
              background: linear-gradient(135deg, #f8d7da, #f5c6cb);
              color: #721c24;
              padding: 0.5rem 1rem;
              border-radius: 10px;
              font-size: 0.9rem;
              font-weight: 700;
              border: 1px solid #f5c6cb;
            }
          }
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .blood-inventory-view {
    .blood-inventory-view-content {
      margin-left: 0;
      padding: 1rem;

      .page-header {
        flex-direction: column;
        align-items: stretch;
        padding: 1.5rem;

        div h1 {
          font-size: 2rem;
        }

        .view-controls {
          justify-content: center;
        }
      }

      .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;

        .stat-card {
          padding: 1.5rem;
          gap: 1rem;

          .stat-icon {
            font-size: 2rem;
            width: 60px;
            height: 60px;
          }

          .stat-info .stat-number {
            font-size: 2rem;
          }
        }
      }

      .filters-section {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem;

        .filter-group select {
          min-width: 100%;
        }
      }

      .inventory-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .inventory-table-container {
        overflow-x: auto;

        .inventory-table {
          min-width: 800px;
        }
      }

      .blood-type-summary .summary-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .summary-card {
          padding: 1.5rem;

          .summary-body .total-quantity {
            font-size: 2rem;
          }
        }
      }
    }
  }
}

// Blood type badge styles
.blood-type-badge {
  display: inline-block;
  padding: 6px 25px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 20px;
  text-align: center;
  min-height: 40px;
  min-width: 50px;

  &.positive {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
  }

  &.negative {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
  }
}

// Status badge styles (match manager)
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  border: 1px solid;
  white-space: nowrap;
}

// Rare badge styles
.rare-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 15px;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
  background-color: #e2d9f3;
  color: #6f42c1;
  border: 1px solid #d1c7e6;
  min-width: 60px;
}
