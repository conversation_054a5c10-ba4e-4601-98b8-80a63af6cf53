@use "../base/variables" as vars;
@use "../base/mixin" as mixins;

.blog-editor {
  max-width: 1200px;
  margin: 0 auto;

  .editor-header {
    @include mixins.flex-between;
    @include mixins.card-base;
    @include mixins.card-padding(vars.$spacing-8);
    margin-bottom: vars.$spacing-8;
    @include mixins.shadow-elevation(2);

    .header-content {
      h1 {
        @include mixins.heading(
          vars.$font-size-3xl,
          vars.$font-weight-bold,
          vars.$text-primary
        );
        @include mixins.text-gradient(vars.$accent-color, vars.$primary-color);
        margin-bottom: vars.$spacing-2;
      }

      p {
        @include mixins.text(vars.$font-size-lg, vars.$text-secondary);
        margin: 0;
      }
    }

    .header-actions {
      @include mixins.flex-align(center, center);
      gap: vars.$spacing-3;

      .btn-secondary {
        @include mixins.button-secondary;
        @include mixins.flex-align(center, center);
        gap: vars.$spacing-2;
      }

      .btn-outline {
        @include mixins.button-ghost(vars.$text-secondary);
        border: 1px solid vars.$border-medium;
        @include mixins.flex-align(center, center);
        gap: vars.$spacing-2;

        &:hover {
          background: vars.$background-section;
          border-color: vars.$primary-color;
          color: vars.$primary-color;
        }
      }

      .btn-primary {
        @include mixins.button-primary;
        @include mixins.flex-align(center, center);
        gap: vars.$spacing-2;
      }
    }
  }

  .editor-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: vars.$spacing-8;

    .editor-main {
      .form-group {
        margin-bottom: vars.$spacing-6;

        label {
          @include mixins.form-label;
          color: vars.$text-primary;
          font-weight: vars.$font-weight-semibold;
        }

        input,
        textarea,
        select {
          @include mixins.form-input;
          font-size: vars.$font-size-base;
        }

        textarea {
          resize: vertical;
          min-height: 80px;
        }

        small {
          @include mixins.text(vars.$font-size-xs, vars.$text-muted);
          margin-top: vars.$spacing-1;
          display: block;
        }
      }

      .content-editor {
        .editor-toolbar {
          @include mixins.flex-align(flex-start, center);
          gap: vars.$spacing-1;
          padding: vars.$spacing-3;
          background: vars.$background-section;
          border: 1px solid vars.$border-light;
          border-bottom: none;
          border-radius: vars.$border-radius-base vars.$border-radius-base 0 0;

          .toolbar-btn {
            @include mixins.button-ghost(vars.$text-secondary);
            padding: vars.$spacing-2;
            border-radius: vars.$border-radius-sm;
            font-size: vars.$font-size-sm;
            min-width: 32px;
            height: 32px;
            @include mixins.flex-center;

            &:hover {
              background: vars.$primary-light;
              color: vars.$primary-color;
            }

            &.active {
              background: vars.$primary-color;
              color: vars.$white;
            }
          }

          .toolbar-divider {
            width: 1px;
            height: 20px;
            background: vars.$border-medium;
            margin: 0 vars.$spacing-2;
          }
        }

        textarea {
          border-radius: 0 0 vars.$border-radius-base vars.$border-radius-base;
          border-top: none;
          font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
          font-size: vars.$font-size-sm;
          line-height: vars.$line-height-relaxed;
          min-height: 400px;
        }
      }
    }

    .editor-sidebar {
      .sidebar-section {
        @include mixins.card-base;
        @include mixins.card-padding(vars.$spacing-6);
        margin-bottom: vars.$spacing-6;

        h3 {
          @include mixins.heading(
            vars.$font-size-lg,
            vars.$font-weight-semibold,
            vars.$text-primary
          );
          margin-bottom: vars.$spacing-4;
          padding-bottom: vars.$spacing-3;
          border-bottom: 1px solid vars.$border-light;
        }

        .form-group {
          margin-bottom: vars.$spacing-4;

          &:last-child {
            margin-bottom: 0;
          }

          label {
            @include mixins.form-label;
            color: vars.$text-primary;
            font-weight: vars.$font-weight-medium;
          }

          input,
          select {
            @include mixins.form-input;
            font-size: vars.$font-size-sm;
          }

          small {
            @include mixins.text(vars.$font-size-xs, vars.$text-muted);
            margin-top: vars.$spacing-1;
            display: block;
          }
        }

        .image-upload {
          .upload-placeholder {
            @include mixins.flex-center;
            flex-direction: column;
            gap: vars.$spacing-2;
            padding: vars.$spacing-8;
            border: 2px dashed vars.$border-medium;
            border-radius: vars.$border-radius-base;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;

            &:hover {
              border-color: vars.$primary-color;
              background: vars.$primary-light;
            }

            i {
              font-size: vars.$font-size-3xl;
              color: vars.$text-muted;
            }

            span {
              @include mixins.text(
                vars.$font-size-base,
                vars.$text-secondary,
                vars.$font-weight-medium
              );
            }

            small {
              @include mixins.text(vars.$font-size-xs, vars.$text-muted);
              margin: 0;
            }
          }

          .image-preview {
            position: relative;
            border-radius: vars.$border-radius-base;
            overflow: hidden;
            @include mixins.shadow-elevation(1);

            img {
              width: 100%;
              height: 200px;
              object-fit: cover;
              display: block;
            }

            .remove-image {
              position: absolute;
              top: vars.$spacing-2;
              right: vars.$spacing-2;
              @include mixins.button-ghost(vars.$white);
              background: rgba(vars.$black, 0.7);
              border-radius: vars.$border-radius-full;
              width: 32px;
              height: 32px;
              @include mixins.flex-center;
              font-size: vars.$font-size-sm;

              &:hover {
                background: rgba(vars.$error-color, 0.9);
              }
            }
          }
        }
      }
    }
  }

  .loading-spinner {
    @include mixins.flex-center;
    flex-direction: column;
    padding: vars.$spacing-16;
    gap: vars.$spacing-4;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid vars.$border-light;
      border-top: 4px solid vars.$primary-color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    p {
      @include mixins.text(vars.$font-size-base, vars.$text-secondary);
      margin: 0;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // === RESPONSIVE DESIGN ===
  @include mixins.tablet {
    padding: vars.$spacing-4;

    .editor-header {
      flex-direction: column;
      gap: vars.$spacing-4;
      text-align: center;

      .header-actions {
        justify-content: center;
        flex-wrap: wrap;
      }
    }

    .editor-content {
      grid-template-columns: 1fr;
      gap: vars.$spacing-6;

      .editor-sidebar {
        order: -1;

        .sidebar-section {
          .image-upload .upload-placeholder {
            padding: vars.$spacing-6;
          }
        }
      }
    }
  }

  @include mixins.mobile {
    padding: vars.$spacing-3;

    .editor-header {
      padding: vars.$spacing-4;

      .header-content h1 {
        font-size: vars.$font-size-2xl;
      }

      .header-actions {
        gap: vars.$spacing-2;

        .btn-secondary,
        .btn-outline,
        .btn-primary {
          padding: vars.$spacing-2 vars.$spacing-3;
          font-size: vars.$font-size-sm;
        }
      }
    }

    .editor-content {
      gap: vars.$spacing-4;

      .editor-main {
        .content-editor {
          .editor-toolbar {
            flex-wrap: wrap;
            gap: vars.$spacing-1;
            padding: vars.$spacing-2;

            .toolbar-btn {
              min-width: 28px;
              height: 28px;
              font-size: vars.$font-size-xs;
            }
          }

          textarea {
            min-height: 300px;
            font-size: vars.$font-size-xs;
          }
        }
      }

      .editor-sidebar {
        .sidebar-section {
          padding: vars.$spacing-4;

          .image-upload .upload-placeholder {
            padding: vars.$spacing-4;

            i {
              font-size: vars.$font-size-2xl;
            }
          }
        }
      }
    }
  }
}
