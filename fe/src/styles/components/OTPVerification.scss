.otp-verification {
    &__container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: #f5f5f5;
    }

    &__box {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 400px;
    }

    &__title {
        font-size: 1.5rem;
        font-weight: bold;
        text-align: center;
        margin-bottom: 2rem;
        color: #333;
    }

    &__form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    &__label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    &__input {
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        transition: border-color 0.2s;

        &:focus {
            outline: none;
            border-color: #007bff;
        }
    }

    &__error {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        margin-bottom: 0.5rem;
    }

    &__submit {
        background-color: #007bff;
        color: white;
        padding: 0.75rem;
        border: none;
        border-radius: 4px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
            background-color: #0056b3;
        }

        &:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    }
}