import React, { useState } from "react";
import { getPasswordValidationError, validatePasswordConfirmation } from "../../utils/validation";
import "../../styles/components/ChangePasswordForm.scss";
import authService from "../../services/authService";
import { FaEye, FaEyeSlash } from "react-icons/fa";

export default function ChangePasswordForm({ onSuccess, onCancel }) {
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false,
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const togglePasswordVisibility = (fieldName) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate current password
    if (!formData.currentPassword) {
      newErrors.currentPassword = "Vui lòng nhập mật khẩu hiện tại";
    }

    // Validate new password
    const passwordError = getPasswordValidationError(formData.newPassword);
    if (passwordError) {
      newErrors.newPassword = passwordError;
    }

    // Check if new password is different from current password
    if (formData.currentPassword && formData.newPassword &&
      formData.currentPassword === formData.newPassword) {
      newErrors.newPassword = "Mật khẩu mới phải khác mật khẩu hiện tại";
    }

    // Validate password confirmation
    const confirmPasswordError = validatePasswordConfirmation(
      formData.newPassword,
      formData.confirmPassword
    );
    if (confirmPasswordError) {
      newErrors.confirmPassword = confirmPasswordError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});
    setSuccessMessage("");

    try {
      const changePasswordData = {
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      };

      const result = await authService.changePassword(changePasswordData);

      if (result.success) {
        setSuccessMessage("Mật khẩu đã được thay đổi thành công!");
        // Reset form
        setFormData({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        });

        // Call success callback if provided
        if (onSuccess) {
          setTimeout(() => onSuccess(), 1500);
        }
      } else {
        setErrors({
          submit: result.error || "Không thể thay đổi mật khẩu. Vui lòng thử lại."
        });
      }
    } catch (error) {
      console.error("Change password failed:", error);
      setErrors({
        submit: "Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau."
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="change-password-form__container">
      <div className="change-password-form__header">
        <h2 className="change-password-form__title">Đổi mật khẩu</h2>
        <p className="change-password-form__description">
          Để bảo mật tài khoản, vui lòng nhập mật khẩu hiện tại và mật khẩu mới.
        </p>
      </div>

      <form className="change-password-form__form" onSubmit={handleSubmit}>
        <div className="form-group">
          <label className="change-password-form__label">MẬT KHẨU HIỆN TẠI</label>
          <div className="password-input-container">
            <input
              className={`change-password-form__input ${errors.currentPassword ? "error" : ""}`}
              type={showPasswords.currentPassword ? "text" : "password"}
              name="currentPassword"
              value={formData.currentPassword}
              onChange={handleInputChange}
              placeholder="Nhập mật khẩu hiện tại"
              required
            />
            <button
              type="button"
              className="password-toggle-btn"
              onClick={() => togglePasswordVisibility('currentPassword')}
            >
              {showPasswords.currentPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>
          {errors.currentPassword && (
            <div className="change-password-form__error">{errors.currentPassword}</div>
          )}
        </div>

        <div className="form-group">
          <label className="change-password-form__label">MẬT KHẨU MỚI</label>
          <div className="password-input-container">
            <input
              className={`change-password-form__input ${errors.newPassword ? "error" : ""}`}
              type={showPasswords.newPassword ? "text" : "password"}
              name="newPassword"
              value={formData.newPassword}
              onChange={handleInputChange}
              placeholder="Tối thiểu 6 ký tự bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt"
              required
            />
            <button
              type="button"
              className="password-toggle-btn"
              onClick={() => togglePasswordVisibility('newPassword')}
            >
              {showPasswords.newPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>
          {errors.newPassword && (
            <div className="change-password-form__error">{errors.newPassword}</div>
          )}
        </div>

        <div className="form-group">
          <label className="change-password-form__label">XÁC NHẬN MẬT KHẨU MỚI</label>
          <div className="password-input-container">
            <input
              className={`change-password-form__input ${errors.confirmPassword ? "error" : ""}`}
              type={showPasswords.confirmPassword ? "text" : "password"}
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="Nhập lại mật khẩu mới để xác nhận"
              required
            />
            <button
              type="button"
              className="password-toggle-btn"
              onClick={() => togglePasswordVisibility('confirmPassword')}
            >
              {showPasswords.confirmPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>
          {errors.confirmPassword && (
            <div className="change-password-form__error">{errors.confirmPassword}</div>
          )}
        </div>

        {successMessage && (
          <div className="change-password-form__success">{successMessage}</div>
        )}
        {errors.submit && (
          <div className="change-password-form__error">{errors.submit}</div>
        )}

        <div className="change-password-form__actions">
          <button
            type="button"
            className="change-password-form__cancel"
            onClick={onCancel}
            disabled={isLoading}
          >
            Hủy
          </button>
          <button
            className="change-password-form__submit"
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? "ĐANG ĐỔI..." : "ĐỔI MẬT KHẨU"}
          </button>
        </div>
      </form>
    </div>
  );
}
