import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Steps, Tag, Row, Col, Divider } from "antd";
import {
  UserOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  HeartOutlined,
} from "@ant-design/icons";

const { Step } = Steps;

// Donation process steps (1-5)
const DONATION_PROCESS = {
  REGISTERED: 1,           // Đăng ký
  HEALTH_CHECKED: 2,       // Khám sức khỏe cơ bản
  BLOOD_TAKEN: 3,          // Lấy máu
  BLOOD_TESTED: 4,         // Xét nghiệm máu
  STORED: 5,               // Nhập kho
};

// Legacy statuses for backward compatibility
const DONATION_STATUSES = {
  REGISTERED: "registered",
  HEALTH_CHECKED: "health_checked",
  BLOOD_TAKEN: "blood_taken",
  BLOOD_TESTED: "blood_tested",
  STORED: "stored",
  NOT_ELIGIBLE: "not_eligible"
};

const ProcessWorkflowModal = ({
  visible,
  onCancel,
  selectedItem,
  onStoreBlood,
  isManager = false,
  title = "Quy trình hiến máu",
}) => {
  // Get status info for display based on process number (1-5) and status boolean
  const getStatusInfo = (item) => {
    const process = item.process || 1;
    const status = item.status;

    // If rejected (status = false), show rejection
    if (status === false) {
      return {
        text: "Không chấp nhận",
        color: "#ff4d4f",
        icon: <ExclamationCircleOutlined />,
        step: -1,
      };
    }

    // Map process steps to display info
    const processMap = {
      1: {
        text: "Đăng ký",
        color: "#1890ff",
        icon: <UserOutlined />,
        step: 0,
      },
      2: {
        text: "Khám sức khỏe cơ bản",
        color: "#fa8c16",
        icon: <CheckCircleOutlined />,
        step: 1,
      },
      3: {
        text: "Lấy máu",
        color: "#722ed1",
        icon: <HeartOutlined />,
        step: 2,
      },
      4: {
        text: "Xét nghiệm máu",
        color: "#13c2c2",
        icon: <ClockCircleOutlined />,
        step: 3,
      },
      5: {
        text: "Nhập kho",
        color: "#52c41a",
        icon: <CheckCircleOutlined />,
        step: 4,
      },
    };

    return processMap[process] || processMap[1];
  };

  // Get donation process steps
  const getDonationSteps = () => [
    {
      title: "Đăng ký",
      description: "Đăng ký hiến máu",
      icon: <UserOutlined />,
    },
    {
      title: "Khám sức khỏe cơ bản",
      description: "Kiểm tra sức khỏe và đánh giá điều kiện",
      icon: <CheckCircleOutlined />,
    },
    {
      title: "Lấy máu",
      description: "Thực hiện lấy máu hiến tặng",
      icon: <HeartOutlined />,
    },
    {
      title: "Xét nghiệm máu",
      description: "Kiểm tra chất lượng và an toàn máu",
      icon: <ClockCircleOutlined />,
    },
    {
      title: "Nhập kho",
      description: "Lưu trữ máu vào kho bảo quản",
      icon: <CheckCircleOutlined />,
    },
  ];

  const handleStoreBlood = () => {
    if (onStoreBlood && selectedItem) {
      onStoreBlood(selectedItem.id);
      onCancel();
    }
  };

  if (!selectedItem) return null;

  // Get the name field - could be donorName or name depending on the data structure
  const itemName = selectedItem.donorName || selectedItem.name;
  const statusInfo = getStatusInfo(selectedItem);

  return (
    <Modal
      title={`${title}: ${itemName}`}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="close" onClick={onCancel}>
          Đóng
        </Button>,
        selectedItem.process === 4 && selectedItem.status === true && (
          <Button key="store" type="primary" onClick={handleStoreBlood}>
            Nhập kho
          </Button>
        ),
      ]}
      width={800}
    >
      <div className="process-workflow">
        <div className="donor-summary">
          <Row gutter={16}>
            <Col span={8}>
              <div className="summary-item">
                <strong>Người hiến:</strong> {itemName}
              </div>
            </Col>
            <Col span={8}>
              <div className="summary-item">
                <strong>Nhóm máu:</strong>
                <Tag color="#D93E4C" style={{ marginLeft: 8 }}>
                  {selectedItem.bloodType}
                </Tag>
              </div>
            </Col>
            <Col span={8}>
              <div className="summary-item">
                <strong>Ngày hẹn:</strong>{" "}
                {selectedItem.appointmentDate
                  ? new Date(selectedItem.appointmentDate).toLocaleDateString(
                    "vi-VN"
                  )
                  : selectedItem.registrationDate
                    ? new Date(selectedItem.registrationDate).toLocaleDateString(
                      "vi-VN"
                    )
                    : "Chưa có"}
              </div>
            </Col>
          </Row>
        </div>

        <Divider />

        <div className="workflow-steps">
          {selectedItem.status === false ? (
            // Show rejection status
            <div className="rejection-status">
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <ExclamationCircleOutlined style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: '16px' }} />
                <h3 style={{ color: '#ff4d4f' }}>Không chấp nhận</h3>
                {selectedItem.notes && (
                  <p style={{ color: '#666', marginTop: '8px' }}>
                    <strong>Lý do:</strong> {selectedItem.notes}
                  </p>
                )}
              </div>
            </div>
          ) : (
            // Show normal process steps
            <Steps
              current={statusInfo.step}
              status="process"
              direction="vertical"
              size="small"
            >
              {getDonationSteps().map((step, index) => (
                <Step
                  key={index}
                  title={step.title}
                  description={step.description}
                  icon={step.icon}
                />
              ))}
            </Steps>
          )}
        </div>

        {selectedItem.process === 4 && selectedItem.status === true && (
          <div className="action-section">
            <Divider />
            <div className="action-info">
              <h4>Hành động tiếp theo</h4>
              <p>
                Máu đã được xét nghiệm và đạt tiêu chuẩn chất lượng. Bạn có thể nhập
                vào kho máu để bảo quản.
              </p>
            </div>
          </div>
        )}

        {selectedItem.notes && (
          <div className="notes-section">
            <Divider />
            <h4>Ghi chú</h4>
            <p>{selectedItem.notes}</p>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ProcessWorkflowModal;
export { DONATION_STATUSES, DONATION_PROCESS };
