import vietnamProvinces from '../data/vietnam-provinces.json';

/**
 * Service for managing Vietnam address data from local JSON file
 */
class VietnamAddressService {
  constructor() {
    this.data = vietnamProvinces;
    this.cache = {
      provinces: null,
      wards: new Map(), // Cache wards by province name
    };
  }

  /**
   * Fetch all provinces from local JSON file
   * @returns {Promise<Array>} List of province names
   */
  async getProvinces() {
    // Return cached data if available
    if (this.cache.provinces) {
      return this.cache.provinces;
    }

    try {
      if (this.data && this.data.success && Array.isArray(this.data.data)) {
        const provinces = this.data.data.map(item => item.province);
        this.cache.provinces = provinces; // Cache the result
        return provinces;
      } else {
        throw new Error('Invalid province data format');
      }
    } catch (error) {
      console.error('Failed to load province data:', error);
      throw new Error('Không thể tải dữ liệu tỉnh thành');
    }
  }

  /**
   * Fetch wards for a specific province from local JSON file
   * @param {string} provinceName - The province name
   * @returns {Promise<Array>} List of ward names
   */
  async getWardsByProvince(provinceName) {
    if (!provinceName) {
      return [];
    }

    // Return cached data if available
    if (this.cache.wards.has(provinceName)) {
      return this.cache.wards.get(provinceName);
    }

    try {
      if (this.data && this.data.success && Array.isArray(this.data.data)) {
        const provinceData = this.data.data.find(item => item.province === provinceName);
        if (provinceData && Array.isArray(provinceData.wards)) {
          const wards = provinceData.wards.map(ward => ward.name);
          this.cache.wards.set(provinceName, wards); // Cache the result
          return wards;
        } else {
          throw new Error('Province not found or invalid ward data');
        }
      } else {
        throw new Error('Invalid data format');
      }
    } catch (error) {
      console.error('Failed to load ward data:', error);
      throw new Error('Không thể tải dữ liệu phường/xã');
    }
  }

  /**
   * Clear cache (useful for refreshing data)
   */
  clearCache() {
    this.cache.provinces = null;
    this.cache.wards.clear();
  }

  /**
   * Get cached provinces without making API call
   * @returns {Array|null} Cached provinces or null if not cached
   */
  getCachedProvinces() {
    return this.cache.provinces;
  }

  /**
   * Get cached wards for a province without making API call
   * @param {string} provinceName - The province name
   * @returns {Array|null} Cached wards or null if not cached
   */
  getCachedWards(provinceName) {
    return this.cache.wards.get(provinceName) || null;
  }

  /**
   * Preload all data (provinces and wards) for better performance
   * @returns {Promise<void>}
   */
  async preloadAllData() {
    try {
      if (this.data && this.data.success && Array.isArray(this.data.data)) {
        // Cache provinces
        const provinces = this.data.data.map(item => item.province);
        this.cache.provinces = provinces;

        // Cache all wards
        this.data.data.forEach(provinceData => {
          if (provinceData.wards && Array.isArray(provinceData.wards)) {
            const wards = provinceData.wards.map(ward => ward.name);
            this.cache.wards.set(provinceData.province, wards);
          }
        });

        console.log('Vietnam address data preloaded successfully');
      } else {
        throw new Error('Invalid data format');
      }
    } catch (error) {
      console.error('Failed to preload Vietnam address data:', error);
      throw error;
    }
  }
}

// Export singleton instance
export default new VietnamAddressService();
