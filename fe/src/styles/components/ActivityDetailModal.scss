@use "../../styles/base/variables" as vars;

// ActivityDetailModal Component Styles
.detail-modal-content {
  .detail-header-card {
    .ant-card-body {
      padding: 16px;
    }
  }

  .detail-sections {
    .ant-card {
      .ant-card-head {
        background: linear-gradient(135deg, vars.$background-section, vars.$border-light);
        border-bottom: 1px solid vars.$border-light;
        
        .ant-card-head-title {
          font-weight: 600;
          color: vars.$text-primary;
        }
      }

      .ant-card-body {
        padding: 16px;
      }
    }

    .detail-item {
      margin-bottom: 8px;

      .ant-typography {
        margin-bottom: 4px;
      }
    }
  }
}

// Modal header styles với base colors
.detail-modal {
  border-radius: 16px;

  .detail-modal-icon {
    &.donation {
      color: vars.$secondary-color;
    }

    &.request {
      color: vars.$primary-color;
    }
  }

  .detail-modal-title {
    font-size: 18px;
    color: vars.$text-primary;
  }

  .detail-modal-close-button {
    border-radius: 8px;
    font-weight: 500;
  }
}

// Modal responsive styles
@media (max-width: 768px) {
  .detail-modal-content {
    .detail-sections {
      .ant-col-12 {
        width: 100% !important;
        max-width: 100% !important;
      }

      .ant-col-8 {
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 12px;
      }
    }
  }
}
