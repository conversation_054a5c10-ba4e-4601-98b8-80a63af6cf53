import React, { useState, useEffect, useRef } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import authService from "../../services/authService";
import NotificationService from "../../services/notificationService";
import useNotifications from "../../hooks/useNotifications";
import { getUserName } from "../../utils/userUtils";
import "../../styles/components/MemberNavbar.scss";

const navItems = [
  { path: "/member", label: "Trang chủ" },
  { path: "/member/blood-info", label: "<PERSON><PERSON><PERSON> li<PERSON>u" },
  { path: "/member/blog", label: "Tin tức" },
  { path: "/member/donation-guide", label: "Hướng dẫn hiến máu" },
];

const MemberNavbar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [showMenu, setShowMenu] = useState(false);
  const [showMobileNav, setShowMobileNav] = useState(false);

  // Use notifications hook for real-time updates
  const { unreadCount } = useNotifications();
  const dropdownRef = useRef(null);
  const mobileNavRef = useRef(null);

  const userName = getUserName();
  const currentUser = authService.getCurrentUser();

  // unreadCount is now managed by useNotifications hook

  // Close dropdown and mobile nav when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowMenu(false);
      }
      if (mobileNavRef.current && !mobileNavRef.current.contains(event.target)) {
        setShowMobileNav(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      await authService.logout();
      navigate("/");
    } catch (error) {
      console.error("Logout error:", error);
      // Still navigate even if logout API fails
      navigate("/");
    }
  };

  const handleMenuItemClick = () => {
    setShowMenu(false);
  };

  const handleMobileNavItemClick = () => {
    setShowMobileNav(false);
  };

  const toggleMobileNav = () => {
    setShowMobileNav(prev => !prev);
  };

  return (
    <header className="navbar member-navbar">
      <div className="navbar-logo">
        <Link to="/member">Blood Donation</Link>
      </div>

      {/* Desktop Navigation */}
      <nav className="navbar-nav desktop-nav">
        {navItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={location.pathname === item.path ? "active" : ""}
          >
            {item.label}
          </Link>
        ))}
      </nav>

      {/* Mobile Hamburger Button */}
      <button
        className="mobile-menu-toggle"
        onClick={toggleMobileNav}
        aria-label="Toggle mobile menu"
      >
        <span className={`hamburger-line ${showMobileNav ? 'active' : ''}`}></span>
        <span className={`hamburger-line ${showMobileNav ? 'active' : ''}`}></span>
        <span className={`hamburger-line ${showMobileNav ? 'active' : ''}`}></span>
      </button>
      {/* Desktop User Actions */}
      <div className="navbar-actions desktop-actions" ref={dropdownRef}>
        <div className="user-info">
          <span className="user-name">
            {userName}
          </span>
          <div
            className="member-avatar-wrapper"
            onClick={() => setShowMenu((prev) => !prev)}
          >
            {userName.charAt(0).toUpperCase()}
            {unreadCount > 0 && (
              <span className="avatar-notification-dot"></span>
            )}
          </div>
        </div>
        {showMenu && (
          <div className="member-dropdown-menu">
            <Link to="/member/activity-history" className="dropdown-item" onClick={handleMenuItemClick}>
              Lịch sử hoạt động
            </Link>
            <Link to="/member/notifications" className="dropdown-item" onClick={handleMenuItemClick}>
              <span>Thông báo cá nhân</span>
              {unreadCount > 0 && (
                <span className="notification-badge">{unreadCount > 99 ? '99+' : unreadCount}</span>
              )}
            </Link>
            <Link to="/member/profile" className="dropdown-item" onClick={handleMenuItemClick}>
              Hồ sơ cá nhân
            </Link>
            <Link to="/member/change-password" className="dropdown-item" onClick={handleMenuItemClick}>
              Đổi mật khẩu
            </Link>

            <div className="dropdown-divider"></div>

            <button onClick={handleLogout} className="dropdown-item logout-btn">
              Đăng xuất
            </button>
          </div>
        )}
      </div>

      {/* Mobile Navigation Menu */}
      <div
        className={`mobile-nav-overlay ${showMobileNav ? 'active' : ''}`}
        ref={mobileNavRef}
      >
        <nav className="mobile-nav">
          <div className="mobile-nav-header">
            <div className="mobile-user-info">
              <div className="mobile-avatar">
                {userName.charAt(0).toUpperCase()}
                {unreadCount > 0 && (
                  <span className="avatar-notification-dot"></span>
                )}
              </div>
              <span className="mobile-user-name">{userName}</span>
            </div>
          </div>

          <div className="mobile-nav-items">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`mobile-nav-item ${location.pathname === item.path ? "active" : ""}`}
                onClick={handleMobileNavItemClick}
              >
                {item.label}
              </Link>
            ))}

            <div className="mobile-nav-divider"></div>

            <Link to="/member/activity-history" className="mobile-nav-item" onClick={handleMobileNavItemClick}>
              Lịch sử hoạt động
            </Link>
            <Link to="/member/notifications" className="mobile-nav-item" onClick={handleMobileNavItemClick}>
              <span>Thông báo cá nhân</span>
              {unreadCount > 0 && (
                <span className="notification-badge">{unreadCount > 99 ? '99+' : unreadCount}</span>
              )}
            </Link>
            <Link to="/member/profile" className="mobile-nav-item" onClick={handleMobileNavItemClick}>
              Hồ sơ cá nhân
            </Link>
            <Link to="/member/change-password" className="mobile-nav-item" onClick={handleMobileNavItemClick}>
              Đổi mật khẩu
            </Link>

            <div className="mobile-nav-divider"></div>

            <button onClick={handleLogout} className="mobile-nav-item logout-btn">
              Đăng xuất
            </button>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default MemberNavbar;
