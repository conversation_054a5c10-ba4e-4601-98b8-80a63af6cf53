.charts-section {
  margin-bottom: 16px;

  .ant-row {
    margin: 0 -8px !important;

    .ant-col {
      padding: 0 8px !important;
    }
  }

  .chart-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    height: 100%;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      padding: 12px 16px;
      min-height: auto;

      .ant-card-head-title {
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        padding: 0;
      }
    }

    .ant-card-body {
      padding: 16px;
      height: calc(100% - 41px);
      display: flex;
      flex-direction: column;

      > div {
        flex: 1;
        min-height: 250px;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 1200px) {
    .chart-card {
      .ant-card-body {
        > div {
          min-height: 200px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .chart-card {
      .ant-card-body {
        > div {
          min-height: 180px;
        }
      }
    }
  }
}
